// Figma Library - Unified interface for Figma plugin communications
// Export all public APIs

export {
  FigmaClient,
  getFigmaClient,
  resetFigmaClient
} from './client'

export {
  MockFigmaClient
} from './mock-client'

// Export all types and constants
export {
  type WindowSettings,
  type FigmaClientEvents,
  type StorageInterface,
  type WindowInterface,
  type SelectionInterface,
  type CommandsInterface,
  type UIMessage,
  type PluginMessage,
  type CancelMessage,
  type SetWindowSizeMessage,
  type GetFigmaSelectionMessage,
  type FigmaSelectionResponse,
  type StorageGetMessage,
  type StorageSetMessage,
  type StorageDeleteMessage,
  type StorageResponseMessage,
  type FigmaEnvironment,
  type EnvironmentInfo,
  DEFAULT_WINDOW_SETTINGS,
  STORAGE_KEYS,
  MESSAGE_TYPES
} from './types'

// Export mock data generators for development
export {
  generateMockSelection,
  generateEmptySelection,
  generateMockWindowSettings,
  type MockFigmaNode,
  type MockPaint
} from './mock-data'

// Note: storage_test.ts has been removed as it's no longer needed
