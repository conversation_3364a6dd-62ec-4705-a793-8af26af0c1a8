import React, { useEffect, useState } from 'react'
import { AiLoadingState } from '../types/ui_types'

interface InlineLoadingIndicatorProps {
  loadingState: AiLoadingState
  className?: string
  position?: 'start' | 'inline' | 'end' // Dynamic positioning
}

export const InlineLoadingIndicator: React.FC<InlineLoadingIndicatorProps> = ({
  loadingState,
  className = '',
  position = 'start'
}) => {
  const [dots, setDots] = useState('')
  const [elapsedTime, setElapsedTime] = useState(0)
  const [isTimedOut, setIsTimedOut] = useState(false)

  // Timeout handling
  useEffect(() => {
    if (!loadingState.isActive || loadingState.isTimedOut) {
      setIsTimedOut(loadingState.isTimedOut || false)
      return
    }

    const timeoutMs = loadingState.timeoutMs || 60000 // Default 60 seconds
    const timeoutId = setTimeout(() => {
      setIsTimedOut(true)
    }, timeoutMs)

    return () => clearTimeout(timeoutId)
  }, [loadingState.isActive, loadingState.startTime, loadingState.timeoutMs, loadingState.isTimedOut])

  // Animated ellipsis effect
  useEffect(() => {
    if (!loadingState.isActive || isTimedOut) {
      setDots('')
      return
    }

    const interval = setInterval(() => {
      setDots(prev => {
        if (prev === '...') return ''
        return prev + '.'
      })
    }, 500)

    return () => clearInterval(interval)
  }, [loadingState.isActive, isTimedOut])

  // Elapsed time tracking
  useEffect(() => {
    if (!loadingState.isActive || !loadingState.startTime) {
      setElapsedTime(0)
      return
    }

    const interval = setInterval(() => {
      // Ensure startTime is a Date object (handle JSON deserialization)
      const startTime = loadingState.startTime instanceof Date
        ? loadingState.startTime
        : new Date(loadingState.startTime)

      const elapsed = Math.floor((Date.now() - startTime.getTime()) / 1000)
      setElapsedTime(elapsed)
    }, 1000)

    return () => clearInterval(interval)
  }, [loadingState.isActive, loadingState.startTime])

  if (!loadingState.isActive && !isTimedOut) {
    return null
  }

  const getPhaseIcon = () => {
    if (isTimedOut || loadingState.phase === 'timeout') {
      return '⏰'
    }

    switch (loadingState.phase) {
      case 'initializing':
        return '🚀'
      case 'thinking':
        return '🤔'
      case 'tool_execution':
        return '🔧'
      case 'analyzing':
        return '📊'
      case 'completing':
        return '✨'
      default:
        return '⚡'
    }
  }

  const getStatusMessage = () => {
    if (isTimedOut || loadingState.phase === 'timeout') {
      return 'Operation timed out'
    }
    return loadingState.statusMessage
  }

  const formatElapsedTime = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}m ${remainingSeconds}s`
  }

  return (
    <div className={`inline-loading-indicator position-${position} ${isTimedOut ? 'timeout' : ''} ${className}`}>
      {/* Compact Single-Line Layout */}
      <div className="status-line">
        <span className="phase-icon">{getPhaseIcon()}</span>
        <span className="status-text">
          {getStatusMessage()}{!isTimedOut ? dots : ''}
        </span>

        {/* Tool Execution Badge - Inline */}
        {loadingState.currentTool && (
          <span className="tool-badge">
            {loadingState.currentTool}
          </span>
        )}

        {/* Tools Count - Inline */}
        {loadingState.toolsExecuted > 0 && (
          <span className="tools-count">
            {loadingState.toolsExecuted} tool{loadingState.toolsExecuted !== 1 ? 's' : ''}
          </span>
        )}

        {/* Timestamp on the right */}
        {elapsedTime > 0 && (
          <span className="elapsed-time">
            {formatElapsedTime(elapsedTime)}
          </span>
        )}
      </div>

      {/* Compact Progress Bar */}
      <div className="progress-bar">
        <div className="progress-fill" />
      </div>

      <style jsx>{`
        .inline-loading-indicator {
          background: rgba(59, 130, 246, 0.04);
          border: 1px solid rgba(59, 130, 246, 0.15);
          border-radius: 6px;
          padding: 6px 8px;
          margin: 4px 0;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          animation: fadeIn 0.3s ease-in-out;
        }

        .inline-loading-indicator.timeout {
          background: rgba(239, 68, 68, 0.04);
          border-color: rgba(239, 68, 68, 0.15);
        }

        .timeout .phase-icon {
          animation: none;
        }

        .timeout .status-text {
          color: #dc2626;
        }

        .timeout .progress-fill {
          background: linear-gradient(90deg, #dc2626, #ef4444);
          animation: none;
          width: 100%;
        }

        /* Dynamic positioning styles */
        .position-start {
          margin-bottom: 6px;
        }

        .position-inline {
          margin: 3px 0;
          background: rgba(59, 130, 246, 0.03);
          border-color: rgba(59, 130, 246, 0.12);
          border-left: 2px solid rgba(59, 130, 246, 0.3);
          padding-left: 10px;
        }

        .position-end {
          margin-top: 6px;
        }

        .status-line {
          display: flex;
          align-items: center;
          gap: 6px;
          margin-bottom: 3px;
        }

        .phase-icon {
          font-size: 11px;
          animation: pulse 2s infinite;
          flex-shrink: 0;
        }

        .status-text {
          font-size: 10px;
          color: #374151;
          font-weight: 500;
          flex: 1;
          min-width: 0;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .tool-badge {
          font-size: 8px;
          background: #3b82f6;
          color: white;
          padding: 1px 4px;
          border-radius: 6px;
          font-weight: 600;
          flex-shrink: 0;
        }

        .tools-count {
          font-size: 8px;
          background: rgba(59, 130, 246, 0.1);
          color: #6b7280;
          padding: 1px 3px;
          border-radius: 5px;
          font-weight: 500;
          flex-shrink: 0;
        }

        .elapsed-time {
          font-size: 8px;
          color: #6b7280;
          font-weight: 500;
          margin-left: auto;
          flex-shrink: 0;
        }

        .progress-bar {
          height: 2px;
          background: rgba(59, 130, 246, 0.15);
          border-radius: 1px;
          overflow: hidden;
        }

        .progress-fill {
          height: 100%;
          width: 25%;
          background: linear-gradient(90deg, #3b82f6, #60a5fa);
          border-radius: 1px;
          animation: indeterminate 2s ease-in-out infinite;
        }

        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(-2px); }
          to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.05); }
        }

        @keyframes indeterminate {
          0% { transform: translateX(-100%); }
          50% { transform: translateX(0%); }
          100% { transform: translateX(300%); }
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
          .inline-loading-indicator {
            background: rgba(59, 130, 246, 0.08);
            border-color: rgba(59, 130, 246, 0.25);
          }

          .position-inline {
            background: rgba(59, 130, 246, 0.06);
            border-color: rgba(59, 130, 246, 0.2);
            border-left-color: rgba(59, 130, 246, 0.4);
          }

          .status-text {
            color: #e5e7eb;
          }

          .tools-count {
            background: rgba(59, 130, 246, 0.15);
            color: #9ca3af;
          }

          .elapsed-time {
            color: #9ca3af;
          }

          .progress-bar {
            background: rgba(59, 130, 246, 0.25);
          }
        }

        /* Compact mode for smaller screens */
        @media (max-width: 640px) {
          .inline-loading-indicator {
            padding: 4px 6px;
          }

          .status-line {
            gap: 4px;
            margin-bottom: 2px;
          }

          .phase-icon {
            font-size: 11px;
          }

          .status-text {
            font-size: 10px;
          }

          .tool-badge {
            font-size: 8px;
            padding: 1px 4px;
          }

          .tools-count {
            font-size: 8px;
            padding: 1px 3px;
          }

          .elapsed-time {
            font-size: 8px;
          }

          .progress-bar {
            height: 1.5px;
          }
        }
      `}</style>
    </div>
  )
}

export default InlineLoadingIndicator
