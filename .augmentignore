
# Selective node_modules inclusion - only essential direct dependencies
# Remove blanket node_modules exclusion to allow selective inclusion

# EXPL<PERSON><PERSON>LY INCLUDE actively imported libraries (with ! prefix)
# These libraries are actually used in the codebase and need to remain accessible

# React ecosystem
!node_modules/react
!node_modules/react-dom

# State management
!node_modules/jotai

# React utilities and hooks
!node_modules/react-use

# Markdown rendering
!node_modules/react-markdown
!node_modules/remark-gfm

# Syntax highlighting
!node_modules/react-syntax-highlighter
!node_modules/highlightjs-vue

# WebSocket functionality (native implementation)
# No external dependencies needed for WebSocket client

# CSS and styling
!node_modules/tailwindcss
!node_modules/@tailwindcss
!node_modules/css-to-tailwind-translator

# Type definitions for actively used libraries
!node_modules/@types/react
!node_modules/@types/react-dom
!node_modules/@types/node
!node_modules/@types/react-syntax-highlighter

# Figma plugin types
!node_modules/@figma/plugin-typings

# Keep llmdocs excluded
!llmdocs