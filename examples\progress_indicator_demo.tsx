import React, { useState } from 'react'
import { Provider } from 'jotai'
import AiProgressIndicator from '../src/ui/components/AiProgressIndicator'
import { useAiProgress } from '../src/ui/store/hooks'

// Demo component to showcase the dynamic loading indicator
const ProgressIndicatorDemo: React.FC = () => {
  const { startProgress, setPhase, setToolExecution, completeProgress } = useAiProgress()
  const [isRunning, setIsRunning] = useState(false)

  const runDemo = async () => {
    if (isRunning) return

    setIsRunning(true)

    try {
      // Phase 1: Initialize
      startProgress('Starting conversation with OpenRouter...')
      await delay(1000)

      // Phase 2: Thinking
      setPhase('thinking', 'Understanding your request...')
      await delay(1500)

      // Phase 3: Tool Execution 1
      setToolExecution('get_figma_selection_json')
      await delay(2000)

      // Phase 4: Analyzing
      setPhase('analyzing', 'Analyzing selection data...')
      await delay(1000)

      // Phase 5: Tool Execution 2
      setToolExecution('get_figma_selection_css')
      await delay(2000)

      // Phase 6: More Analysis
      setPhase('analyzing', 'Generating recommendations...')
      await delay(1500)

      // Phase 7: Completing
      setPhase('completing', 'Finalizing response...')
      await delay(1000)

      // Complete
      completeProgress()
    } catch (error) {
      completeProgress()
    } finally {
      setIsRunning(false)
    }
  }

  const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-800 mb-2">
          AI Progress Indicator Demo
        </h1>
        <p className="text-gray-600">
          Experience the dynamic loading indicators used during multi-turn AI conversations
        </p>
      </div>

      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <h2 className="text-lg font-semibold text-gray-700 mb-4">
          Multi-Turn Tool Calling Simulation
        </h2>

        {/* Progress Indicator */}
        <AiProgressIndicator />

        {/* Demo Controls */}
        <div className="mt-4 text-center">
          <button
            onClick={runDemo}
            disabled={isRunning}
            className={`px-6 py-2 rounded-lg font-medium transition-colors ${
              isRunning
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            {isRunning ? 'Running Demo...' : 'Start Demo'}
          </button>
        </div>
      </div>

      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h3 className="text-md font-semibold text-gray-700 mb-3">
          Demo Sequence
        </h3>
        <div className="space-y-2 text-sm text-gray-600">
          <div className="flex items-center gap-2">
            <span className="text-lg">🚀</span>
            <span><strong>Initialize:</strong> Starting conversation...</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-lg">🤔</span>
            <span><strong>Thinking:</strong> Understanding request</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-lg">🔧</span>
            <span><strong>Tool Execution:</strong> get_figma_selection_json</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-lg">📊</span>
            <span><strong>Analyzing:</strong> Processing selection data</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-lg">🔧</span>
            <span><strong>Tool Execution:</strong> get_figma_selection_css</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-lg">📊</span>
            <span><strong>Analyzing:</strong> Generating recommendations</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-lg">✨</span>
            <span><strong>Completing:</strong> Finalizing response</span>
          </div>
        </div>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="text-md font-semibold text-blue-800 mb-3">
          Features Demonstrated
        </h3>
        <ul className="space-y-1 text-sm text-blue-700">
          <li>• <strong>Indeterminate Progress Bar:</strong> Shows ongoing activity without specific completion percentage</li>
          <li>• <strong>Phase Icons:</strong> Visual indicators for each processing phase</li>
          <li>• <strong>Status Messages:</strong> Real-time updates with animated ellipsis</li>
          <li>• <strong>Tool Execution:</strong> Specific tool names and execution count</li>
          <li>• <strong>Elapsed Time:</strong> Live time tracking during processing</li>
          <li>• <strong>Smooth Animations:</strong> Fade-in/out and progress transitions</li>
          <li>• <strong>Auto-Hide on Stop:</strong> Disappears when user clicks stop button</li>
        </ul>
      </div>

      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <h3 className="text-md font-semibold text-green-800 mb-3">
          Real-World Usage
        </h3>
        <p className="text-sm text-green-700 mb-2">
          This progress indicator automatically appears when you:
        </p>
        <ul className="space-y-1 text-sm text-green-700">
          <li>• Send messages to the AI agent in the chat</li>
          <li>• Use commands like <code>/ai analyze my selection</code></li>
          <li>• Trigger multi-turn tool calling sequences</li>
          <li>• Request complex design analysis or code generation</li>
        </ul>
      </div>

      <div className="text-center text-xs text-gray-500">
        <p>
          This demo simulates the actual progress tracking used in the Figma Agent.
          <br />
          In real usage, progress updates are driven by actual AI API responses and tool executions.
        </p>
      </div>
    </div>
  )
}

// Wrapper component with Jotai provider
const ProgressIndicatorDemoApp: React.FC = () => {
  return (
    <Provider>
      <div className="min-h-screen bg-gray-100 py-8">
        <ProgressIndicatorDemo />
      </div>
    </Provider>
  )
}

export default ProgressIndicatorDemoApp

// Usage instructions for integration:
/*
To integrate this demo into your development environment:

1. Add to your development routes or create a standalone demo page
2. Import and render ProgressIndicatorDemoApp
3. Ensure Jotai provider is available in your app context
4. Style with your preferred CSS framework (Tailwind shown here)

Example integration:
```tsx
import ProgressIndicatorDemoApp from './examples/progress_indicator_demo'

// In your router or demo page
<Route path="/demo/progress" component={ProgressIndicatorDemoApp} />
```

The demo showcases all the key features:
- Real-time progress tracking
- Phase-based visual feedback
- Tool execution monitoring
- Turn-by-turn progress updates
- Animated visual elements
- Professional loading experience
*/
