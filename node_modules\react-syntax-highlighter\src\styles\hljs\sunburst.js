export default {
    "hljs": {
        "display": "block",
        "overflowX": "auto",
        "padding": "0.5em",
        "background": "#000",
        "color": "#f8f8f8"
    },
    "hljs-comment": {
        "color": "#aeaeae",
        "fontStyle": "italic"
    },
    "hljs-quote": {
        "color": "#aeaeae",
        "fontStyle": "italic"
    },
    "hljs-keyword": {
        "color": "#e28964"
    },
    "hljs-selector-tag": {
        "color": "#e28964"
    },
    "hljs-type": {
        "color": "#e28964"
    },
    "hljs-string": {
        "color": "#65b042"
    },
    "hljs-subst": {
        "color": "#daefa3"
    },
    "hljs-regexp": {
        "color": "#e9c062"
    },
    "hljs-link": {
        "color": "#e9c062"
    },
    "hljs-title": {
        "color": "#89bdff"
    },
    "hljs-section": {
        "color": "#89bdff"
    },
    "hljs-tag": {
        "color": "#89bdff"
    },
    "hljs-name": {
        "color": "#89bdff"
    },
    "hljs-class .hljs-title": {
        "textDecoration": "underline"
    },
    "hljs-doctag": {
        "textDecoration": "underline"
    },
    "hljs-symbol": {
        "color": "#3387cc"
    },
    "hljs-bullet": {
        "color": "#3387cc"
    },
    "hljs-number": {
        "color": "#3387cc"
    },
    "hljs-params": {
        "color": "#3e87e3"
    },
    "hljs-variable": {
        "color": "#3e87e3"
    },
    "hljs-template-variable": {
        "color": "#3e87e3"
    },
    "hljs-attribute": {
        "color": "#cda869"
    },
    "hljs-meta": {
        "color": "#8996a8"
    },
    "hljs-formula": {
        "backgroundColor": "#0e2231",
        "color": "#f8f8f8",
        "fontStyle": "italic"
    },
    "hljs-addition": {
        "backgroundColor": "#253b22",
        "color": "#f8f8f8"
    },
    "hljs-deletion": {
        "backgroundColor": "#420e09",
        "color": "#f8f8f8"
    },
    "hljs-selector-class": {
        "color": "#9b703f"
    },
    "hljs-selector-id": {
        "color": "#8b98ab"
    },
    "hljs-emphasis": {
        "fontStyle": "italic"
    },
    "hljs-strong": {
        "fontWeight": "bold"
    }
}