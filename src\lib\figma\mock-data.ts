// Mock Data Generators for Development Environment
// Provides realistic placeholder data for Figma APIs when running in web mode

export interface MockFigmaNode {
  id: string
  name: string
  type: string
  visible: boolean
  locked: boolean
  x: number
  y: number
  width: number
  height: number
  rotation: number
  opacity: number
  fills?: MockPaint[]
  strokes?: MockPaint[]
  strokeWeight?: number
  cornerRadius?: number
  children?: MockFigmaNode[]
  characters?: string
  fontSize?: number
  fontName?: { family: string; style: string }
  textAlignHorizontal?: string
  textAlignVertical?: string
  lineHeight?: { value: number; unit: string }
  letterSpacing?: { value: number; unit: string }
}

export interface MockPaint {
  type: 'SOLID' | 'GRADIENT_LINEAR' | 'GRADIENT_RADIAL' | 'IMAGE'
  color?: { r: number; g: number; b: number }
  opacity?: number
  gradientStops?: Array<{
    position: number
    color: { r: number; g: number; b: number }
  }>
}

// Generate random colors
function randomColor(): { r: number; g: number; b: number } {
  return {
    r: Math.random(),
    g: Math.random(),
    b: Math.random()
  }
}

// Generate random solid paint
function randomSolidPaint(): MockPaint {
  return {
    type: 'SOLID',
    color: randomColor(),
    opacity: 0.8 + Math.random() * 0.2
  }
}

// Generate random gradient paint
function randomGradientPaint(): MockPaint {
  return {
    type: 'GRADIENT_LINEAR',
    opacity: 1,
    gradientStops: [
      { position: 0, color: randomColor() },
      { position: 1, color: randomColor() }
    ]
  }
}

// Generate random text node
function generateMockTextNode(id: string, name: string): MockFigmaNode {
  const sampleTexts = [
    'Hello World',
    'Sample Text',
    'Lorem ipsum dolor sit amet',
    'Design System',
    'Button Label',
    'Heading Text',
    'Paragraph content goes here'
  ]

  return {
    id,
    name,
    type: 'TEXT',
    visible: true,
    locked: false,
    x: Math.random() * 800,
    y: Math.random() * 600,
    width: 100 + Math.random() * 200,
    height: 20 + Math.random() * 40,
    rotation: 0,
    opacity: 1,
    fills: [randomSolidPaint()],
    characters: sampleTexts[Math.floor(Math.random() * sampleTexts.length)],
    fontSize: 12 + Math.random() * 24,
    fontName: { family: 'Inter', style: 'Regular' },
    textAlignHorizontal: 'LEFT',
    textAlignVertical: 'TOP',
    lineHeight: { value: 1.2, unit: 'PERCENT' },
    letterSpacing: { value: 0, unit: 'PIXELS' }
  }
}

// Generate random rectangle node
function generateMockRectangleNode(id: string, name: string): MockFigmaNode {
  return {
    id,
    name,
    type: 'RECTANGLE',
    visible: true,
    locked: false,
    x: Math.random() * 800,
    y: Math.random() * 600,
    width: 50 + Math.random() * 300,
    height: 50 + Math.random() * 200,
    rotation: 0,
    opacity: 1,
    fills: [Math.random() > 0.5 ? randomSolidPaint() : randomGradientPaint()],
    strokes: Math.random() > 0.7 ? [randomSolidPaint()] : [],
    strokeWeight: Math.random() > 0.7 ? 1 + Math.random() * 3 : 0,
    cornerRadius: Math.random() > 0.5 ? Math.random() * 20 : 0
  }
}

// Generate random frame node with children
function generateMockFrameNode(id: string, name: string): MockFigmaNode {
  const childCount = 1 + Math.floor(Math.random() * 4)
  const children: MockFigmaNode[] = []

  for (let i = 0; i < childCount; i++) {
    const childId = `${id}_child_${i}`
    const childType = Math.random() > 0.5 ? 'TEXT' : 'RECTANGLE'
    
    if (childType === 'TEXT') {
      children.push(generateMockTextNode(childId, `Text ${i + 1}`))
    } else {
      children.push(generateMockRectangleNode(childId, `Rectangle ${i + 1}`))
    }
  }

  return {
    id,
    name,
    type: 'FRAME',
    visible: true,
    locked: false,
    x: Math.random() * 600,
    y: Math.random() * 400,
    width: 200 + Math.random() * 400,
    height: 150 + Math.random() * 300,
    rotation: 0,
    opacity: 1,
    fills: [{ type: 'SOLID', color: { r: 1, g: 1, b: 1 }, opacity: 1 }],
    children
  }
}

// Generate mock selection data
export function generateMockSelection(): Record<string, unknown> {
  const nodeCount = 1 + Math.floor(Math.random() * 3)
  const selection: MockFigmaNode[] = []

  const nodeTypes = ['TEXT', 'RECTANGLE', 'FRAME']
  const nodeNames = [
    'Button Component',
    'Header Text',
    'Card Container',
    'Icon Button',
    'Navigation Item',
    'Content Block',
    'Sidebar Panel'
  ]

  for (let i = 0; i < nodeCount; i++) {
    const id = `mock_node_${Date.now()}_${i}`
    const type = nodeTypes[Math.floor(Math.random() * nodeTypes.length)]
    const name = nodeNames[Math.floor(Math.random() * nodeNames.length)]

    switch (type) {
      case 'TEXT':
        selection.push(generateMockTextNode(id, name))
        break
      case 'RECTANGLE':
        selection.push(generateMockRectangleNode(id, name))
        break
      case 'FRAME':
        selection.push(generateMockFrameNode(id, name))
        break
    }
  }

  return {
    selection,
    timestamp: new Date().toISOString(),
    mockData: true
  }
}

// Generate empty selection
export function generateEmptySelection(): Record<string, unknown> {
  return {
    selection: [],
    timestamp: new Date().toISOString(),
    mockData: true
  }
}

// Generate mock window settings
export function generateMockWindowSettings() {
  return {
    width: 400,
    height: 600,
    mockData: true
  }
}
