export default {
    "hljs": {
        "display": "block",
        "overflowX": "auto",
        "padding": "0.5em",
        "background": "#474949",
        "color": "#d1d9e1"
    },
    "hljs-comment": {
        "color": "#969896",
        "fontStyle": "italic"
    },
    "hljs-quote": {
        "color": "#969896",
        "fontStyle": "italic"
    },
    "hljs-keyword": {
        "color": "#cc99cc"
    },
    "hljs-selector-tag": {
        "color": "#cc99cc"
    },
    "hljs-literal": {
        "color": "#cc99cc"
    },
    "hljs-type": {
        "color": "#cc99cc"
    },
    "hljs-addition": {
        "color": "#cc99cc"
    },
    "hljs-number": {
        "color": "#f99157"
    },
    "hljs-selector-attr": {
        "color": "#f99157"
    },
    "hljs-selector-pseudo": {
        "color": "#f99157"
    },
    "hljs-string": {
        "color": "#8abeb7"
    },
    "hljs-doctag": {
        "color": "#8abeb7"
    },
    "hljs-regexp": {
        "color": "#8abeb7"
    },
    "hljs-title": {
        "color": "#b5bd68"
    },
    "hljs-name": {
        "color": "#b5bd68",
        "fontWeight": "bold"
    },
    "hljs-section": {
        "color": "#b5bd68",
        "fontWeight": "bold"
    },
    "hljs-built_in": {
        "color": "#b5bd68"
    },
    "hljs-variable": {
        "color": "#ffcc66"
    },
    "hljs-template-variable": {
        "color": "#ffcc66"
    },
    "hljs-selector-id": {
        "color": "#ffcc66"
    },
    "hljs-class .hljs-title": {
        "color": "#ffcc66"
    },
    "hljs-strong": {
        "fontWeight": "bold"
    },
    "hljs-symbol": {
        "color": "#f99157"
    },
    "hljs-bullet": {
        "color": "#f99157"
    },
    "hljs-subst": {
        "color": "#f99157"
    },
    "hljs-meta": {
        "color": "#f99157"
    },
    "hljs-link": {
        "color": "#f99157"
    },
    "hljs-deletion": {
        "color": "#dc322f"
    },
    "hljs-formula": {
        "background": "#eee8d5"
    },
    "hljs-attr": {
        "color": "#81a2be"
    },
    "hljs-attribute": {
        "color": "#81a2be"
    },
    "hljs-emphasis": {
        "fontStyle": "italic"
    }
}