#!/usr/bin/env python3
"""
OpenRouter Models Update Script

This script fetches the latest model data from OpenRouter API and updates
the local models.json file used for autocomplete functionality.

Usage:
    python scripts/update-openrouter-models.py
"""

import json
import logging
import os
import sys
from pathlib import Path
from typing import Dict, Any
import urllib.request
import urllib.error

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# Configuration
OPENROUTER_API_URL = "https://openrouter.ai/api/v1/models"
MODELS_JSON_PATH = "src/lib/openrouter/models.json"
REQUEST_TIMEOUT = 30  # seconds





def save_models_json(raw_api_response: Dict[str, Any], output_path: str) -> bool:
    """
    Save raw OpenRouter API response to JSON file.

    Args:
        raw_api_response: Raw API response from OpenRouter
        output_path: Path to save the JSON file

    Returns:
        True if successful, False otherwise
    """
    try:
        # Ensure the directory exists
        output_dir = Path(output_path).parent
        output_dir.mkdir(parents=True, exist_ok=True)

        # Save the raw API response directly
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(raw_api_response, f, indent=2, ensure_ascii=False)

        models_count = len(raw_api_response.get('data', []))
        logger.info(f"Successfully saved raw API response with {models_count} models to {output_path}")
        return True

    except Exception as e:
        logger.error(f"Failed to save models to {output_path}: {e}")
        return False


def main():
    """Main function to update OpenRouter models."""
    logger.info("Starting OpenRouter models update")

    try:
        # Change to script directory to ensure relative paths work
        script_dir = Path(__file__).parent
        project_root = script_dir.parent
        os.chdir(project_root)

        # Fetch raw API response
        logger.info(f"Fetching models from {OPENROUTER_API_URL}")

        # Create request with headers
        req = urllib.request.Request(
            OPENROUTER_API_URL,
            headers={
                'User-Agent': 'FigmaAgent/1.0.0 (Model Update Script)',
                'Accept': 'application/json'
            }
        )

        # Make the request and get raw response
        with urllib.request.urlopen(req, timeout=REQUEST_TIMEOUT) as response:
            if response.status != 200:
                logger.error(f"API request failed with status {response.status}")
                sys.exit(1)

            raw_api_response = json.loads(response.read().decode('utf-8'))

            if 'data' not in raw_api_response:
                logger.error("Invalid API response format: missing 'data' field")
                sys.exit(1)

            models_count = len(raw_api_response['data'])
            logger.info(f"Successfully fetched {models_count} models")

        # Save raw API response to JSON file
        if not save_models_json(raw_api_response, MODELS_JSON_PATH):
            logger.error("Failed to save models to JSON file")
            sys.exit(1)

        logger.info("OpenRouter models update completed successfully")

    except urllib.error.URLError as e:
        logger.error(f"Network error while fetching models: {e}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse JSON response: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        logger.info("Update cancelled by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error during update: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
