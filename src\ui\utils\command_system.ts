// Simplified command system for handling chat commands

import { useCallback, useState } from 'react'
import { getFigmaClient } from '../../lib/figma'
import { Message, Command, AVAILABLE_COMMANDS, COMMAND_PREFIX } from '../types/ui_types'
import { CssToTailwindTranslator } from "css-to-tailwind-translator"

// Pure utility functions for HTML generation

// Type definitions for better type safety
interface FigmaNode {
  type?: string
  name?: string
  id?: string
  characters?: string
  css?: Record<string, unknown>
  children?: Array<{ figma?: FigmaNode } | FigmaNode>
  mainComponent?: string | { id: string } // For INSTANCE nodes
  [key: string]: unknown
}

// Helper function to convert CSS object to CSS string
const styleObjectToCss = (styleObj: Record<string, unknown>): string => {
  return Object.entries(styleObj)
    .map(([property, value]) => `  ${property}: ${value};`)
    .join('\n')
}

// Helper function to convert CSS object to Tailwind classes
const convertCssToTailwind = (cssObj: Record<string, unknown>): string => {
  try {
    const cssString = styleObjectToCss(cssObj)
    const fullCssRule = `.temp-class {\n${cssString}\n}`
    const result = CssToTailwindTranslator(fullCssRule)
    return result.data[0]?.resultVal || ''
  } catch (error) {
    console.warn('Failed to convert CSS to Tailwind:', error)
    return ''
  }
}

// Helper function to get appropriate HTML tag based on Figma node type
const getHtmlTag = (nodeType: string): string => {
  switch (nodeType) {
    case 'TEXT':
      return 'p'
    case 'FRAME':
    case 'GROUP':
      return 'div'
    case 'RECTANGLE':
    case 'ELLIPSE':
    case 'POLYGON':
    case 'STAR':
      return 'div'
    case 'VECTOR':
      return 'svg'
    case 'IMAGE':
      return 'img'
    default:
      return 'div'
  }
}

// Helper function to sanitize text for HTML
const sanitizeForHtml = (text: string): string => {
  return text.replace(/[<>&]/g, (match) => {
    const entities: Record<string, string> = { '<': '&lt;', '>': '&gt;', '&': '&amp;' }
    return entities[match] || match
  })
}

// Helper function to get display name for node (text content for TEXT nodes, name for others)
const getNodeDisplayName = (node: FigmaNode, nodeType: string): string => {
  let name = String(node.name || 'element')
  if (nodeType === 'TEXT' && node.characters) {
    name = String(node.characters).substring(0, 50)
    if (String(node.characters).length > 50) {
      name += '...'
    }
  }
  return name.replace(/[<>&"]/g, '') // Sanitize for HTML comments
}

// Helper function to generate detailed node information for HTML comments
const getNodeInfo = (node: FigmaNode): string => {
  const nodeType = String(node.type || 'UNKNOWN')
  const nodeId = String(node.id || 'no-id')
  const nodeName = getNodeDisplayName(node, nodeType)

  let info = `${nodeName} | type: ${nodeType} | id: ${nodeId}`

  // Add mainComponent info for INSTANCE nodes
  if (nodeType === 'INSTANCE' && node.mainComponent) {
    let mainComponentId: string
    if (typeof node.mainComponent === 'string') {
      mainComponentId = node.mainComponent
    } else if (typeof node.mainComponent === 'object' && node.mainComponent.id) {
      mainComponentId = node.mainComponent.id
    } else {
      mainComponentId = String(node.mainComponent)
    }
    info += ` | component: ${mainComponentId}`
  }

  return info
}

// Pure function to generate HTML structure from Figma nodes
const generateHtmlFromNode = (node: FigmaNode, depth: number = 0): string => {
  // Safety check for node
  if (!node || typeof node !== 'object') {
    return `${'  '.repeat(depth)}<!-- Invalid node -->`
  }

  const indent = '  '.repeat(depth)
  const nodeType = String(node.type || 'UNKNOWN')
  const name = getNodeDisplayName(node, nodeType)

  // Get appropriate HTML tag
  const tag = getHtmlTag(nodeType)

  // Convert CSS to Tailwind classes
  let tailwindClasses = ''
  if (node.css && typeof node.css === 'object') {
    tailwindClasses = convertCssToTailwind(node.css)
  }

  // Handle different node types
  if (nodeType === 'TEXT') {
    // For text nodes, embed the text content directly
    const textContent = sanitizeForHtml(String(node.characters || ''))
    const classAttr = tailwindClasses ? ` class="${tailwindClasses}"` : ''
    return `${indent}<${tag}${classAttr}>${textContent}</${tag}>`
  } else if (nodeType === 'VECTOR' || nodeType === 'IMAGE') {
    // For SVG/image nodes, treat as image placeholder
    const classAttr = tailwindClasses ? ` class="${tailwindClasses}"` : ''
    if (tag === 'svg') {
      return `${indent}<${tag}${classAttr}>\n${indent}  <!-- SVG content for ${name} -->\n${indent}</${tag}>`
    } else {
      return `${indent}<${tag}${classAttr} src="#" alt="${name}" />`
    }
  } else {
    // For container nodes, process children recursively
    const classAttr = tailwindClasses ? ` class="${tailwindClasses}"` : ''
    let html = `${indent}<${tag}${classAttr}>`

    // Add detailed comment for identification
    if (depth === 0 || (name !== 'element' && nodeType !== 'TEXT')) {
      const nodeInfo = getNodeInfo(node)
      html += ` <!-- ${nodeInfo} -->`
    }

    // Process children if they exist
    if (node.children && Array.isArray(node.children) && node.children.length > 0) {
      html += '\n'
      for (const child of node.children) {
        const childNode = (child as any).figma || child
        const childHtml = generateHtmlFromNode(childNode as FigmaNode, depth + 1)
        if (childHtml.trim()) { // Only add non-empty child HTML
          html += childHtml + '\n'
        }
      }
      html += indent
    } else if (nodeType === 'TEXT' && node.characters) {
      // Handle text content for nodes that might not have proper type detection
      const textContent = sanitizeForHtml(String(node.characters))
      html += textContent
    }

    html += `</${tag}>`
    return html
  }
};

export interface CommandHookOptions {
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>
  messagesEndRef?: React.RefObject<HTMLDivElement | null>
  unifiedAiApi?: {
    sendStreamingMessage: (message: string) => Promise<void>
    config: { apiKey: string }
    vendor: string
  }
}

// Create a message helper
function createMessage(text: string, type: 'user' | 'system' = 'system'): Message {
  return {
    id: Date.now().toString() + Math.random(),
    text,
    timestamp: new Date(),
    type
  }
}

// Main command hook
export function useCommandSystem(options: CommandHookOptions) {
  console.log('🔧 [CommandSystem] useCommandSystem hook initialized')
  const { setMessages, unifiedAiApi } = options
  const [lastCommand, setLastCommand] = useState<string | null>(null)

  console.log('🔧 [CommandSystem] Unified AI API available:', !!unifiedAiApi)
  if (unifiedAiApi) {
    console.log('🔧 [CommandSystem] AI config:', {
      vendor: unifiedAiApi.vendor,
      hasApiKey: !!unifiedAiApi.config.apiKey
    })
  }

  // Add message to chat
  const addMessage = useCallback((message: Message) => {
    setMessages(prev => [...prev, message])
  }, [setMessages])

  // Get Figma selection and format as JSON
  const getFigmaSelection = useCallback(async (): Promise<void> => {
    console.log('🎯 [Command] Starting getFigmaSelection command')

    try {
      console.log('📡 [Command] Calling getFigmaClient().selection.getSelection()')
      const selection = await getFigmaClient().selection.getSelection()
      console.log('✅ [Command] Selection received:', {
        isArray: Array.isArray(selection),
        length: Array.isArray(selection) ? selection.length : 'not array',
        type: typeof selection
      })

      const jsonData = JSON.stringify(selection, null, 2)
      const responseText = `\`\`\`json\n${jsonData}\n\`\`\``
      addMessage(createMessage(responseText, 'system'))
    } catch (error) {
      console.error('❌ [Command] getFigmaSelection failed:', error instanceof Error ? error.message : String(error))
      addMessage(createMessage(`Failed to get Figma selection: ${error instanceof Error ? error.message : 'Unknown error'}`))
    }
  }, [addMessage])



  // Get Figma selection and format as CSS
  const getFigmaSelectionCss = useCallback(async (): Promise<void> => {
    try {
      const selection = await getFigmaClient().selection.getSelection()
      let cssText = ''

      if (Array.isArray(selection) && selection.length > 0) {
        cssText = selection.map((nodeWrapper: any, index: number) => {
          // Access the figma object which contains the actual node data
          const node = nodeWrapper.figma || nodeWrapper
          const name = String(node.name || `element-${index}`)
          const selector = `.${name.toLowerCase().replace(/\s+/g, '-')}`

          // Convert CSS style object to CSS string
          if (node.css && typeof node.css === 'object') {
            const cssStyles = styleObjectToCss(node.css)
            return `${selector} {\n${cssStyles}\n}`
          } else {
            return `${selector} {\n  /* No CSS available for this element */\n}`
          }
        }).join('\n\n')
      } else {
        cssText = '/* No selection found */'
      }

      const responseText = `\`\`\`css\n${cssText}\n\`\`\``
      addMessage(createMessage(responseText, 'system'))
    } catch (error) {
      addMessage(createMessage(`Failed to get Figma selection: ${error instanceof Error ? error.message : 'Unknown error'}`))
    }
  }, [addMessage])

  // Get Figma selection and format as Tailwind CSS
  const getFigmaSelectionTailwind = useCallback(async (): Promise<void> => {
    try {
      const selection = await getFigmaClient().selection.getSelection()
      let tailwindText = ''

      if (Array.isArray(selection) && selection.length > 0) {
        tailwindText = selection.map((nodeWrapper: any, index: number) => {
          // Access the figma object which contains the actual node data
          const node = nodeWrapper.figma || nodeWrapper
          const name = String(node.name || `element-${index}`)

          // Use CSS from Figma and convert to Tailwind using library
          if (node.css && typeof node.css === 'object') {
            try {
              // Convert CSS object to CSS string first
              const cssString = styleObjectToCss(node.css)
              const fullCssRule = `.temp-class {\n${cssString}\n}`

              // Use css-to-tailwind-translator library to convert
              const tailwindClasses = CssToTailwindTranslator(fullCssRule).data[0].resultVal

              return `<!-- ${name} -->\n<div class="${tailwindClasses}"></div>`
            } catch (conversionError) {
              console.warn('Failed to convert CSS to Tailwind for', name, conversionError)
              return `<!-- ${name} -->\n<div class="/* CSS to Tailwind conversion failed */"></div>`
            }
          } else {
            return `<!-- ${name} -->\n<div class="/* No CSS available for conversion */"></div>`
          }
        }).join('\n\n')
      } else {
        tailwindText = '<!-- No selection found -->'
      }

      const responseText = `\`\`\`html\n${tailwindText}\n\`\`\``
      addMessage(createMessage(responseText, 'system'))
    } catch (error) {
      addMessage(createMessage(`Failed to get Figma selection: ${error instanceof Error ? error.message : 'Unknown error'}`))
    }
  }, [addMessage])



  // Get Figma selection and format as Tailwind HTML structure
  const getFigmaSelectionTailwindHtml = useCallback(async (): Promise<void> => {
    try {
      const selection = await getFigmaClient().selection.getSelection()
      let htmlText = ''

      if (Array.isArray(selection) && selection.length > 0) {
        htmlText = selection.map((nodeWrapper: any) => {
          // Access the figma object which contains the actual node data
          const node = nodeWrapper.figma || nodeWrapper
          return generateHtmlFromNode(node, 0)
        }).join('\n\n')
      } else {
        htmlText = '<!-- No selection found -->'
      }

      const responseText = `\`\`\`html\n${htmlText}\n\`\`\``
      addMessage(createMessage(responseText, 'system'))
    } catch (error) {
      addMessage(createMessage(`Failed to get Figma selection: ${error instanceof Error ? error.message : 'Unknown error'}`))
    }
  }, [addMessage])

  // Handle AI chat command
  const handleAiCommand = useCallback(async (commandText: string): Promise<void> => {
    console.log('🤖 [CommandSystem] handleAiCommand called with:', commandText)

    if (!unifiedAiApi) {
      console.log('⚠️ [CommandSystem] Unified AI API not available')
      addMessage(createMessage('AI functionality is not available. Please check your configuration.'))
      return
    }

    if (!unifiedAiApi.config.apiKey) {
      console.log(`⚠️ [CommandSystem] ${unifiedAiApi.vendor} API key not configured`)
      addMessage(createMessage(`${unifiedAiApi.vendor} API key is not configured. Please add it in settings.`))
      return
    }

    // Extract message from command (everything after '/ai ')
    const aiMessage = commandText.replace('/ai', '').trim()

    if (!aiMessage) {
      addMessage(createMessage('Please provide a message for the Agent. Usage: /ai [your message]'))
      return
    }

    try {
      console.log(`🤖 [AI] Sending to ${unifiedAiApi.vendor}: "${aiMessage}"`)
      // Use streaming for AI responses
      await unifiedAiApi.sendStreamingMessage(aiMessage)
    } catch (error) {
      console.error('❌ [AI] Command failed:', error instanceof Error ? error.message : String(error))
      addMessage(createMessage(`Agent command failed: ${error instanceof Error ? error.message : 'Unknown error'}`))
    }
  }, [addMessage, unifiedAiApi])

  // Command handlers mapping
  const commandHandlers: Record<string, (commandText?: string) => Promise<void>> = {
    '/sel-figma': getFigmaSelection,
    '/sel-css': getFigmaSelectionCss,
    '/sel-twcss': getFigmaSelectionTailwind,
    '/sel-twhtml': getFigmaSelectionTailwindHtml,
    '/ai': async (commandText?: string) => {
      if (commandText) {
        await handleAiCommand(commandText)
      } else {
        addMessage(createMessage('Please provide a message for the Agent. Usage: /ai [your message]'))
      }
    },
    '/p': async () => {
      if (lastCommand && lastCommand !== '/p') {
        await executeCommand(lastCommand)
      } else {
        addMessage(createMessage('No previous command to repeat'))
      }
    },
    '/help': async () => {
      const helpText = `## Available Commands\n\n${AVAILABLE_COMMANDS.map(c => `- **${c.name}** - ${c.description}`).join('\n')}`
      addMessage(createMessage(helpText))
    }
  }

  // Execute a command
  const executeCommand = useCallback(async (commandText: string): Promise<void> => {
    const fullCommand = commandText.trim()
    setLastCommand(fullCommand)

    // Add user command to messages
    addMessage(createMessage(fullCommand, 'user'))

    try {
      // Parse command and arguments
      const parts = fullCommand.split(' ')
      const command = parts[0]

      // Check if it's a direct command match
      const handler = commandHandlers[command]

      if (handler) {
        console.log(`🔧 [Command] Executing: ${command}`)
        await handler(fullCommand)
      } else {
        console.warn(`❌ [Command] Unknown: ${command}`)
        const errorText = `Unknown command: ${command}. Available commands: ${AVAILABLE_COMMANDS.map(c => c.name).join(', ')}`
        addMessage(createMessage(errorText))
      }
    } catch (error) {
      console.error('💥 [CommandSystem] Error executing command:', error)
      addMessage(createMessage(`Error executing command: ${error instanceof Error ? error.message : 'Unknown error'}`))
    }
  }, [setLastCommand, addMessage, commandHandlers])

  // Get command suggestions based on input
  const getSuggestions = useCallback((input: string, cursorPos: number): Command[] => {
    // Find the command prefix before cursor
    let prefixPos = -1
    for (let i = cursorPos - 1; i >= 0; i--) {
      if (input[i] === COMMAND_PREFIX) {
        if (i === 0 || /\s/.test(input[i - 1])) {
          prefixPos = i
          break
        }
      }
      if (/\s/.test(input[i])) {
        break
      }
    }

    // Only return suggestions if we found a command prefix
    if (prefixPos === -1) return []

    const query = input.substring(prefixPos + 1, cursorPos).toLowerCase()
    return AVAILABLE_COMMANDS.filter(cmd =>
      cmd.name.toLowerCase().includes(query) ||
      cmd.description.toLowerCase().includes(query)
    )
  }, [])

  return {
    executeCommand,
    getSuggestions
  }
}
