// Mock Figma Client for Development Environment
// Provides a complete mock implementation of the FigmaClient interface for web development

import {
  type FigmaClientEvents,
  type StorageInterface,
  type WindowInterface,
  type SelectionInterface,
  type CommandsInterface,
  type WindowSettings,
  DEFAULT_WINDOW_SETTINGS
} from './types'
import { generateMockSelection, generateEmptySelection } from './mock-data'

// Mock storage implementation using localStorage
class MockStorageInterface implements StorageInterface {
  private prefix = 'figmagent-mock-'

  async getItem<T>(key: string, defaultValue: T): Promise<T> {
    try {
      const fullKey = this.prefix + key
      const stored = localStorage.getItem(fullKey)
      if (stored === null) {
        console.log(`🔍 [MockStorage] No value found for key: ${key}, returning default`)
        return defaultValue
      }
      const parsed = JSON.parse(stored)
      console.log(`✅ [MockStorage] Retrieved value for key: ${key}`)
      return parsed
    } catch (error) {
      console.error(`❌ [MockStorage] Failed to get item: ${key}`, error)
      return defaultValue
    }
  }

  async setItem<T>(key: string, value: T): Promise<void> {
    try {
      const fullKey = this.prefix + key
      localStorage.setItem(fullKey, JSON.stringify(value))
      console.log(`💾 [MockStorage] Saved value for key: ${key}`)
    } catch (error) {
      console.error(`❌ [MockStorage] Failed to set item: ${key}`, error)
      throw error
    }
  }

  async removeItem(key: string): Promise<void> {
    try {
      const fullKey = this.prefix + key
      localStorage.removeItem(fullKey)
      console.log(`🗑️ [MockStorage] Removed value for key: ${key}`)
    } catch (error) {
      console.error(`❌ [MockStorage] Failed to remove item: ${key}`, error)
      throw error
    }
  }
}

// Mock window interface for web environment
class MockWindowInterface implements WindowInterface {
  private currentSize = { width: 400, height: 600 }

  async resize(width: number, height: number): Promise<void> {
    this.currentSize = { width, height }
    console.log(`🪟 [MockWindow] Resize requested: ${width}x${height}`)

    // In web development mode, ignore actual resize operations
    // Users can manually resize the browser window as needed
    console.log('🌐 [MockWindow] Web mode - resize operation logged but not executed')
    console.log('� [MockWindow] Tip: Resize your browser window manually for responsive testing')

    // Don't attempt to resize anything in web mode
    // The UI should be responsive and adapt to container size changes
  }

  async minimize(): Promise<void> {
    console.log('📉 [MockWindow] Minimize requested')
    console.log('🌐 [MockWindow] Web mode - minimize operation logged but not executed')
    console.log('� [MockWindow] Tip: Use browser developer tools to test mobile/tablet views')

    // Update internal state but don't actually minimize
    await this.resize(52, 40)
  }

  async close(): Promise<void> {
    console.log('❌ [MockWindow] Close requested')
    console.log('🌐 [MockWindow] Web mode - close operation logged but not executed')
    console.log('💡 [MockWindow] Tip: Close the browser tab manually if needed')
  }

  getCurrentSize() {
    return { ...this.currentSize }
  }

  // Additional helper for web mode
  getActualWindowSize() {
    if (typeof window !== 'undefined') {
      return {
        width: window.innerWidth,
        height: window.innerHeight,
        outerWidth: window.outerWidth,
        outerHeight: window.outerHeight
      }
    }
    return this.currentSize
  }
}

// Mock selection interface with realistic data
class MockSelectionInterface implements SelectionInterface {
  private hasSelection = true

  async getSelection(): Promise<Record<string, unknown>> {
    console.log('🎯 [MockSelection] Getting mock selection data')

    // Simulate some delay like real Figma API
    await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200))

    // Randomly return selection or empty selection
    if (Math.random() > 0.3 && this.hasSelection) {
      const selection = generateMockSelection()
      console.log('✅ [MockSelection] Returning mock selection with nodes:', selection)
      return selection
    } else {
      const emptySelection = generateEmptySelection()
      console.log('📭 [MockSelection] Returning empty selection')
      return emptySelection
    }
  }

  requestSelection(): void {
    console.log('📤 [MockSelection] Selection requested (event-based)')
    // In mock mode, we'll just trigger the event after a short delay
    setTimeout(() => {
      // This would normally be handled by the parent MockFigmaClient
      console.log('📨 [MockSelection] Mock selection event would be emitted')
    }, 50)
  }

  // Mock utility to toggle selection state for testing
  toggleSelectionState(): void {
    this.hasSelection = !this.hasSelection
    console.log(`🔄 [MockSelection] Selection state toggled: ${this.hasSelection ? 'has selection' : 'no selection'}`)
  }
}

// Mock commands interface
class MockCommandsInterface implements CommandsInterface {
  private selectionInterface: MockSelectionInterface
  private onSelectionCallback?: (result: { data: Record<string, unknown>; success: boolean; error?: string }) => void

  constructor(selectionInterface: MockSelectionInterface) {
    this.selectionInterface = selectionInterface
  }

  executeSelectionCommand(): void {
    console.log('⚡ [MockCommands] Executing selection command')
    this.selectionInterface.requestSelection()

    // Simulate async selection result
    setTimeout(async () => {
      try {
        const data = await this.selectionInterface.getSelection()
        this.onSelectionCallback?.({
          data,
          success: true
        })
      } catch (error) {
        this.onSelectionCallback?.({
          data: {},
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }, 100)
  }

  onSelectionResult(callback: (result: { data: Record<string, unknown>; success: boolean; error?: string }) => void): () => void {
    this.onSelectionCallback = callback
    return () => {
      this.onSelectionCallback = undefined
    }
  }
}

// Main Mock Figma Client
export class MockFigmaClient {
  private eventListeners = new Map<string, Set<(data: unknown) => void>>()

  public readonly storage: StorageInterface
  public readonly window: MockWindowInterface
  public readonly selection: MockSelectionInterface
  public readonly commands: CommandsInterface

  constructor() {
    console.log('🎭 [MockFigmaClient] Initializing mock Figma client for development')

    this.storage = new MockStorageInterface()
    this.window = new MockWindowInterface()
    this.selection = new MockSelectionInterface()
    this.commands = new MockCommandsInterface(this.selection)

    // Add some development helpers
    this.addDevelopmentHelpers()
  }

  private addDevelopmentHelpers(): void {
    // Add global helpers for development
    if (typeof window !== 'undefined') {
      (window as any).__figmaDevHelpers = {
        toggleSelection: () => this.selection.toggleSelectionState(),
        getStorageKeys: () => {
          const keys = []
          for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i)
            if (key?.startsWith('figmagent-mock-')) {
              keys.push(key.replace('figmagent-mock-', ''))
            }
          }
          return keys
        },
        clearStorage: () => {
          const keys = []
          for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i)
            if (key?.startsWith('figmagent-mock-')) {
              keys.push(key)
            }
          }
          keys.forEach(key => localStorage.removeItem(key))
          console.log('🧹 [MockFigmaClient] Cleared all mock storage')
        }
      }
      console.log('🛠️ [MockFigmaClient] Development helpers available at window.__figmaDevHelpers')
    }
  }

  // Event system implementation
  on<K extends keyof FigmaClientEvents>(event: K, listener: (data: FigmaClientEvents[K]) => void): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set())
    }
    this.eventListeners.get(event)!.add(listener as (data: unknown) => void)
  }

  off<K extends keyof FigmaClientEvents>(event: K, listener: (data: FigmaClientEvents[K]) => void): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      listeners.delete(listener as (data: unknown) => void)
    }
  }

  protected emit<K extends keyof FigmaClientEvents>(event: K, data: FigmaClientEvents[K]): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      listeners.forEach(listener => listener(data))
    }
  }

  // Utility methods
  async getWindowSettings(defaultSettings: WindowSettings): Promise<WindowSettings> {
    return this.storage.getItem('figmagent-window-settings', defaultSettings)
  }

  async setWindowSettings(settings: WindowSettings): Promise<void> {
    await this.storage.setItem('figmagent-window-settings', settings)
    await this.window.resize(settings.width, settings.height)
    this.emit('window-resized', { width: settings.width, height: settings.height })
  }

  // Cleanup
  destroy(): void {
    this.eventListeners.clear()
    console.log('🧹 [MockFigmaClient] Mock client destroyed')
  }
}
