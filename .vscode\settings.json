{
  // 终端环境配置
  "terminal.integrated.env.windows": {
    "PATH": "/d/msys64/home/<USER>/micromamba/envs/figmagent/Library/bin;/d/msys64/home/<USER>/micromamba/envs/figmagent;${env:PATH}"
  },

  // TypeScript 配置
  "typescript.preferences.includePackageJsonAutoImports": "auto",
  "typescript.suggest.autoImports": true,
  "typescript.updateImportsOnFileMove.enabled": "always",

  // ESLint 配置
  "eslint.enable": true,
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact"
  ],
  "eslint.format.enable": true,

  // 格式化配置
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "explicit"
  },

  // 文件关联
  "files.associations": {
    "*.json5": "json5",
    "*.css": "tailwindcss"
  },

  // Tailwind CSS 配置
  "tailwindCSS.includeLanguages": {
    "typescript": "javascript",
    "typescriptreact": "javascript"
  },
  "tailwindCSS.experimental.classRegex": [
    ["clsx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"],
    ["classnames\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"],
    ["cn\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
  ],

  // React 配置
  "emmet.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html"
  },

  // 文件排除
  "files.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/.git": true,
    "**/.DS_Store": true,
    "**/Thumbs.db": true
  },

  // 搜索排除
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/*.log": true,
    "**/package-lock.json": true
  },

  // 编辑器配置
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,
  "editor.wordWrap": "on",
  "editor.minimap.enabled": false,

  // 终端配置
  "terminal.integrated.defaultProfile.windows": "PowerShell",

  // Git 配置
  "git.autofetch": true,
  "git.confirmSync": false,

  // 智能提示
  "editor.quickSuggestions": {
    "strings": true
  },
  "editor.suggest.insertMode": "replace"
}