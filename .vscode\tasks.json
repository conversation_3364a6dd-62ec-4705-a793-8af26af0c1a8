{"version": "2.0.0", "tasks": [{"label": "Install Dependencies", "type": "shell", "command": "npm", "args": ["install"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Start Dev Server", "type": "shell", "command": "npm", "args": ["run", "dev"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Build Production", "type": "shell", "command": "npm", "args": ["run", "build:prod"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$tsc"]}, {"label": "Build Staging", "type": "shell", "command": "npm", "args": ["run", "build:staging"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$tsc"]}, {"label": "Type Check", "type": "shell", "command": "npm", "args": ["run", "type-check"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$tsc"]}, {"label": "ESLint", "type": "shell", "command": "npm", "args": ["run", "lint"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$eslint-stylish"]}, {"label": "ESLint Fix", "type": "shell", "command": "npm", "args": ["run", "lint:fix"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$eslint-stylish"]}, {"label": "Update Models", "type": "shell", "command": "npm", "args": ["run", "update-models"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Extract Figma Types", "type": "shell", "command": "npm", "args": ["run", "extract-types"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Watch Production", "type": "shell", "command": "npm", "args": ["run", "watch:prod"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$tsc-watch"], "isBackground": true}, {"label": "Watch Staging", "type": "shell", "command": "npm", "args": ["run", "watch:staging"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$tsc-watch"], "isBackground": true}]}