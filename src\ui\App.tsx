import { useEffect } from 'react'
import { getFigmaClient, WindowSettings, FigmaClient } from '../lib/figma'
import { TabSystem } from './components/TabSystem'
import { ChatTab } from './components/ChatTab'
import { DevToolsTab } from './components/DevToolsTab'
// import { NewsTab } from './components/NewsTab' // Temporarily hidden
import { MinimizedView } from './components/MinimizedView'
import { SettingsModal } from './components/SettingsModal'
import { JotaiProvider, useActiveTab, useMinimizedState, useSettingsModal, useWindowSettings, useVendorStorage } from './store'
import './App.css'

// Internal App component that uses Jo<PERSON> hooks
function AppContent() {
  // Use Jotai hooks for state management
  const windowSettings = useWindowSettings()
  const minimizedState = useMinimizedState()
  const activeTab = useActiveTab()
  const settingsModal = useSettingsModal()
  const vendorStorage = useVendorStorage() // Initialize vendor storage for debugging

  // Load saved window size on component mount (only once)
  useEffect(() => {
    const loadWindowSize = async () => {
      try {
        console.log('🔄 [App] Loading window settings on mount')
        const stored = await getFigmaClient().getWindowSettings(windowSettings.windowSettings)
        console.log('✅ [App] Loaded window settings:', stored)
        windowSettings.setWindowSettings(stored)
      } catch (error) {
        console.error('❌ [App] Failed to load window settings:', error)
        // windowSettings already has default values from atom
      }
    }
    loadWindowSize()
  }, []) // Remove windowSettings dependency to prevent infinite loop



  // Listen for window size changes from settings modal
  useEffect(() => {
    const handleStorageChange = (data: { key: string; value: unknown }) => {
      console.log('🔄 [App] Storage change event received:', data)
      if (data.key === 'figmagent-window-settings') {
        console.log('🪟 [App] Updating window settings from storage event')
        windowSettings.setWindowSettings(data.value as WindowSettings)
      }
    }

    // Listen for storage change events from figmaClient
    const client = getFigmaClient()
    client.on('storage-changed', handleStorageChange)

    return () => {
      client.off('storage-changed', handleStorageChange)
    }
  }, []) // Remove windowSettings dependency to prevent infinite loop

  // UI functions
  const minimize_plugin = async () => {
    minimizedState.minimize()
    try {
      await getFigmaClient().window.minimize()
    } catch (error) {
      console.error('Failed to minimize window:', error)
    }
  }

  const expand_plugin = async () => {
    minimizedState.expand()
    try {
      const settings = windowSettings.windowSettings
      await getFigmaClient().window.resize(settings.width, settings.height)
    } catch (error) {
      console.error('Failed to expand window:', error)
    }
  }



  // Tab definitions
  const envInfo = FigmaClient.detectEnvironment()
  const tabs = [
    {
      id: 'chat',
      label: 'Chat',
      icon: '💬',
      content: <ChatTab />
    },
    // Dev Tools tab - only visible in web-dev mode
    ...(envInfo.environment === 'web-dev' ? [{
      id: 'dev-tools',
      label: 'Dev Tools',
      icon: '🛠️',
      content: <DevToolsTab />
    }] : [])
    // News tab temporarily hidden
    // {
    //   id: 'news',
    //   label: 'News',
    //   icon: '📰',
    //   content: <NewsTab />
    // }
  ]

  // Minimized view
  if (minimizedState.isMinimized) {
    return (
      <MinimizedView
        onExpand={expand_plugin}
      />
    )
  }

  // Normal expanded view with tabs
  return (
    <>
      <div className="font-sans w-full h-full flex flex-col relative">
        {/* Top-right buttons */}
        <div className="absolute top-2 right-2 z-50 flex items-center gap-1">
          {/* Settings button */}
          <button
            onClick={settingsModal.openSettings}
            className="w-6 h-6 rounded bg-white/80 hover:bg-white text-black hover:text-gray-800 transition-all duration-200 flex items-center justify-center shadow-sm border border-gray-200 hover:border-gray-300"
            title="Settings"
          >
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </button>

          {/* Minimize button */}
          <button
            onClick={minimize_plugin}
            className="w-6 h-6 rounded bg-white/80 hover:bg-white text-black hover:text-gray-800 transition-all duration-200 flex items-center justify-center shadow-sm border border-gray-200 hover:border-gray-300"
            title="Minimize Plugin"
          >
            <span className="text-sm font-bold leading-none">−</span>
          </button>
        </div>

        <TabSystem
          tabs={tabs}
          activeTab={activeTab.activeTab}
          onTabChange={activeTab.setActiveTab}
        />
      </div>

      {/* Settings Modal */}
      <SettingsModal
        isOpen={settingsModal.showSettings}
        onClose={settingsModal.closeSettings}
      />
    </>
  )
}

// Main App component wrapped with Jotai Provider
export default function App() {
  return (
    <JotaiProvider>
      <AppContent />
    </JotaiProvider>
  )
}
