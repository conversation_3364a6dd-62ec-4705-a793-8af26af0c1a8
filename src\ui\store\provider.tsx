// Jotai Provider setup for the application
// This file sets up the Jotai store and provides it to the React component tree

import React from 'react'
import { Provider } from 'jotai'

interface JotaiProviderProps {
  children: React.ReactNode
}

/**
 * JotaiProvider component that wraps the application with Jotai state management
 *
 * Features:
 * - Provides Jotai store to all child components
 * - Includes development tools for debugging (only in development)
 * - Sets up proper error boundaries for state management
 */
export function JotaiProvider({ children }: JotaiProviderProps) {
  return (
    <Provider>
      {children}
    </Provider>
  )
}

/**
 * Hook to check if we're running in development mode
 * Useful for conditional debugging features
 */
export function useIsDevelopment(): boolean {
  return process.env.NODE_ENV === 'development'
}
