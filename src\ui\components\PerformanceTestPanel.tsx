// Performance test panel for validating chat interface improvements
// Only visible in development mode

import React, { useState, useCallback } from 'react'
import { useChatMessages } from '../store'
import { 
  generateTestMessages, 
  testInputResponsiveness, 
  logPerformanceResults,
  PERFORMANCE_TEST_CONFIG 
} from '../utils/performance_test'

interface PerformanceTestPanelProps {
  inputRef: React.RefObject<HTMLElement>
}

export const PerformanceTestPanel: React.FC<PerformanceTestPanelProps> = ({ inputRef }) => {
  const { setMessages, clearMessages } = useChatMessages()
  const [isRunning, setIsRunning] = useState(false)
  const [results, setResults] = useState<Array<{
    messageCount: number
    avgLatency: number
    maxLatency: number
    status: string
  }>>([])

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  const runPerformanceTest = useCallback(async (messageCount: number) => {
    if (!inputRef.current) {
      console.error('Input ref not available for testing')
      return
    }

    setIsRunning(true)
    
    try {
      // Generate test messages
      console.log(`🧪 Starting performance test with ${messageCount} messages...`)
      const testMessages = generateTestMessages(messageCount)
      setMessages(testMessages)

      // Wait for messages to render
      await new Promise(resolve => setTimeout(resolve, 500))

      // Test input responsiveness
      const testResults = await testInputResponsiveness(
        inputRef.current,
        PERFORMANCE_TEST_CONFIG.TEST_TEXT,
        (progress) => {
          console.log(`⌨️  Typing progress: ${Math.round(progress * 100)}%`)
        }
      )

      // Log and store results
      logPerformanceResults(messageCount, testResults)
      
      const { THRESHOLDS } = PERFORMANCE_TEST_CONFIG
      let status = '🟢 GOOD'
      if (testResults.avgLatency > THRESHOLDS.POOR) {
        status = '🔴 POOR'
      } else if (testResults.avgLatency > THRESHOLDS.ACCEPTABLE) {
        status = '🟡 ACCEPTABLE'
      }

      setResults(prev => [...prev, {
        messageCount,
        avgLatency: testResults.avgLatency,
        maxLatency: testResults.maxLatency,
        status
      }])

    } catch (error) {
      console.error('Performance test failed:', error)
    } finally {
      setIsRunning(false)
    }
  }, [inputRef, setMessages])

  const runAllTests = useCallback(async () => {
    setResults([])
    for (const count of PERFORMANCE_TEST_CONFIG.MESSAGE_COUNTS) {
      await runPerformanceTest(count)
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 1000))
    }
    console.log('🎉 All performance tests completed!')
  }, [runPerformanceTest])

  const clearTestData = useCallback(() => {
    clearMessages()
    setResults([])
  }, [clearMessages])

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-sm z-50">
      <div className="text-sm font-medium text-gray-700 mb-3">
        🧪 Performance Test Panel
      </div>
      
      <div className="space-y-2 mb-3">
        <button
          onClick={runAllTests}
          disabled={isRunning}
          className="w-full px-3 py-2 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
        >
          {isRunning ? 'Running Tests...' : 'Run All Tests'}
        </button>
        
        <div className="grid grid-cols-2 gap-1">
          {PERFORMANCE_TEST_CONFIG.MESSAGE_COUNTS.map(count => (
            <button
              key={count}
              onClick={() => runPerformanceTest(count)}
              disabled={isRunning}
              className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs hover:bg-gray-200 disabled:bg-gray-50 disabled:cursor-not-allowed"
            >
              {count} msgs
            </button>
          ))}
        </div>
        
        <button
          onClick={clearTestData}
          disabled={isRunning}
          className="w-full px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
        >
          Clear Data
        </button>
      </div>

      {results.length > 0 && (
        <div className="border-t border-gray-200 pt-3">
          <div className="text-xs font-medium text-gray-600 mb-2">Results:</div>
          <div className="space-y-1 max-h-32 overflow-y-auto">
            {results.map((result, index) => (
              <div key={index} className="text-xs flex justify-between items-center">
                <span className="text-gray-600">{result.messageCount} msgs:</span>
                <div className="flex items-center gap-1">
                  <span className="text-gray-800">{result.avgLatency.toFixed(1)}ms</span>
                  <span>{result.status.split(' ')[0]}</span>
                </div>
              </div>
            ))}
          </div>
          
          {results.length > 0 && (
            <div className="mt-2 pt-2 border-t border-gray-100">
              <div className="text-xs text-gray-500">
                Thresholds: 🟢 &lt;16ms, 🟡 &lt;33ms, 🔴 &gt;33ms
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default PerformanceTestPanel
