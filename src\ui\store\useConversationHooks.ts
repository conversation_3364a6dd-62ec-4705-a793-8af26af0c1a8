// Conversation Management Hooks
// Hooks for managing conversation list, current conversation, and conversation actions

import { atom, useAtom, useSetAtom } from 'jotai'
import { useCallback } from 'react'
import { Conversation, ConversationSummary } from '../types/ui_types'
import { messagesAtom } from './useChatHooks'
import { aiConversationAtom } from './useAiHooks'

// =============================================================================
// CONVERSATION ATOMS
// =============================================================================

// Conversation management state
export const conversationsAtom = atom<ConversationSummary[]>([])
export const currentConversationIdAtom = atom<string | null>(null)
export const currentConversationAtom = atom<Conversation | null>(null)
export const showConversationManagerAtom = atom<boolean>(false)

// Conversation actions atom (for complex conversation operations)
export const conversationActionsAtom = atom(
  null,
  (get, set, action: {
    type: 'create' | 'switch' | 'delete' | 'clear_current' | 'update_current' | 'update_title'
    payload?: any
  }) => {
    const conversations = get(conversationsAtom)
    const currentConversation = get(currentConversationAtom)
    const currentMessages = get(messagesAtom)
    const currentAiMessages = get(aiConversationAtom)

    switch (action.type) {
      case 'create':
        const newConversation: Conversation = {
          id: `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          title: action.payload?.title || `Conversation ${conversations.length + 1}`,
          messages: [],
          aiMessages: [],
          createdAt: new Date(),
          updatedAt: new Date(),
          previewText: 'New conversation'
        }
        set(currentConversationAtom, newConversation)
        set(currentConversationIdAtom, newConversation.id)

        // Update conversations list
        const newSummary: ConversationSummary = {
          id: newConversation.id,
          title: newConversation.title,
          previewText: newConversation.previewText,
          createdAt: newConversation.createdAt,
          updatedAt: newConversation.updatedAt,
          messageCount: 0
        }
        set(conversationsAtom, [...conversations, newSummary])
        console.log('📝 Conversation: Created new conversation:', newConversation.id)
        break

      case 'update_current':
        if (currentConversation) {
          const updatedConversation: Conversation = {
            ...currentConversation,
            messages: currentMessages,
            aiMessages: currentAiMessages,
            updatedAt: new Date(),
            previewText: currentMessages.length > 0
              ? currentMessages[currentMessages.length - 1].text.substring(0, 50) + '...'
              : 'Empty conversation'
          }
          set(currentConversationAtom, updatedConversation)

          // Update summary in conversations list
          const updatedSummary: ConversationSummary = {
            id: updatedConversation.id,
            title: updatedConversation.title,
            previewText: updatedConversation.previewText,
            createdAt: updatedConversation.createdAt,
            updatedAt: updatedConversation.updatedAt,
            messageCount: currentMessages.length
          }
          set(conversationsAtom, conversations.map(conv =>
            conv.id === updatedConversation.id ? updatedSummary : conv
          ))
        }
        break

      case 'switch':
        const targetConversationId = action.payload.conversationId
        console.log('🔄 Conversation: Switching to conversation:', targetConversationId)
        set(currentConversationIdAtom, targetConversationId)
        console.log('🔄 Conversation: Successfully set current conversation ID to:', targetConversationId)
        break

      case 'delete':
        const conversationId = action.payload.conversationId
        set(conversationsAtom, conversations.filter(conv => conv.id !== conversationId))

        // If deleting current conversation, clear it
        if (get(currentConversationIdAtom) === conversationId) {
          set(currentConversationIdAtom, null)
          set(currentConversationAtom, null)
        }
        console.log('🗑️ Conversation: Deleted conversation:', conversationId)
        break

      case 'clear_current':
        // Clear conversation metadata
        set(currentConversationIdAtom, null)
        set(currentConversationAtom, null)

        // Clear chat messages and AI conversation
        set(messagesAtom, [])
        set(aiConversationAtom, [])

        console.log('🧹 Conversation: Cleared current conversation, messages, and AI conversation')
        break

      case 'update_title':
        if (currentConversation) {
          const updatedConversation = {
            ...currentConversation,
            title: action.payload.title,
            updatedAt: new Date()
          }
          set(currentConversationAtom, updatedConversation)

          // Update summary in conversations list
          const updatedSummary: ConversationSummary = {
            id: updatedConversation.id,
            title: updatedConversation.title,
            previewText: updatedConversation.previewText,
            createdAt: updatedConversation.createdAt,
            updatedAt: updatedConversation.updatedAt,
            messageCount: currentMessages.length
          }
          set(conversationsAtom, conversations.map(conv =>
            conv.id === updatedConversation.id ? updatedSummary : conv
          ))
          console.log('📝 Conversation: Updated title:', action.payload.title)
        }
        break
    }
  }
)

/**
 * Hook for managing conversation list and current conversation
 */
export function useConversationManager() {
  const [conversations, setConversations] = useAtom(conversationsAtom)
  const [currentConversationId, setCurrentConversationId] = useAtom(currentConversationIdAtom)
  const [currentConversation, setCurrentConversation] = useAtom(currentConversationAtom)
  const [showConversationManager, setShowConversationManager] = useAtom(showConversationManagerAtom)
  const executeConversationAction = useSetAtom(conversationActionsAtom)

  const createNewConversation = useCallback((title?: string) => {
    executeConversationAction({ type: 'create', payload: { title } })
  }, [executeConversationAction])

  const switchToConversation = useCallback((conversationId: string) => {
    executeConversationAction({ type: 'switch', payload: { conversationId } })
  }, [executeConversationAction])

  const deleteConversation = useCallback((conversationId: string) => {
    executeConversationAction({ type: 'delete', payload: { conversationId } })
  }, [executeConversationAction])

  const clearCurrentConversation = useCallback(() => {
    executeConversationAction({ type: 'clear_current' })
  }, [executeConversationAction])

  const updateCurrentConversation = useCallback(() => {
    executeConversationAction({ type: 'update_current' })
  }, [executeConversationAction])

  const updateConversationTitle = useCallback((title: string) => {
    executeConversationAction({ type: 'update_title', payload: { title } })
  }, [executeConversationAction])

  const toggleConversationManager = useCallback(() => {
    setShowConversationManager(prev => !prev)
  }, [setShowConversationManager])

  return {
    conversations,
    currentConversationId,
    currentConversation,
    showConversationManager,
    showManager: showConversationManager, // Backward compatibility alias
    setConversations,
    setCurrentConversationId,
    setCurrentConversation,
    setShowConversationManager,
    setShowManager: setShowConversationManager, // Backward compatibility alias
    createNewConversation,
    switchToConversation,
    deleteConversation,
    clearCurrentConversation,
    updateCurrentConversation,
    updateConversationTitle,
    toggleConversationManager,
    toggleManager: toggleConversationManager // Backward compatibility alias
  }
}
