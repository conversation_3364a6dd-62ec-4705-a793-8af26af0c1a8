# Vue.js Syntax Highlighting Test Examples

This file contains various Vue.js code examples to test the syntax highlighting implementation in the Figma plugin.

## Test Case 1: Basic Vue Single File Component

```vue
<template>
  <div class="hello-world">
    <h1 v-if="showTitle">{{ title }}</h1>
    <button @click="toggleTitle">Toggle Title</button>
    <ul>
      <li v-for="item in items" :key="item.id">
        {{ item.name }}
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'HelloWorld',
  data() {
    return {
      title: 'Hello Vue!',
      showTitle: true,
      items: [
        { id: 1, name: 'Item 1' },
        { id: 2, name: 'Item 2' },
        { id: 3, name: 'Item 3' }
      ]
    }
  },
  methods: {
    toggleTitle() {
      this.showTitle = !this.showTitle
    }
  }
}
</script>

<style scoped>
.hello-world {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

h1 {
  color: #42b883;
  font-size: 2rem;
}

button {
  background-color: #42b883;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background-color: #369870;
}
</style>
```

## Test Case 2: Vue 3 Composition API

```vue
<template>
  <div>
    <input v-model="searchTerm" placeholder="Search users..." />
    <div v-for="user in filteredUsers" :key="user.id" class="user-card">
      <h3>{{ user.name }}</h3>
      <p>{{ user.email }}</p>
      <span :class="{ active: user.isActive }">
        {{ user.isActive ? 'Active' : 'Inactive' }}
      </span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

const searchTerm = ref('')
const users = ref([])

const filteredUsers = computed(() => {
  return users.value.filter(user => 
    user.name.toLowerCase().includes(searchTerm.value.toLowerCase())
  )
})

onMounted(async () => {
  try {
    const response = await fetch('/api/users')
    users.value = await response.json()
  } catch (error) {
    console.error('Failed to fetch users:', error)
  }
})
</script>

<style scoped>
.user-card {
  border: 1px solid #ddd;
  padding: 1rem;
  margin: 0.5rem 0;
  border-radius: 8px;
}

.active {
  color: green;
  font-weight: bold;
}
</style>
```

## Test Case 3: Vue with TypeScript

```vue
<template>
  <div class="counter">
    <h2>Counter: {{ count }}</h2>
    <button @click="increment">+</button>
    <button @click="decrement">-</button>
    <button @click="reset">Reset</button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface CounterState {
  count: number
  step: number
}

const count = ref<number>(0)
const step = ref<number>(1)

const doubleCount = computed<number>(() => count.value * 2)

const increment = (): void => {
  count.value += step.value
}

const decrement = (): void => {
  count.value -= step.value
}

const reset = (): void => {
  count.value = 0
}
</script>

<style lang="scss" scoped>
.counter {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  
  h2 {
    color: #2c3e50;
    margin: 0;
  }
  
  button {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    background-color: #3498db;
    color: white;
    cursor: pointer;
    
    &:hover {
      background-color: #2980b9;
    }
    
    &:active {
      transform: translateY(1px);
    }
  }
}
</style>
```

## Test Case 4: Vue with Complex Directives

```vue
<template>
  <div class="todo-app">
    <form @submit.prevent="addTodo">
      <input 
        v-model.trim="newTodo" 
        placeholder="Add a new todo..."
        required
      />
      <button type="submit">Add</button>
    </form>
    
    <div class="filters">
      <button 
        v-for="filter in filters" 
        :key="filter.name"
        :class="{ active: currentFilter === filter.name }"
        @click="currentFilter = filter.name"
      >
        {{ filter.label }}
      </button>
    </div>
    
    <transition-group name="todo" tag="ul" class="todo-list">
      <li 
        v-for="todo in filteredTodos" 
        :key="todo.id"
        :class="{ completed: todo.completed }"
        class="todo-item"
      >
        <input 
          type="checkbox" 
          v-model="todo.completed"
          @change="updateTodo(todo)"
        />
        <span 
          v-if="!todo.editing"
          @dblclick="editTodo(todo)"
          class="todo-text"
        >
          {{ todo.text }}
        </span>
        <input 
          v-else
          v-model="todo.text"
          @blur="finishEdit(todo)"
          @keyup.enter="finishEdit(todo)"
          @keyup.esc="cancelEdit(todo)"
          class="edit-input"
          ref="editInput"
        />
        <button @click="removeTodo(todo.id)" class="remove-btn">×</button>
      </li>
    </transition-group>
  </div>
</template>

<script setup>
import { ref, computed, nextTick } from 'vue'

const newTodo = ref('')
const todos = ref([])
const currentFilter = ref('all')
const editInput = ref(null)

const filters = [
  { name: 'all', label: 'All' },
  { name: 'active', label: 'Active' },
  { name: 'completed', label: 'Completed' }
]

const filteredTodos = computed(() => {
  switch (currentFilter.value) {
    case 'active':
      return todos.value.filter(todo => !todo.completed)
    case 'completed':
      return todos.value.filter(todo => todo.completed)
    default:
      return todos.value
  }
})

const addTodo = () => {
  if (newTodo.value) {
    todos.value.push({
      id: Date.now(),
      text: newTodo.value,
      completed: false,
      editing: false,
      originalText: ''
    })
    newTodo.value = ''
  }
}

const removeTodo = (id) => {
  const index = todos.value.findIndex(todo => todo.id === id)
  if (index > -1) {
    todos.value.splice(index, 1)
  }
}

const editTodo = async (todo) => {
  todo.originalText = todo.text
  todo.editing = true
  await nextTick()
  editInput.value?.focus()
}

const finishEdit = (todo) => {
  todo.editing = false
  todo.text = todo.text.trim()
  if (!todo.text) {
    removeTodo(todo.id)
  }
}

const cancelEdit = (todo) => {
  todo.editing = false
  todo.text = todo.originalText
}

const updateTodo = (todo) => {
  // Could emit event or call API here
  console.log('Todo updated:', todo)
}
</script>

<style scoped>
/* Component styles would go here */
</style>
```

## Comparison: Regular JavaScript (should use Prism.js)

```javascript
// This should be highlighted with Prism.js
const { createApp, ref, computed } = Vue

const app = createApp({
  setup() {
    const count = ref(0)
    const doubleCount = computed(() => count.value * 2)
    
    function increment() {
      count.value++
    }
    
    return {
      count,
      doubleCount,
      increment
    }
  }
})

app.mount('#app')
```

## Comparison: Regular HTML (should use Prism.js)

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue App</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
</head>
<body>
    <div id="app">
        <h1>{{ message }}</h1>
        <button @click="count++">Count: {{ count }}</button>
    </div>
    
    <script>
        const { createApp, ref } = Vue
        
        createApp({
            setup() {
                const message = ref('Hello Vue!')
                const count = ref(0)
                return { message, count }
            }
        }).mount('#app')
    </script>
</body>
</html>
```
