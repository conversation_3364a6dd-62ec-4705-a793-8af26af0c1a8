// Basic test for AI tool calling functionality
// This is a simple test to verify the tools are properly defined and can be executed

import { FIGMA_TOOLS, executeTool, TOOL_EXECUTORS } from './tools'

// Mock the Figma client for testing
const mockFigmaClient = {
  selection: {
    getSelection: async () => {
      return [
        {
          figma: {
            id: 'test-123',
            type: 'FRAME',
            name: 'Test Frame',
            x: 100,
            y: 200,
            width: 300,
            height: 400,
            css: {
              'background-color': '#ffffff',
              'border-radius': '8px',
              'padding': '16px'
            },
            children: [
              {
                figma: {
                  id: 'test-456',
                  type: 'TEXT',
                  name: 'Test Text',
                  characters: 'Hello World',
                  css: {
                    'font-size': '16px',
                    'color': '#333333'
                  }
                }
              }
            ]
          }
        }
      ]
    }
  }
}

// Mock the getFigmaClient function
jest.mock('../figma', () => ({
  getFigmaClient: () => mockFigmaClient
}))

describe('AI Tools', () => {
  test('FIGMA_TOOLS should be properly defined', () => {
    expect(FIGMA_TOOLS).toBeDefined()
    expect(Array.isArray(FIGMA_TOOLS)).toBe(true)
    expect(FIGMA_TOOLS.length).toBe(3)

    // Check each tool has required properties
    FIGMA_TOOLS.forEach(tool => {
      expect(tool.type).toBe('function')
      expect(tool.function.name).toBeDefined()
      expect(tool.function.description).toBeDefined()
      expect(tool.function.parameters).toBeDefined()
    })
  })

  test('Tool names should match expected values', () => {
    const toolNames = FIGMA_TOOLS.map(tool => tool.function.name)
    expect(toolNames).toContain('get_figma_selection_json')
    expect(toolNames).toContain('get_figma_selection_css')
    expect(toolNames).toContain('get_figma_selection_tailwind_html')
  })

  test('TOOL_EXECUTORS should have all tool functions', () => {
    const toolNames = FIGMA_TOOLS.map(tool => tool.function.name)

    toolNames.forEach(name => {
      expect(TOOL_EXECUTORS[name]).toBeDefined()
      expect(typeof TOOL_EXECUTORS[name]).toBe('function')
    })
  })

  test('executeTool should handle unknown tools', async () => {
    const result = await executeTool('unknown_tool')

    expect(result.success).toBe(false)
    expect(result.error).toContain('Unknown tool')
  })

  test('get_figma_selection_json should return JSON data', async () => {
    const result = await executeTool('get_figma_selection_json')

    expect(result.success).toBe(true)
    expect(result.data).toBeDefined()
    expect(Array.isArray(result.data)).toBe(true)
  })

  test('get_figma_selection_css should return CSS string', async () => {
    const result = await executeTool('get_figma_selection_css')

    expect(result.success).toBe(true)
    expect(typeof result.data).toBe('string')
    expect(result.data).toContain('background-color')
  })

  test('get_figma_selection_tailwind_html should return HTML string', async () => {
    const result = await executeTool('get_figma_selection_tailwind_html')

    expect(result.success).toBe(true)
    expect(typeof result.data).toBe('string')
    expect(result.data).toContain('<div')
  })
})

// Manual test function for development
export async function manualTestTools() {
  console.log('🧪 Testing OpenRouter Tools...')

  try {
    // Test JSON tool
    console.log('\n📋 Testing get_figma_selection_json...')
    const jsonResult = await executeTool('get_figma_selection_json')
    console.log('Result:', jsonResult)

    // Test CSS tool
    console.log('\n🎨 Testing get_figma_selection_css...')
    const cssResult = await executeTool('get_figma_selection_css')
    console.log('Result:', cssResult)

    // Test Tailwind HTML tool
    console.log('\n🏗️ Testing get_figma_selection_tailwind_html...')
    const htmlResult = await executeTool('get_figma_selection_tailwind_html')
    console.log('Result:', htmlResult)

    console.log('\n✅ All tools tested successfully!')

    return {
      json: jsonResult,
      css: cssResult,
      html: htmlResult
    }
  } catch (error) {
    console.error('❌ Tool testing failed:', error)
    throw error
  }
}

// Export for use in browser console
if (typeof window !== 'undefined') {
  (window as any).testOpenRouterTools = manualTestTools
}
