// AI State Management Hooks
// Hooks for managing AI vendor configuration, conversation state, and AI progress

import { atom, useAtom } from 'jotai'
import { useCallback } from 'react'
import { OpenRouterConfig, DeepSeekConfig, OpenRouterMessage, AIVendor, DEFAULT_OPENROUTER_CONFIG, DEFAULT_DEEPSEEK_CONFIG } from '../types/ui_types'
import { messagesAtom } from './useChatHooks'

// =============================================================================
// AI ATOMS
// =============================================================================

// Current AI vendor selection - persisted to storage
export const aiVendorAtom = atom<AIVendor>('openrouter')

// OpenRouter configuration - persisted to storage
export const openRouterConfigAtom = atom<OpenRouterConfig>(DEFAULT_OPENROUTER_CONFIG)

// DeepSeek configuration - persisted to storage
export const deepSeekConfigAtom = atom<DeepSeekConfig>(DEFAULT_DEEPSEEK_CONFIG)

// AI conversation state
export const aiConversationAtom = atom<OpenRouterMessage[]>([])

// AI loading state
export const aiLoadingAtom = atom<boolean>(false)

// AI error state
export const aiErrorAtom = atom<string | null>(null)

// AI streaming state - tracks if AI is currently generating a response
export const aiStreamingAtom = atom<boolean>(false)

// AI abort controller - for canceling streaming requests
export const aiAbortControllerAtom = atom<AbortController | null>(null)

// AI Progress Tracking for Dynamic Loading Indicators
export interface AiProgressState {
  isActive: boolean
  phase: 'initializing' | 'thinking' | 'tool_execution' | 'analyzing' | 'completing' | 'timeout' | 'idle'
  statusMessage: string
  currentTool?: string
  toolsExecuted: number
  startTime?: Date
  timeoutMs?: number // Timeout duration in milliseconds (default: 60000)
  isTimedOut?: boolean // Whether the operation has timed out
}

export const aiProgressAtom = atom<AiProgressState>({
  isActive: false,
  phase: 'idle',
  statusMessage: '',
  toolsExecuted: 0
})

/**
 * AI Vendor Selection Hook
 */
export function useAiVendor() {
  const [aiVendor, setAiVendor] = useAtom(aiVendorAtom)

  return {
    aiVendor,
    vendor: aiVendor, // Backward compatibility alias
    setAiVendor,
    isOpenRouter: aiVendor === 'openrouter',
    isDeepSeek: aiVendor === 'deepseek'
  }
}

/**
 * OpenRouter AI Configuration Hook
 */
export function useOpenRouterConfig() {
  const [openRouterConfig, setOpenRouterConfig] = useAtom(openRouterConfigAtom)

  const updateOpenRouterConfig = useCallback((updates: Partial<OpenRouterConfig>) => {
    setOpenRouterConfig(prev => ({ ...prev, ...updates }))
  }, [setOpenRouterConfig])

  return {
    openRouterConfig,
    config: openRouterConfig, // Backward compatibility alias
    setOpenRouterConfig,
    setConfig: setOpenRouterConfig, // Backward compatibility alias
    updateOpenRouterConfig,
    updateConfig: updateOpenRouterConfig // Backward compatibility alias
  }
}

/**
 * DeepSeek AI Configuration Hook
 */
export function useDeepSeekConfig() {
  const [deepSeekConfig, setDeepSeekConfig] = useAtom(deepSeekConfigAtom)

  const updateDeepSeekConfig = useCallback((updates: Partial<DeepSeekConfig>) => {
    setDeepSeekConfig(prev => ({ ...prev, ...updates }))
  }, [setDeepSeekConfig])

  return {
    deepSeekConfig,
    config: deepSeekConfig, // Backward compatibility alias
    setDeepSeekConfig,
    setConfig: setDeepSeekConfig, // Backward compatibility alias
    updateDeepSeekConfig,
    updateConfig: updateDeepSeekConfig // Backward compatibility alias
  }
}

/**
 * AI Conversation Hook
 */
export function useAiConversation() {
  const [aiConversation, setAiConversation] = useAtom(aiConversationAtom)

  const addMessage = useCallback((message: OpenRouterMessage) => {
    setAiConversation(prev => [...prev, message])
  }, [setAiConversation])

  const clearConversation = useCallback(() => {
    setAiConversation([])
  }, [setAiConversation])

  return {
    aiConversation,
    conversation: aiConversation, // Backward compatibility alias
    setAiConversation,
    addMessage,
    clearConversation
  }
}

/**
 * AI State Hook for loading, error, and streaming states
 */
export function useAiState() {
  const [aiLoading, setAiLoading] = useAtom(aiLoadingAtom)
  const [aiError, setAiError] = useAtom(aiErrorAtom)
  const [aiStreaming, setAiStreaming] = useAtom(aiStreamingAtom)
  const [aiAbortController, setAiAbortController] = useAtom(aiAbortControllerAtom)
  const [_aiProgress, setAiProgress] = useAtom(aiProgressAtom)
  const [_messages, setMessages] = useAtom(messagesAtom)

  const clearAiError = useCallback(() => {
    setAiError(null)
  }, [setAiError])

  const abortAiStreaming = useCallback(() => {
    if (aiAbortController) {
      console.log('🛑 [AiHooks] Aborting AI streaming - preserving partial content')

      aiAbortController.abort()
      setAiAbortController(null)
      setAiStreaming(false)
      setAiLoading(false)

      // Reset progress state when aborting
      setAiProgress({
        isActive: false,
        phase: 'idle',
        statusMessage: '',
        toolsExecuted: 0
      })

      // Mark streaming AI messages as interrupted and preserve partial content
      setMessages(prev => prev.map(msg => {
        if (msg.type === 'ai' && (msg.isStreaming || msg.loadingState?.isActive)) {
          console.log(`🛑 [AiHooks] Marking message ${msg.id.slice(-8)} as interrupted`)
          return {
            ...msg,
            isStreaming: false,
            isInterrupted: true, // Mark as interrupted
            loadingState: msg.loadingState ? {
              ...msg.loadingState,
              isActive: false,
              phase: 'completing' as const
            } : undefined
          }
        }
        return msg
      }))
    }
  }, [aiAbortController, setAiAbortController, setAiStreaming, setAiLoading, setAiProgress, setMessages])

  return {
    aiLoading,
    loading: aiLoading, // Backward compatibility alias
    setAiLoading,
    setLoading: setAiLoading, // Backward compatibility alias
    aiError,
    error: aiError, // Backward compatibility alias
    setAiError,
    setError: setAiError, // Backward compatibility alias
    clearAiError,
    clearError: clearAiError, // Backward compatibility alias
    aiStreaming,
    streaming: aiStreaming, // Backward compatibility alias
    setAiStreaming,
    setStreaming: setAiStreaming, // Backward compatibility alias
    aiAbortController,
    setAiAbortController,
    setAbortController: setAiAbortController, // Backward compatibility alias
    abortAiStreaming,
    abortStreaming: abortAiStreaming // Backward compatibility alias
  }
}

/**
 * AI Progress State Hook
 */
export function useAiProgress() {
  const [aiProgress, setAiProgress] = useAtom(aiProgressAtom)

  const updateAiProgress = useCallback((updates: Partial<AiProgressState>) => {
    setAiProgress(prev => ({ ...prev, ...updates }))
  }, [setAiProgress])

  const startAiProgress = useCallback((initialMessage: string = 'Initializing...') => {
    setAiProgress({
      isActive: true,
      phase: 'initializing',
      statusMessage: initialMessage,
      toolsExecuted: 0,
      startTime: new Date()
    })
  }, [setAiProgress])

  const setAiPhase = useCallback((phase: AiProgressState['phase'], statusMessage: string) => {
    updateAiProgress({ phase, statusMessage })
  }, [updateAiProgress])

  const setAiToolExecution = useCallback((toolName: string) => {
    updateAiProgress({
      phase: 'tool_execution',
      currentTool: toolName,
      statusMessage: `🔧 Executing ${toolName}...`,
      toolsExecuted: aiProgress.toolsExecuted + 1
    })
  }, [updateAiProgress, aiProgress.toolsExecuted])

  const completeAiProgress = useCallback(() => {
    setAiProgress({
      isActive: false,
      phase: 'idle',
      statusMessage: '',
      toolsExecuted: 0
    })
  }, [setAiProgress])

  return {
    aiProgress,
    progress: aiProgress, // Backward compatibility alias
    updateAiProgress,
    updateProgress: updateAiProgress, // Backward compatibility alias
    startAiProgress,
    startProgress: startAiProgress, // Backward compatibility alias
    setAiPhase,
    setPhase: setAiPhase, // Backward compatibility alias
    setAiToolExecution,
    setToolExecution: setAiToolExecution, // Backward compatibility alias
    completeAiProgress,
    completeProgress: completeAiProgress // Backward compatibility alias
  }
}
