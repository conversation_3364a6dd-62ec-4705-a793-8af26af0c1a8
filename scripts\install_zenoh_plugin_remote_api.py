#!/usr/bin/env python3
"""
Figmagent Zenoh Plugin Installation Script
Detects zenohd location and installs zenoh_plugin_remote_api.dll to the same directory
"""

import os
import shutil
import subprocess
import sys
from pathlib import Path


def detect_zenohd_location():
    """Detect the location of zenohd executable"""
    try:
        # Try to find zenohd using 'which' command
        result = subprocess.run(['which', 'zenohd'],
                              capture_output=True,
                              text=True,
                              check=True)
        zenohd_path_str = result.stdout.strip()

        # Convert Unix-style path to Windows path if needed
        if zenohd_path_str.startswith('/d/'):
            zenohd_path_str = zenohd_path_str.replace('/d/', 'D:/', 1)
            zenohd_path_str = zenohd_path_str.replace('/', '\\')

        zenohd_path = Path(zenohd_path_str)
        return zenohd_path.parent
    except (subprocess.CalledProcessError, FileNotFoundError):
        # If 'which' fails, try 'where' command (Windows)
        try:
            result = subprocess.run(['where', 'zenohd'],
                                  capture_output=True,
                                  text=True,
                                  check=True)
            zenohd_path = Path(result.stdout.strip().split('\n')[0])
            return zenohd_path.parent
        except (subprocess.CalledProcessError, FileNotFoundError):
            return None


def main():
    print("🚀 Installing Zenoh Plugin Remote API")
    print("=" * 40)

    # Get project root directory (parent of scripts directory)
    script_dir = Path(__file__).parent
    project_root = script_dir.parent

    # Define source path
    source_dll = project_root / "deps" / "zenoh_plugin_remote_api.dll"

    print(f"📁 Project root: {project_root}")
    print(f"📦 Source: {source_dll}")

    # Detect zenohd location
    print("🔍 Detecting zenohd location...")
    zenohd_dir = detect_zenohd_location()

    if zenohd_dir is None:
        print("❌ Error: zenohd command not found in PATH")
        print("   Please ensure zenohd is installed and available in PATH")
        print("   You can install it using: conda install -c conda-forge zenoh")
        sys.exit(1)

    target_dll = zenohd_dir / "zenoh_plugin_remote_api.dll"
    print(f"🎯 Target: {target_dll}")
    print(f"📍 zenohd directory: {zenohd_dir}")

    # Check if source file exists
    if not source_dll.exists():
        print(f"❌ Error: Source file not found: {source_dll}")
        print("   Please ensure zenoh_plugin_remote_api.dll exists in deps/ directory")
        sys.exit(1)

    # Check if target directory exists and is writable
    if not zenohd_dir.exists():
        print(f"❌ Error: Target directory not found: {zenohd_dir}")
        print("   zenohd directory does not exist")
        sys.exit(1)

    if not os.access(zenohd_dir, os.W_OK):
        print(f"❌ Error: No write permission to directory: {zenohd_dir}")
        print("   Please run with appropriate permissions")
        sys.exit(1)

    # Copy the file
    try:
        print("📋 Copying zenoh_plugin_remote_api.dll...")
        shutil.copy2(source_dll, target_dll)

        # Verify copy was successful
        if target_dll.exists():
            file_size = target_dll.stat().st_size
            print(f"✅ Successfully copied! ({file_size:,} bytes)")
        else:
            print("❌ Copy failed - target file not found")
            sys.exit(1)

    except Exception as e:
        print(f"❌ Error during copy: {e}")
        sys.exit(1)

    print("\n🎉 Installation completed successfully!")
    print("   Zenoh daemon can now load the remote API plugin")


if __name__ == "__main__":
    main()
