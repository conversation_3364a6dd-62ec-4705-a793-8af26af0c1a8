// DeepSeek API Client
// Provides a clean interface for DeepSeek API interactions

import {
  DeepSeekConfig,
  OpenRouterMessage,
  OpenRouterResponse,
  OpenRouterStreamChunk,
  OpenRouterToolCall,
  ToolExecutionResult
} from '../../ui/types/ui_types'
import { FIGMA_TOOLS, executeTool } from '../openrouter/tools'

export class DeepSeekClient {
  private baseUrl = 'https://api.deepseek.com/v1'
  private config: DeepSeekConfig
  private enableTools: boolean

  constructor(config: DeepSeekConfig, enableTools: boolean = true) {
    this.config = config
    // DeepSeek supports OpenAI-style tool calling for all models
    this.enableTools = enableTools && this.supportsToolCalling()
    console.log(`🔧 [DeepSeek] Client initialized with tools ${this.enableTools ? 'enabled' : 'disabled'}`)
  }

  // Check if the current model supports tool calling
  private supportsToolCalling(): boolean {
    // According to DeepSeek API docs, all current models support tool calling
    const toolSupportedModels = [
      'deepseek-chat',
      'deepseek-coder',
      'deepseek-reasoner'
    ]

    const supportsTools = toolSupportedModels.includes(this.config.model)
    if (!supportsTools) {
      console.log(`🔧 [DeepSeek] Model ${this.config.model} does not support tool calling`)
    } else {
      console.log(`🔧 [DeepSeek] Model ${this.config.model} supports tool calling`)
    }
    return supportsTools
  }

  // Update configuration
  updateConfig(config: DeepSeekConfig) {
    this.config = config
    // Re-check tool support when config changes
    this.enableTools = this.enableTools && this.supportsToolCalling()
  }

  // Validate API key format
  private validateApiKey(): boolean {
    return this.config.apiKey && this.config.apiKey.length > 0
  }

  // Create request headers
  private createHeaders(): Record<string, string> {
    return {
      'Authorization': `Bearer ${this.config.apiKey}`,
      'Content-Type': 'application/json'
    }
  }

  // Format messages for DeepSeek API
  private formatMessages(userMessage: string, conversation: OpenRouterMessage[]): OpenRouterMessage[] {
    const messages: OpenRouterMessage[] = []

    // Add system prompt if provided
    if (this.config.systemPrompt) {
      messages.push({
        role: 'system',
        content: this.config.systemPrompt
      })
    }

    // Add conversation history
    messages.push(...conversation)

    // Add current user message
    messages.push({
      role: 'user',
      content: userMessage
    })

    // Validate message ordering for tool calling
    this.validateMessageOrdering(messages)

    return messages
  }

  // Validate message ordering according to DeepSeek API requirements
  private validateMessageOrdering(messages: OpenRouterMessage[]): void {
    for (let i = 0; i < messages.length; i++) {
      const message = messages[i]

      // If this is an assistant message with tool_calls, ensure the next message(s) are tool responses
      if (message.role === 'assistant' && message.tool_calls && message.tool_calls.length > 0) {
        const toolCallIds = message.tool_calls.map(tc => tc.id)
        let nextIndex = i + 1

        console.log(`🔧 [DeepSeek] Validating tool calls at message ${i}:`, {
          toolCallIds,
          expectedNextIndex: nextIndex,
          totalMessages: messages.length
        })

        // Check that all tool calls have corresponding tool messages
        for (const toolCallId of toolCallIds) {
          if (nextIndex >= messages.length) {
            console.warn(`🔧 [DeepSeek] Missing tool message for tool_call_id: ${toolCallId}`)
            continue
          }

          const nextMessage = messages[nextIndex]
          if (nextMessage.role !== 'tool' || nextMessage.tool_call_id !== toolCallId) {
            console.warn(`🔧 [DeepSeek] Invalid message ordering at index ${nextIndex}:`, {
              expected: { role: 'tool', tool_call_id: toolCallId },
              actual: { role: nextMessage.role, tool_call_id: nextMessage.tool_call_id },
              messageContent: nextMessage.content?.substring(0, 100)
            })
          } else {
            console.log(`🔧 [DeepSeek] Valid tool message found:`, {
              tool_call_id: toolCallId,
              messageIndex: nextIndex
            })
          }
          nextIndex++
        }
      }
    }
  }

  // Check message ordering for validation (returns boolean)
  private checkMessageOrdering(messages: OpenRouterMessage[]): boolean {
    try {
      for (let i = 0; i < messages.length; i++) {
        const message = messages[i]

        // If this is an assistant message with tool_calls, ensure the next message(s) are tool responses
        if (message.role === 'assistant' && message.tool_calls && message.tool_calls.length > 0) {
          const toolCallIds = message.tool_calls.map(tc => tc.id)
          let nextIndex = i + 1

          // Check that all tool calls have corresponding tool messages
          for (const toolCallId of toolCallIds) {
            if (nextIndex >= messages.length) {
              return false
            }

            const nextMessage = messages[nextIndex]
            if (nextMessage.role !== 'tool' || nextMessage.tool_call_id !== toolCallId) {
              return false
            }
            nextIndex++
          }
        }
      }
      return true
    } catch (error) {
      return false
    }
  }

  // Format tool execution result for DeepSeek API
  private formatToolResult(result: ToolExecutionResult, toolName: string): string {
    if (!result.success) {
      return `Error: ${result.error}`
    }

    // For successful results, format based on tool type
    if (typeof result.data === 'string') {
      return result.data
    }

    // For Figma tools, we need to provide the actual data but in a format DeepSeek can handle
    if (toolName === 'get_figma_selection_json') {
      // Return the JSON data as a string, but compact format to avoid issues
      try {
        return JSON.stringify(result.data)
      } catch (error) {
        return `Figma selection data retrieved but could not be serialized: ${error}`
      }
    } else if (toolName === 'get_figma_selection_css') {
      // CSS data should be returned as-is since it's already a string
      return String(result.data)
    } else if (toolName === 'get_figma_selection_tailwind_html') {
      // HTML data should be returned as-is since it's already a string
      return String(result.data)
    }

    // For other data types, try to serialize as JSON first
    try {
      return JSON.stringify(result.data)
    } catch (error) {
      // If JSON serialization fails, provide a summary
      if (Array.isArray(result.data)) {
        return `Array with ${result.data.length} items`
      } else if (result.data && typeof result.data === 'object') {
        return `Object with keys: ${Object.keys(result.data).join(', ')}`
      }
      return String(result.data)
    }
  }

  // Format conversation messages for API request (without adding new user message)
  private formatConversationMessages(conversation: OpenRouterMessage[]): OpenRouterMessage[] {
    const messages: OpenRouterMessage[] = []

    // Add system prompt if configured
    if (this.config.systemPrompt) {
      messages.push({
        role: 'system',
        content: this.config.systemPrompt
      })
    }

    // Add conversation history (keep last 10 messages for context)
    const recentConversation = conversation.slice(-10)
    messages.push(...recentConversation)

    // Log detailed message information for debugging
    console.log(`🔧 [DeepSeek] Formatted conversation messages:`, {
      totalMessages: messages.length,
      messageRoles: messages.map(m => m.role),
      toolCallsCount: messages.filter(m => m.role === 'assistant' && m.tool_calls).length,
      toolMessagesCount: messages.filter(m => m.role === 'tool').length,
      lastFewMessages: messages.slice(-5).map(m => ({
        role: m.role,
        hasToolCalls: !!(m.role === 'assistant' && m.tool_calls),
        toolCallId: m.role === 'tool' ? m.tool_call_id : undefined,
        contentPreview: m.content?.substring(0, 100) + (m.content?.length > 100 ? '...' : '')
      }))
    })

    // Validate message ordering for tool calling
    this.validateMessageOrdering(messages)

    return messages
  }

  // Send chat completion request
  async sendMessage(
    userMessage: string,
    conversation: OpenRouterMessage[] = []
  ): Promise<OpenRouterResponse> {
    if (!this.validateApiKey()) {
      throw new Error('DeepSeek API key is required')
    }

    const messages = this.formatMessages(userMessage, conversation)

    const request = {
      model: this.config.model,
      messages,
      temperature: this.config.temperature,
      max_tokens: this.config.maxTokens,
      stream: false,
      ...(this.enableTools && { tools: FIGMA_TOOLS, tool_choice: 'auto' })
    }

    console.log(`🔧 [DeepSeek] Non-streaming request:`, {
      model: request.model,
      messagesCount: request.messages.length,
      toolsEnabled: this.enableTools,
      toolsCount: this.enableTools ? FIGMA_TOOLS.length : 0,
      hasToolChoice: !!request.tool_choice
    })

    try {
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: this.createHeaders(),
        body: JSON.stringify(request)
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        console.error('🔧 [DeepSeek] Non-Streaming API Error Details:', {
          status: response.status,
          statusText: response.statusText,
          errorData,
          requestBody: JSON.stringify(request, null, 2),
          toolsEnabled: this.enableTools,
          toolsCount: this.enableTools ? FIGMA_TOOLS.length : 0
        })

        // Enhanced error analysis for 422 errors to help debug tool calling issues
        if (response.status === 422) {
          console.error('🔧 [DeepSeek] Non-Streaming 422 Error Analysis:', {
            hasTools: !!request.tools,
            toolsArray: request.tools,
            toolChoice: request.tool_choice,
            messagesCount: request.messages?.length,
            lastMessage: request.messages?.[request.messages.length - 1],
            messageRoles: request.messages?.map(m => m.role),
            toolCallsInMessages: request.messages?.filter(m => m.role === 'assistant' && m.tool_calls).length,
            toolMessagesCount: request.messages?.filter(m => m.role === 'tool').length,
            errorDetails: errorData
          })

          // Check for common tool calling issues
          if (request.tools && request.tools.length > 0) {
            console.error('🔧 [DeepSeek] Tool calling validation:', {
              toolsValid: request.tools.every(t => t.type === 'function' && t.function?.name),
              toolChoiceValid: ['auto', 'none', 'required'].includes(request.tool_choice as string) ||
                              (typeof request.tool_choice === 'object' && request.tool_choice?.type === 'function'),
              messageOrderingValid: this.checkMessageOrdering(request.messages || [])
            })
          }
        }

        // Provide more specific error messages for common issues
        let errorMessage = `DeepSeek API error: ${response.status} ${response.statusText}`
        if (errorData.error?.message) {
          errorMessage += `. ${errorData.error.message}`
        } else if (response.status === 422) {
          errorMessage += '. This may be due to invalid request format or tool calling configuration. Check console for detailed analysis.'
        } else {
          errorMessage += '. Unknown error'
        }

        throw new Error(errorMessage)
      }

      const data: OpenRouterResponse = await response.json()

      // Handle tool calls if present
      if (data.choices[0]?.message?.tool_calls) {
        await this.handleToolCalls(data.choices[0].message.tool_calls, conversation)
      }

      return data
    } catch (error) {
      if (error instanceof Error) {
        throw error
      }
      throw new Error(`Network error: ${String(error)}`)
    }
  }

  // Send multi-turn chat completion request with tool calling support
  async sendMultiTurnMessage(
    userMessage: string,
    conversation: AiMessage[] = [],
    maxTurns: number = 5,
    onToolCall?: (toolCall: AiToolCall, result: ToolExecutionResult) => void,
    onTurnComplete?: (turn: number, response: AiResponse) => void
  ): Promise<AiResponse> {
    console.log(`🔄 [DeepSeek] Starting multi-turn conversation (max ${maxTurns} turns)`)

    if (!this.validateApiKey()) {
      throw new Error('DeepSeek API key is required')
    }

    // Create a working copy of the conversation
    const workingConversation = [...conversation]
    let currentTurn = 0
    let lastResponse: OpenRouterResponse | null = null

    // Add the initial user message
    const userOpenRouterMessage: OpenRouterMessage = {
      role: 'user',
      content: userMessage
    }
    workingConversation.push(userOpenRouterMessage)

    while (currentTurn < maxTurns) {
      currentTurn++
      console.log(`🔄 [DeepSeek] Turn ${currentTurn}/${maxTurns}`)

      const messages = workingConversation

      const request = {
        model: this.config.model,
        messages,
        temperature: this.config.temperature,
        max_tokens: this.config.maxTokens,
        stream: false,
        ...(this.enableTools && { tools: FIGMA_TOOLS, tool_choice: 'auto' })
      }

      console.log(`🔧 [DeepSeek] Multi-turn request (turn ${currentTurn}):`, {
        model: request.model,
        messagesCount: request.messages.length,
        toolsEnabled: this.enableTools,
        toolsCount: this.enableTools ? FIGMA_TOOLS.length : 0,
        hasToolChoice: !!request.tool_choice
      })

      try {
        const response = await fetch(`${this.baseUrl}/chat/completions`, {
          method: 'POST',
          headers: this.createHeaders(),
          body: JSON.stringify(request)
        })

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}))
          console.error('🔧 [DeepSeek] Multi-Turn API Error Details:', {
            status: response.status,
            statusText: response.statusText,
            errorData,
            requestBody: JSON.stringify(request, null, 2),
            toolsEnabled: this.enableTools,
            toolsCount: this.enableTools ? FIGMA_TOOLS.length : 0,
            turn: currentTurn
          })

          // Enhanced error analysis for 422 errors to help debug tool calling issues
          if (response.status === 422) {
            console.error('🔧 [DeepSeek] Multi-Turn 422 Error Analysis:', {
              hasTools: !!request.tools,
              toolsArray: request.tools,
              toolChoice: request.tool_choice,
              messagesCount: request.messages?.length,
              lastMessage: request.messages?.[request.messages.length - 1],
              turn: currentTurn,
              messageRoles: request.messages?.map(m => m.role),
              toolCallsInMessages: request.messages?.filter(m => m.role === 'assistant' && m.tool_calls).length,
              toolMessagesCount: request.messages?.filter(m => m.role === 'tool').length,
              errorDetails: errorData
            })

            // Check for common tool calling issues
            if (request.tools && request.tools.length > 0) {
              console.error('🔧 [DeepSeek] Multi-turn tool calling validation:', {
                toolsValid: request.tools.every(t => t.type === 'function' && t.function?.name),
                toolChoiceValid: ['auto', 'none', 'required'].includes(request.tool_choice as string) ||
                                (typeof request.tool_choice === 'object' && request.tool_choice?.type === 'function'),
                messageOrderingValid: this.checkMessageOrdering(request.messages || [])
              })
            }
          }

          // Provide more specific error messages for common issues
          let errorMessage = `DeepSeek API error: ${response.status} ${response.statusText}`
          if (errorData.error?.message) {
            errorMessage += `. ${errorData.error.message}`
          } else if (response.status === 422) {
            errorMessage += '. This may be due to invalid request format or tool calling configuration. Check console for detailed analysis.'
          } else {
            errorMessage += '. Unknown error'
          }

          throw new Error(errorMessage)
        }

        const data: AiResponse = await response.json()
        lastResponse = data

        // Add AI response to working conversation
        const aiMessage = data.choices[0]?.message
        if (aiMessage) {
          const aiAiMessage: AiMessage = {
            role: 'assistant',
            content: aiMessage.content || '',
            ...(aiMessage.tool_calls && { tool_calls: aiMessage.tool_calls })
          }
          workingConversation.push(aiAiMessage)

          // If this message has tool calls, notify about the AI's intent
          if (aiMessage.tool_calls && aiMessage.tool_calls.length > 0 && aiMessage.content) {
            console.log(`🤔 [DeepSeek] Turn ${currentTurn}: AI reasoning: "${aiMessage.content}"`)
          }
        }

        // Notify about turn completion
        if (onTurnComplete) {
          onTurnComplete(currentTurn, data)
        }

        // Handle tool calls if present
        if (data.choices[0]?.message?.tool_calls) {
          console.log(`🔧 [DeepSeek] Turn ${currentTurn}: Executing ${data.choices[0].message.tool_calls.length} tool calls`)

          const hasToolResults = await this.handleMultiTurnToolCalls(
            data.choices[0].message.tool_calls,
            workingConversation,
            onToolCall
          )

          // If we executed tools, continue to next turn for AI to analyze results
          if (hasToolResults) {
            continue
          }
        }

        // No tool calls, conversation is complete
        console.log(`✅ [DeepSeek] Multi-turn conversation completed in ${currentTurn} turns`)
        break

      } catch (error) {
        console.error(`❌ [DeepSeek] Turn ${currentTurn} failed:`, error)
        if (error instanceof Error) {
          throw error
        }
        throw new Error(`Network error: ${String(error)}`)
      }
    }

    if (currentTurn >= maxTurns) {
      console.warn(`⚠️ [DeepSeek] Multi-turn conversation reached max turns limit (${maxTurns})`)
    }

    // Update the original conversation with all messages
    conversation.splice(0, conversation.length, ...workingConversation)

    return lastResponse || {
      id: 'fallback-response',
      model: this.config.model,
      choices: [{ message: { role: 'assistant', content: 'No response' }, finish_reason: 'error' }],
      usage: { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 }
    }
  }

  // Send multi-turn streaming chat completion request with tool calling support
  async sendMultiTurnStreamingMessage(
    userMessage: string,
    conversation: OpenRouterMessage[] = [],
    maxTurns: number = 5,
    onChunk: (chunk: string) => void,
    onComplete: () => void,
    onError: (error: Error) => void,
    abortController?: AbortController,
    onToolCall?: (toolCall: OpenRouterToolCall, result: ToolExecutionResult) => void,
    onTurnComplete?: (turn: number, hasToolCalls: boolean) => void
  ): Promise<void> {
    console.log(`🔄 [DeepSeek] Starting multi-turn streaming conversation (max ${maxTurns} turns)`)

    if (!this.validateApiKey()) {
      onError(new Error('DeepSeek API key is required'))
      return
    }

    // Create working conversation copy
    const workingConversation = [...conversation]

    // Add user message to working conversation
    const userOpenRouterMessage: OpenRouterMessage = {
      role: 'user',
      content: userMessage
    }
    workingConversation.push(userOpenRouterMessage)

    let currentTurn = 1

    while (currentTurn <= maxTurns) {
      if (abortController?.signal.aborted) {
        console.log('🛑 [DeepSeek] Multi-turn streaming conversation aborted')
        return
      }

      console.log(`🔄 [DeepSeek] Turn ${currentTurn}/${maxTurns}`)

      try {
        const hasToolCalls = await this.executeSingleStreamingTurn(
          workingConversation,
          onChunk,
          onError,
          abortController,
          onToolCall
        )

        // Notify about turn completion
        if (onTurnComplete) {
          onTurnComplete(currentTurn, hasToolCalls)
        }

        // If no tool calls were made, conversation is complete
        if (!hasToolCalls) {
          console.log(`✅ [DeepSeek] Multi-turn streaming conversation completed in ${currentTurn} turns`)
          break
        }

        // Continue to next turn for AI to analyze tool results
        currentTurn++
      } catch (error) {
        console.error(`❌ [DeepSeek] Turn ${currentTurn} failed:`, error)
        onError(error instanceof Error ? error : new Error(String(error)))
        return
      }
    }

    if (currentTurn > maxTurns) {
      console.warn(`⚠️ [DeepSeek] Multi-turn streaming conversation reached max turns limit (${maxTurns})`)
    }

    // Update the original conversation with all messages
    conversation.splice(0, conversation.length, ...workingConversation)
    onComplete()
  }

  // Execute a single streaming turn and return whether tool calls were made
  private async executeSingleStreamingTurn(
    conversation: OpenRouterMessage[],
    onChunk: (chunk: string) => void,
    onError: (error: Error) => void,
    abortController?: AbortController,
    onToolCall?: (toolCall: OpenRouterToolCall, result: ToolExecutionResult) => void
  ): Promise<boolean> {
    const messages = this.formatConversationMessages(conversation)

    const request = {
      model: this.config.model,
      messages,
      temperature: this.config.temperature,
      max_tokens: this.config.maxTokens,
      stream: true,
      ...(this.enableTools && { tools: FIGMA_TOOLS, tool_choice: 'auto' })
    }

    console.log(`🔧 [DeepSeek] Streaming request:`, {
      model: request.model,
      messagesCount: request.messages.length,
      toolsEnabled: this.enableTools,
      toolsCount: this.enableTools ? FIGMA_TOOLS.length : 0,
      hasToolChoice: !!request.tool_choice,
      availableTools: this.enableTools ? FIGMA_TOOLS.map(t => t.function.name) : []
    })

    try {
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: this.createHeaders(),
        body: JSON.stringify(request),
        signal: abortController?.signal
      })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      console.error('🔧 [DeepSeek] API Error Details:', {
        status: response.status,
        statusText: response.statusText,
        errorData,
        requestBody: JSON.stringify(request, null, 2),
        toolsEnabled: this.enableTools,
        toolsCount: this.enableTools ? FIGMA_TOOLS.length : 0
      })

      // Enhanced error analysis for 422 errors to help debug tool calling issues
      if (response.status === 422) {
        console.error('🔧 [DeepSeek] Streaming 422 Error Analysis:', {
          hasTools: !!request.tools,
          toolsArray: request.tools,
          toolChoice: request.tool_choice,
          messagesCount: request.messages?.length,
          lastMessage: request.messages?.[request.messages.length - 1],
          messageRoles: request.messages?.map(m => m.role),
          toolCallsInMessages: request.messages?.filter(m => m.role === 'assistant' && m.tool_calls).length,
          toolMessagesCount: request.messages?.filter(m => m.role === 'tool').length,
          errorDetails: errorData
        })

        // Check for common tool calling issues
        if (request.tools && request.tools.length > 0) {
          console.error('🔧 [DeepSeek] Streaming tool calling validation:', {
            toolsValid: request.tools.every(t => t.type === 'function' && t.function?.name),
            toolChoiceValid: ['auto', 'none', 'required'].includes(request.tool_choice as string) ||
                            (typeof request.tool_choice === 'object' && request.tool_choice?.type === 'function'),
            messageOrderingValid: this.checkMessageOrdering(request.messages || [])
          })
        }
      }

      // Provide more specific error messages for common issues
      let errorMessage = `DeepSeek API error: ${response.status} ${response.statusText}`
      if (errorData.error?.message) {
        errorMessage += `. ${errorData.error.message}`
      } else if (response.status === 422) {
        errorMessage += '. This may be due to invalid request format or tool calling configuration. Check console for detailed analysis.'
      } else {
        errorMessage += `. ${JSON.stringify(errorData) || 'Unknown error'}`
      }

      throw new Error(errorMessage)
    }

    if (!response.body) {
      throw new Error('No response body received')
    }

    const reader = response.body.getReader()
    const decoder = new TextDecoder()
    let buffer = ''
    let accumulatedToolCalls: Record<string, Partial<OpenRouterToolCall>> = {}
    let aiContent = ''

    try {
      while (true) {
        if (abortController?.signal.aborted) {
          console.log('🛑 [DeepSeek] Streaming turn aborted')
          break
        }

        const { done, value } = await reader.read()
        if (done) break

        buffer += decoder.decode(value, { stream: true })
        const lines = buffer.split('\n')
        buffer = lines.pop() || ''

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6).trim()
            if (data === '[DONE]') {
              // Add AI response to conversation FIRST (before executing tools)
              let hasToolCalls = false
              if (aiContent.trim() || Object.keys(accumulatedToolCalls).length > 0) {
                // Filter out invalid tool calls before creating the AI message
                const validToolCalls = Object.values(accumulatedToolCalls)
                  .filter(tc => tc.id && tc.id !== 'undefined' && tc.function?.name)
                  .map(tc => ({
                    id: tc.id!,
                    type: 'function' as const,
                    function: tc.function!
                  }))

                console.log(`🔧 [DeepSeek] Adding AI message with tool calls:`, {
                  hasContent: !!aiContent.trim(),
                  totalAccumulated: Object.keys(accumulatedToolCalls).length,
                  validToolCallsCount: validToolCalls.length,
                  validToolCallIds: validToolCalls.map(tc => tc.id),
                  invalidToolCalls: Object.values(accumulatedToolCalls)
                    .filter(tc => !tc.id || tc.id === 'undefined' || !tc.function?.name)
                    .map(tc => ({ id: tc.id, functionName: tc.function?.name }))
                })

                const aiMessage: OpenRouterMessage = {
                  role: 'assistant',
                  content: aiContent,
                  ...(validToolCalls.length > 0 && { tool_calls: validToolCalls })
                }
                conversation.push(aiMessage)
              }

              // Then execute any accumulated tool calls (this adds tool messages after assistant message)
              if (Object.keys(accumulatedToolCalls).length > 0) {
                hasToolCalls = await this.executeAccumulatedToolCallsForMultiTurn(
                  accumulatedToolCalls,
                  conversation,
                  onToolCall
                )
              }

              return hasToolCalls
            }

            // Skip empty data chunks
            if (!data || data.trim() === '') {
              continue
            }

            try {
              const chunk: OpenRouterStreamChunk = JSON.parse(data)
              const delta = chunk.choices[0]?.delta

              // Handle content chunks
              if (delta?.content) {
                aiContent += delta.content
                onChunk(delta.content)
              }

              // Handle tool call chunks - DeepSeek follows OpenAI format
              if (delta?.tool_calls) {
                this.accumulateToolCalls(delta.tool_calls, accumulatedToolCalls)
              }
            } catch (parseError) {
              console.warn('🔧 [DeepSeek] Failed to parse streaming chunk:', {
                line,
                data,
                error: parseError
              })
            }
          }
        }
      }

      // Handle case where stream ended without [DONE]
      if (!abortController?.signal.aborted) {
        // Add AI response to conversation FIRST (before executing tools)
        let hasToolCalls = false
        if (aiContent.trim() || Object.keys(accumulatedToolCalls).length > 0) {
          // Filter out invalid tool calls before creating the AI message
          const validToolCalls = Object.values(accumulatedToolCalls)
            .filter(tc => tc.id && tc.id !== 'undefined' && tc.function?.name)
            .map(tc => ({
              id: tc.id!,
              type: 'function' as const,
              function: tc.function!
            }))

          console.log(`🔧 [DeepSeek] Stream ended - Adding AI message with tool calls:`, {
            hasContent: !!aiContent.trim(),
            totalAccumulated: Object.keys(accumulatedToolCalls).length,
            validToolCallsCount: validToolCalls.length,
            validToolCallIds: validToolCalls.map(tc => tc.id)
          })

          const aiMessage: OpenRouterMessage = {
            role: 'assistant',
            content: aiContent,
            ...(validToolCalls.length > 0 && { tool_calls: validToolCalls })
          }
          conversation.push(aiMessage)
        }

        // Then execute any accumulated tool calls (this adds tool messages after assistant message)
        if (Object.keys(accumulatedToolCalls).length > 0) {
          hasToolCalls = await this.executeAccumulatedToolCallsForMultiTurn(
            accumulatedToolCalls,
            conversation,
            onToolCall
          )
        }

        return hasToolCalls
      }

      return false
    } finally {
      reader.releaseLock()
    }
    } catch (error) {
      // Don't call onError if the request was aborted intentionally
      if (error instanceof Error && error.name === 'AbortError') {
        console.log('🛑 [DeepSeek] Multi-turn streaming turn was aborted')
        return false
      }
      onError(error instanceof Error ? error : new Error(String(error)))
      return false
    }
  }

  // Send streaming chat completion request (legacy single-turn version)
  async sendStreamingMessage(
    userMessage: string,
    conversation: OpenRouterMessage[] = [],
    onChunk: (chunk: string) => void,
    onComplete: () => void,
    onError: (error: Error) => void,
    abortController?: AbortController,
    onToolCall?: (toolCall: OpenRouterToolCall, result: ToolExecutionResult) => void
  ): Promise<void> {
    console.log(`🤖 [DeepSeek] Streaming ${this.config.model} (${conversation.length} msgs)`)

    if (!this.validateApiKey()) {
      onError(new Error('DeepSeek API key is required'))
      return
    }

    const messages = this.formatMessages(userMessage, conversation)

    const request = {
      model: this.config.model,
      messages,
      temperature: this.config.temperature,
      max_tokens: this.config.maxTokens,
      stream: true,
      ...(this.enableTools && { tools: FIGMA_TOOLS, tool_choice: 'auto' })
    }

    console.log(`🔧 [DeepSeek] Legacy streaming request:`, {
      model: request.model,
      messagesCount: request.messages.length,
      toolsEnabled: this.enableTools,
      toolsCount: this.enableTools ? FIGMA_TOOLS.length : 0,
      hasToolChoice: !!request.tool_choice
    })

    try {
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: this.createHeaders(),
        body: JSON.stringify(request),
        signal: abortController?.signal
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        console.error('🔧 [DeepSeek] Legacy Streaming API Error Details:', {
          status: response.status,
          statusText: response.statusText,
          errorData,
          requestBody: JSON.stringify(request, null, 2),
          toolsEnabled: this.enableTools,
          toolsCount: this.enableTools ? FIGMA_TOOLS.length : 0
        })

        // Enhanced error analysis for 422 errors to help debug tool calling issues
        if (response.status === 422) {
          console.error('🔧 [DeepSeek] Legacy Streaming 422 Error Analysis:', {
            hasTools: !!request.tools,
            toolsArray: request.tools,
            toolChoice: request.tool_choice,
            messagesCount: request.messages?.length,
            lastMessage: request.messages?.[request.messages.length - 1],
            messageRoles: request.messages?.map(m => m.role),
            toolCallsInMessages: request.messages?.filter(m => m.role === 'assistant' && m.tool_calls).length,
            toolMessagesCount: request.messages?.filter(m => m.role === 'tool').length,
            errorDetails: errorData
          })

          // Check for common tool calling issues
          if (request.tools && request.tools.length > 0) {
            console.error('🔧 [DeepSeek] Legacy streaming tool calling validation:', {
              toolsValid: request.tools.every(t => t.type === 'function' && t.function?.name),
              toolChoiceValid: ['auto', 'none', 'required'].includes(request.tool_choice as string) ||
                              (typeof request.tool_choice === 'object' && request.tool_choice?.type === 'function'),
              messageOrderingValid: this.checkMessageOrdering(request.messages || [])
            })
          }
        }

        // Provide more specific error messages for common issues
        let errorMessage = `DeepSeek API error: ${response.status} ${response.statusText}`
        if (errorData.error?.message) {
          errorMessage += `. ${errorData.error.message}`
        } else if (response.status === 422) {
          errorMessage += '. This may be due to invalid request format or tool calling configuration. Check console for detailed analysis.'
        } else {
          errorMessage += '. Unknown error'
        }

        throw new Error(errorMessage)
      }

      if (!response.body) {
        throw new Error('No response body received')
      }

      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let buffer = ''
      let accumulatedToolCalls: Record<string, Partial<OpenRouterToolCall>> = {}

      try {
        while (true) {
          // Check if request was aborted
          if (abortController?.signal.aborted) {
            console.log('🛑 [DeepSeekClient] Streaming request aborted')
            break
          }

          const { done, value } = await reader.read()
          if (done) break

          buffer += decoder.decode(value, { stream: true })
          const lines = buffer.split('\n')
          buffer = lines.pop() || ''

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6).trim()
              if (data === '[DONE]') {
                // Execute any accumulated tool calls before completing
                await this.executeAccumulatedToolCalls(accumulatedToolCalls, conversation, onToolCall)
                onComplete()
                return
              }

              // Skip empty data chunks
              if (!data || data.trim() === '') {
                continue
              }

              try {
                const chunk: OpenRouterStreamChunk = JSON.parse(data)
                const delta = chunk.choices[0]?.delta

                // Handle content chunks
                if (delta?.content) {
                  onChunk(delta.content)
                }

                // Handle tool call chunks - DeepSeek follows OpenAI format
                if (delta?.tool_calls) {
                  this.accumulateToolCalls(delta.tool_calls, accumulatedToolCalls)
                }
              } catch (parseError) {
                console.warn('🔧 [DeepSeek] Failed to parse legacy streaming chunk:', {
                  line,
                  data,
                  error: parseError
                })
              }
            }
          }
        }

        // Execute any accumulated tool calls and complete if not aborted
        if (!abortController?.signal.aborted) {
          await this.executeAccumulatedToolCalls(accumulatedToolCalls, conversation, onToolCall)
          onComplete()
        }
      } catch (streamError) {
        if (!abortController?.signal.aborted) {
          onError(streamError instanceof Error ? streamError : new Error(String(streamError)))
        }
      } finally {
        reader.releaseLock()
      }
    } catch (error) {
      if (!abortController?.signal.aborted) {
        onError(error instanceof Error ? error : new Error(String(error)))
      }
    }
  }

  // Accumulate tool call chunks during streaming
  private accumulateToolCalls(
    toolCallDeltas: OpenRouterToolCall[],
    accumulated: Record<string, Partial<OpenRouterToolCall>>
  ): void {
    for (const delta of toolCallDeltas) {
      // Validate that the tool call has a valid ID
      if (!delta.id || delta.id === 'undefined' || typeof delta.id !== 'string') {
        console.warn(`🔧 [DeepSeek] Invalid tool call ID in delta:`, {
          deltaId: delta.id,
          deltaType: typeof delta.id,
          delta: delta,
          functionName: delta.function?.name
        })
        continue // Skip this invalid tool call
      }

      console.log(`🔧 [DeepSeek] Processing tool call delta:`, {
        id: delta.id,
        type: delta.type,
        functionName: delta.function?.name,
        hasArguments: !!delta.function?.arguments,
        argumentsLength: delta.function?.arguments?.length || 0
      })

      if (!accumulated[delta.id]) {
        // Initialize new tool call
        accumulated[delta.id] = {
          id: delta.id,
          type: delta.type || 'function',
          function: {
            name: delta.function?.name || '',
            arguments: delta.function?.arguments || ''
          }
        }
        console.log(`🔧 [DeepSeek] Initialized new tool call:`, {
          id: delta.id,
          functionName: delta.function?.name
        })
      } else {
        // Accumulate function arguments and name
        if (delta.function?.arguments) {
          accumulated[delta.id].function!.arguments += delta.function.arguments
        }
        if (delta.function?.name) {
          accumulated[delta.id].function!.name = delta.function.name
        }
        console.log(`🔧 [DeepSeek] Updated existing tool call:`, {
          id: delta.id,
          totalArgumentsLength: accumulated[delta.id].function?.arguments?.length || 0,
          functionName: accumulated[delta.id].function?.name
        })
      }
    }

    // Log final accumulated state
    console.log(`🔧 [DeepSeek] Final accumulated tool calls:`, {
      count: Object.keys(accumulated).length,
      ids: Object.keys(accumulated),
      details: Object.entries(accumulated).map(([id, tc]) => ({
        id,
        functionName: tc.function?.name,
        argumentsLength: tc.function?.arguments?.length || 0,
        isComplete: !!(tc.function?.name && tc.function?.arguments !== undefined)
      }))
    })
  }

  // Execute accumulated tool calls for multi-turn conversations
  private async executeAccumulatedToolCallsForMultiTurn(
    accumulated: Record<string, Partial<OpenRouterToolCall>>,
    conversation: OpenRouterMessage[],
    onToolCall?: (toolCall: OpenRouterToolCall, result: ToolExecutionResult) => void
  ): Promise<boolean> {
    let hasResults = false

    console.log(`🔧 [DeepSeek] Executing accumulated tool calls:`, {
      totalAccumulated: Object.keys(accumulated).length,
      accumulatedIds: Object.keys(accumulated)
    })

    for (const [id, partialToolCall] of Object.entries(accumulated)) {
      // Validate tool call completeness and ID
      if (!id || id === 'undefined' || !partialToolCall.function?.name || partialToolCall.function?.arguments === undefined) {
        console.warn(`🔧 [DeepSeek] Skipping invalid tool call:`, {
          id,
          hasName: !!partialToolCall.function?.name,
          hasArguments: partialToolCall.function?.arguments !== undefined,
          functionName: partialToolCall.function?.name
        })
        continue
      }

      const toolCall: OpenRouterToolCall = {
        id,
        type: 'function',
        function: {
          name: partialToolCall.function.name,
          arguments: partialToolCall.function.arguments
        }
      }

      console.log(`🔧 [DeepSeek] Executing tool call:`, {
        id: toolCall.id,
        functionName: toolCall.function.name,
        argumentsLength: toolCall.function.arguments.length
      })

      try {
        // Parse arguments and execute tool
        const args = JSON.parse(toolCall.function.arguments || '{}')
        const result = await executeTool(toolCall.function.name, args)
        hasResults = true

        // Notify callback if provided
        if (onToolCall) {
          onToolCall(toolCall, result)
        }

        // Add tool result to conversation with proper formatting
        const toolMessage: OpenRouterMessage = {
          role: 'tool',
          content: this.formatToolResult(result, toolCall.function.name),
          tool_call_id: toolCall.id
        }

        console.log(`🔧 [DeepSeek] Adding tool message:`, {
          tool_call_id: toolCall.id,
          toolName: toolCall.function.name,
          contentLength: toolMessage.content.length,
          success: result.success
        })

        conversation.push(toolMessage)
      } catch (error) {
        console.error('Tool execution failed:', error)
        hasResults = true

        const errorResult: ToolExecutionResult = {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        }

        // Notify callback if provided
        if (onToolCall) {
          onToolCall(toolCall, errorResult)
        }

        // Add error message to conversation
        const errorMessage: OpenRouterMessage = {
          role: 'tool',
          content: `Error executing tool: ${errorResult.error}`,
          tool_call_id: toolCall.id
        }

        conversation.push(errorMessage)
      }
    }

    return hasResults;
  }

  // Execute accumulated tool calls (legacy single-turn version)
  private async executeAccumulatedToolCalls(
    accumulated: Record<string, Partial<OpenRouterToolCall>>,
    conversation: OpenRouterMessage[],
    onToolCall?: (toolCall: OpenRouterToolCall, result: ToolExecutionResult) => void
  ): Promise<void> {
    for (const [id, partialToolCall] of Object.entries(accumulated)) {
      if (partialToolCall.function?.name && partialToolCall.function?.arguments !== undefined) {
        const toolCall: OpenRouterToolCall = {
          id,
          type: 'function',
          function: {
            name: partialToolCall.function.name,
            arguments: partialToolCall.function.arguments
          }
        }

        try {
          // Parse arguments and execute tool
          const args = JSON.parse(toolCall.function.arguments || '{}')
          const result = await executeTool(toolCall.function.name, args)

          // Notify callback if provided
          if (onToolCall) {
            onToolCall(toolCall, result)
          }

          // Add tool result to conversation with proper formatting
          const toolMessage: OpenRouterMessage = {
            role: 'tool',
            content: this.formatToolResult(result, toolCall.function.name),
            tool_call_id: toolCall.id
          }

          console.log(`🔧 [DeepSeek] Adding legacy tool message:`, {
            tool_call_id: toolCall.id,
            toolName: toolCall.function.name,
            contentLength: toolMessage.content.length,
            success: result.success
          })

          conversation.push(toolMessage)
        } catch (error) {
          console.error('Tool execution failed:', error)

          const errorResult: ToolExecutionResult = {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          }

          // Notify callback if provided
          if (onToolCall) {
            onToolCall(toolCall, errorResult)
          }

          // Add error message to conversation
          const errorMessage: OpenRouterMessage = {
            role: 'tool',
            content: `Error executing tool: ${errorResult.error}`,
            tool_call_id: toolCall.id
          }

          conversation.push(errorMessage)
        }
      }
    }
  }

  // Handle tool calls from AI response
  private async handleToolCalls(
    toolCalls: OpenRouterToolCall[],
    conversation: OpenRouterMessage[]
  ): Promise<void> {
    for (const toolCall of toolCalls) {
      if (toolCall.type === 'function') {
        try {
          // Parse arguments (they come as JSON string)
          const args = JSON.parse(toolCall.function.arguments || '{}')

          // Execute the tool
          const result = await executeTool(toolCall.function.name, args)

          // Add tool result to conversation with proper formatting
          const toolMessage: OpenRouterMessage = {
            role: 'tool',
            content: this.formatToolResult(result, toolCall.function.name),
            tool_call_id: toolCall.id
          }

          console.log(`🔧 [DeepSeek] Adding handleToolCalls message:`, {
            tool_call_id: toolCall.id,
            toolName: toolCall.function.name,
            contentLength: toolMessage.content.length,
            success: result.success
          })

          conversation.push(toolMessage)
        } catch (error) {
          console.error('Tool execution failed:', error)

          // Add error message to conversation
          const errorMessage: OpenRouterMessage = {
            role: 'tool',
            content: `Error executing tool: ${error instanceof Error ? error.message : 'Unknown error'}`,
            tool_call_id: toolCall.id
          }

          conversation.push(errorMessage)
        }
      }
    }
  }

  // Handle tool calls for multi-turn conversations
  private async handleMultiTurnToolCalls(
    toolCalls: OpenRouterToolCall[],
    conversation: OpenRouterMessage[],
    onToolCall?: (toolCall: OpenRouterToolCall, result: ToolExecutionResult) => void
  ): Promise<boolean> {
    let hasResults = false

    for (const toolCall of toolCalls) {
      if (toolCall.type === 'function') {
        try {
          // Parse arguments (they come as JSON string)
          const args = JSON.parse(toolCall.function.arguments || '{}')

          // Execute the tool
          const result = await executeTool(toolCall.function.name, args)
          hasResults = true

          // Notify callback if provided
          if (onToolCall) {
            onToolCall(toolCall, result)
          }

          // Add tool result to conversation with proper formatting
          const toolMessage: OpenRouterMessage = {
            role: 'tool',
            content: this.formatToolResult(result, toolCall.function.name),
            tool_call_id: toolCall.id
          }

          console.log(`🔧 [DeepSeek] Adding multi-turn tool message:`, {
            tool_call_id: toolCall.id,
            toolName: toolCall.function.name,
            contentLength: toolMessage.content.length,
            success: result.success
          })

          conversation.push(toolMessage)
        } catch (error) {
          console.error('Tool execution failed:', error)
          hasResults = true

          const errorResult: ToolExecutionResult = {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          }

          // Notify callback if provided
          if (onToolCall) {
            onToolCall(toolCall, errorResult)
          }

          // Add error result to conversation
          const errorMessage: OpenRouterMessage = {
            role: 'tool',
            content: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
            tool_call_id: toolCall.id
          }

          conversation.push(errorMessage)
        }
      }
    }

    return hasResults
  }

  // Test API connection
  async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      await this.sendMessage('Hello, this is a test message.')
      return { success: true }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  // Get available models (static list for now)
  getAvailableModels() {
    return [
      'deepseek-chat',
      'deepseek-coder',
      'deepseek-reasoner'
    ]
  }
}

// Singleton instance
let deepSeekClient: DeepSeekClient | null = null

export function getDeepSeekClient(config: DeepSeekConfig): DeepSeekClient {
  if (!deepSeekClient) {
    deepSeekClient = new DeepSeekClient(config)
  } else {
    deepSeekClient.updateConfig(config)
  }
  return deepSeekClient
}
