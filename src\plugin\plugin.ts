// FigProxy - Figma Plugin with Zenoh Chat
// This plugin provides a chat interface using Zenoh messaging.

// This file holds the main code for plugins. Code in this file has access to
// the *figma document* via the figma global object.
// You can access browser APIs in the <script> tag inside "ui.html" which has a
// full browser environment (See https://www.figma.com/plugin-docs/how-plugins-run).

import {
  UIMessage,
  MESSAGE_TYPES,
  SetWindowSizeMessage,
  GetFigmaSelectionMessage,
  StorageGetMessage,
  StorageSetMessage,
  StorageDeleteMessage,
  DEFAULT_WINDOW_SETTINGS,
  STORAGE_KEYS,
  WindowSettings
} from '../lib/figma';
import { FIGMA_NODE_TYPES, getNodeProperties, hasNodeProperty } from './figma_types';
import {
  handleGetFigmaSelection,
  handleSetWindowSize,
  handlePluginClose,
  handleStorageGet,
  handleStorageSet,
  handleStorageDelete
} from './message_handlers';

// FigProxy plugin - now with auto-generated node type definitions
console.log('📋 Available Figma node types:', FIGMA_NODE_TYPES.length);
console.log('🔧 Frame properties:', getNodeProperties('FRAME').slice(0, 10), '...');
console.log('✅ Text has characters property:', hasNodeProperty('TEXT', 'characters'));

// Load saved window settings and initialize UI
async function initializePlugin() {
  try {
    // Try to load saved window settings using the same key as UI
    console.log('Loading window settings with key:', STORAGE_KEYS.WINDOW_SETTINGS)
    const savedSettings = await figma.clientStorage.getAsync(STORAGE_KEYS.WINDOW_SETTINGS)
    let windowSettings: WindowSettings = DEFAULT_WINDOW_SETTINGS

    if (savedSettings) {
      try {
        windowSettings = JSON.parse(savedSettings)
        console.log('✅ Loaded saved window settings:', windowSettings)
      } catch (error) {
        console.warn('⚠️ Failed to parse saved settings, using defaults:', error)
        windowSettings = DEFAULT_WINDOW_SETTINGS
      }
    } else {
      console.log('ℹ️ No saved settings found, using defaults:', windowSettings)
    }

    // Show UI with saved or default settings
    console.log('🚀 Initializing plugin with window size:', windowSettings.width, 'x', windowSettings.height)
    figma.showUI(__html__, {
      width: windowSettings.width,
      height: windowSettings.height
    })
  } catch (error) {
    console.error('❌ Failed to load settings, using defaults:', error)
    // Fallback to default settings
    figma.showUI(__html__, {
      width: DEFAULT_WINDOW_SETTINGS.width,
      height: DEFAULT_WINDOW_SETTINGS.height
    })
  }
}

// Initialize the plugin
initializePlugin()

// Calls to "parent.postMessage" from within the HTML page will trigger this
// callback. The callback will be passed the "pluginMessage" property of the
// posted message.
figma.ui.onmessage = async (msg: UIMessage) => {
  console.log('🔄 [Plugin] Received message from UI:', {
    type: msg.type,
    request_id: msg.request_id,
    hasRequestId: !!msg.request_id,
    fullMessage: msg
  });

  try {
    // Handle different types of messages sent from the HTML page
    if (msg.type === MESSAGE_TYPES.SET_WINDOW_SIZE) {
      console.log('🪟 [Plugin] Processing set window size message');
      const sizeMsg = msg as SetWindowSizeMessage;
      handleSetWindowSize(sizeMsg.width, sizeMsg.height);
      return;
    } else if (msg.type === MESSAGE_TYPES.GET_FIGMA_SELECTION) {
      console.log('🎯 [Plugin] Processing get figma selection message');
      const selectionMsg = msg as GetFigmaSelectionMessage;
      console.log('📋 [Plugin] Selection request_id:', selectionMsg.request_id);
      await handleGetFigmaSelection(selectionMsg.request_id);
      return;
    } else if (msg.type === MESSAGE_TYPES.STORAGE_GET) {
      console.log('💾 [Plugin] Processing storage get message');
      const getMsg = msg as StorageGetMessage;
      await handleStorageGet(getMsg.key, getMsg.request_id);
      return;
    } else if (msg.type === MESSAGE_TYPES.STORAGE_SET) {
      console.log('💾 [Plugin] Processing storage set message');
      const setMsg = msg as StorageSetMessage;
      await handleStorageSet(setMsg.key, setMsg.value, setMsg.request_id);
      return;
    } else if (msg.type === MESSAGE_TYPES.STORAGE_DELETE) {
      console.log('💾 [Plugin] Processing storage delete message');
      const deleteMsg = msg as StorageDeleteMessage;
      await handleStorageDelete(deleteMsg.key, deleteMsg.request_id);
      return;
    } else if (msg.type === MESSAGE_TYPES.CANCEL) {
      console.log('❌ [Plugin] Processing cancel message');
      handlePluginClose();
    } else {
      console.warn('⚠️ [Plugin] Unknown message type:', msg.type);
    }
  } catch (error) {
    console.error('💥 [Plugin] Error processing message:', {
      type: msg.type,
      request_id: msg.request_id,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
  }
};
