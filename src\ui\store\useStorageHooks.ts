// Generic Storage Hooks
// Consolidated storage functionality for all configuration types

import { useCallback } from 'react'
import { useMount, useAsyncFn } from 'react-use'
import { getFigmaClient, STORAGE_KEYS } from '../../lib/figma'
import {
  AIVendor,
  OpenRouterConfig,
  DeepSeekConfig,
  TerminologySettings,
  CodeBlockSettings,
  DEFAULT_OPENROUTER_CONFIG,
  DEFAULT_DEEPSEEK_CONFIG,
  DEFAULT_TERMINOLOGY,
  DEFAULT_CODE_BLOCK_SETTINGS
} from '../types/ui_types'
import {
  useAiVendor,
  useOpenRouterConfig,
  useDeepSeekConfig
} from './useAiHooks'
// useTerminology is now defined locally in this file
import { atom, useAtom } from 'jotai'

// =============================================================================
// GENERIC STORAGE HOOK
// =============================================================================

interface StorageHookConfig<T> {
  storageKey: string
  defaultValue: T
  validator?: (value: unknown) => value is T
  merger?: (defaultValue: T, storedValue: Partial<T>) => T
}

/**
 * Generic storage hook for any configuration type
 * Provides load, save, update functionality with error handling and loading states
 */
function useGenericStorage<T>(
  config: StorageHookConfig<T>,
  currentValue: T,
  setValue: (value: T) => void
) {
  const { storageKey, defaultValue, validator, merger } = config

  // Load from storage
  const [loadState, loadFromStorage] = useAsyncFn(async () => {
    try {
      const figmaClient = getFigmaClient()
      const storedValue = await figmaClient.storage.getItem(storageKey, null)

      if (storedValue) {
        // The storage interface (MockStorage or FigmaStorage) already handles JSON parsing
        // So we can use the value directly
        const parsedValue = storedValue

        // Apply validation if provided
        if (validator && !validator(parsedValue)) {
          console.warn(`⚠️ Storage: Invalid data for ${storageKey}, using defaults`)
          setValue(defaultValue)
          return
        }

        // Merge with defaults if merger provided, otherwise use parsed value directly
        const finalValue = merger
          ? merger(defaultValue, parsedValue)
          : parsedValue

        setValue(finalValue)
        console.log(`🔄 Storage: Loaded ${storageKey} from storage:`, finalValue)
      }
    } catch (error) {
      console.error(`❌ Storage: Failed to load ${storageKey} from storage:`, error)
    }
  }, [storageKey, defaultValue, validator, merger, setValue])

  // Save to storage
  const [saveState, saveToStorage] = useAsyncFn(async (value: T) => {
    try {
      const figmaClient = getFigmaClient()
      await figmaClient.storage.setItem(storageKey, value)
      console.log(`💾 Storage: Saved ${storageKey} to storage`)
    } catch (error) {
      console.error(`❌ Storage: Failed to save ${storageKey} to storage:`, error)
    }
  }, [storageKey])

  // Auto-save when value changes
  const saveValue = useCallback((value: T) => {
    setValue(value)
    saveToStorage(value)
  }, [setValue, saveToStorage])

  // Update specific fields
  const updateValue = useCallback((updates: Partial<T>) => {
    const newValue = { ...currentValue, ...updates }
    saveValue(newValue)
  }, [currentValue, saveValue])

  // Load on mount
  useMount(() => {
    loadFromStorage()
  })

  return {
    isLoading: loadState.loading,
    isSaving: saveState.loading,
    loadError: loadState.error,
    saveError: saveState.error,
    reload: loadFromStorage,
    saveValue,
    updateValue
  }
}

// =============================================================================
// SPECIFIC STORAGE HOOKS
// =============================================================================

/**
 * AI Vendor Storage Hook
 * Manages persistence of AI vendor selection to Figma clientStorage
 */
export function useVendorStorage() {
  const { aiVendor, vendor, setAiVendor } = useAiVendor()

  const storage = useGenericStorage(
    {
      storageKey: STORAGE_KEYS.AI_VENDOR,
      defaultValue: 'openrouter' as AIVendor,
      validator: (value): value is AIVendor =>
        typeof value === 'string' && ['openrouter', 'deepseek'].includes(value)
    },
    aiVendor,
    setAiVendor
  )

  return {
    aiVendor,
    vendor, // Backward compatibility alias
    setAiVendor: storage.saveValue,
    saveVendor: storage.saveValue, // Backward compatibility alias
    loadingVendor: storage.isLoading, // Backward compatibility alias
    ...storage
  }
}

/**
 * OpenRouter Storage Hook
 * Manages persistence of OpenRouter configuration to Figma clientStorage
 */
export function useOpenRouterStorage() {
  const { openRouterConfig, config, setOpenRouterConfig } = useOpenRouterConfig()

  const storage = useGenericStorage(
    {
      storageKey: STORAGE_KEYS.OPENROUTER_CONFIG,
      defaultValue: DEFAULT_OPENROUTER_CONFIG,
      merger: (defaults, stored) => ({ ...defaults, ...stored })
    },
    openRouterConfig,
    setOpenRouterConfig
  )

  return {
    openRouterConfig,
    config, // Backward compatibility alias
    setOpenRouterConfig: storage.saveValue,
    updateConfig: storage.updateValue,
    loadingConfig: storage.isLoading, // Backward compatibility alias
    ...storage
  }
}

/**
 * DeepSeek Storage Hook
 * Manages persistence of DeepSeek configuration to Figma clientStorage
 */
export function useDeepSeekStorage() {
  const { deepSeekConfig, config, setDeepSeekConfig } = useDeepSeekConfig()

  const storage = useGenericStorage(
    {
      storageKey: STORAGE_KEYS.DEEPSEEK_CONFIG,
      defaultValue: DEFAULT_DEEPSEEK_CONFIG,
      merger: (defaults, stored) => ({ ...defaults, ...stored })
    },
    deepSeekConfig,
    setDeepSeekConfig
  )

  return {
    deepSeekConfig,
    config, // Backward compatibility alias
    setDeepSeekConfig: storage.saveValue,
    updateConfig: storage.updateValue,
    loadingConfig: storage.isLoading, // Backward compatibility alias
    ...storage
  }
}

/**
 * Terminology Storage Hook
 * Manages persistence of terminology settings to Figma clientStorage
 */
export function useTerminologyStorage() {
  const { terminology, setTerminology } = useTerminology()

  const storage = useGenericStorage(
    {
      storageKey: STORAGE_KEYS.TERMINOLOGY,
      defaultValue: DEFAULT_TERMINOLOGY,
      merger: (defaults, stored) => ({ ...defaults, ...stored })
    },
    terminology,
    setTerminology
  )

  return {
    terminology,
    setTerminology: storage.saveValue,
    saveTerminology: storage.saveValue, // Backward compatibility alias
    updateTerminology: storage.updateValue,
    ...storage
  }
}

/**
 * Code Block Settings Storage Hook
 * Manages persistence of code block display settings to Figma clientStorage
 */
export function useCodeBlockSettingsStorage() {
  const { codeBlockSettings, setCodeBlockSettings } = useCodeBlockSettings()

  const storage = useGenericStorage(
    {
      storageKey: STORAGE_KEYS.CODE_BLOCK_SETTINGS,
      defaultValue: DEFAULT_CODE_BLOCK_SETTINGS,
      merger: (defaults, stored) => ({ ...defaults, ...stored })
    },
    codeBlockSettings,
    setCodeBlockSettings
  )

  return {
    codeBlockSettings,
    setCodeBlockSettings: storage.saveValue,
    updateCodeBlockSettings: storage.updateValue,
    ...storage
  }
}

// =============================================================================
// TERMINOLOGY MANAGEMENT (Merged from useTerminologyHooks.ts)
// =============================================================================

// Terminology settings - persisted to storage
export const terminologyAtom = atom<TerminologySettings>(DEFAULT_TERMINOLOGY)

/**
 * Terminology Configuration Hook
 * Manages customizable terminology settings (Agent/Executor names)
 * Merged from useTerminologyHooks.ts for consolidation
 */
export function useTerminology() {
  const [terminology, setTerminology] = useAtom(terminologyAtom)

  const updateTerminology = useCallback((updates: Partial<TerminologySettings>) => {
    setTerminology(prev => ({ ...prev, ...updates }))
  }, [setTerminology])

  return {
    terminology,
    setTerminology,
    updateTerminology
  }
}

// =============================================================================
// CODE BLOCK SETTINGS MANAGEMENT
// =============================================================================

// Code block settings - persisted to storage
export const codeBlockSettingsAtom = atom<CodeBlockSettings>(DEFAULT_CODE_BLOCK_SETTINGS)

/**
 * Code Block Settings Configuration Hook
 * Manages customizable code block display settings (max height, virtualization, etc.)
 */
export function useCodeBlockSettings() {
  const [codeBlockSettings, setCodeBlockSettings] = useAtom(codeBlockSettingsAtom)

  const updateCodeBlockSettings = useCallback((updates: Partial<CodeBlockSettings>) => {
    setCodeBlockSettings(prev => ({ ...prev, ...updates }))
  }, [setCodeBlockSettings])

  return {
    codeBlockSettings,
    setCodeBlockSettings,
    updateCodeBlockSettings
  }
}
