!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("jotai/vanilla"),require("jotai/vanilla/internals")):"function"==typeof define&&define.amd?define(["exports","react","jotai/vanilla","jotai/vanilla/internals"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).jotaiReact={},e.<PERSON>act,e.jotai<PERSON>anilla,e.jotaiVanillaInternals)}(this,(function(e,t,n,r){"use strict";var u=t.createContext(void 0);function i(e){var r=t.useContext(u);return(null==e?void 0:e.store)||r||n.getDefaultStore()}var o=function(e){return"function"==typeof(null==e?void 0:e.then)},a=function(e){e.status||(e.status="pending",e.then((function(t){e.status="fulfilled",e.value=t}),(function(t){e.status="rejected",e.reason=t})))},s=t.use||function(e){if("pending"===e.status)throw e;if("fulfilled"===e.status)return e.value;throw"rejected"===e.status?e.reason:(a(e),e)},c=new WeakMap,f=function(e,t){var n=c.get(e);return n||(n=new Promise((function(u,i){var a=e,s=function(e){return function(t){a===e&&u(t)}},f=function(e){return function(t){a===e&&i(t)}},l=function(){try{var e=t();o(e)?(c.set(e,n),a=e,e.then(s(e),f(e)),r.INTERNAL_registerAbortHandler(e,l)):u(e)}catch(e){i(e)}};e.then(s(e),f(e)),r.INTERNAL_registerAbortHandler(e,l)})),c.set(e,n)),n};function l(e,n){var r=n||{},u=r.delay,c=r.unstable_promiseStatus,l=void 0===c?!t.use:c,v=i(n),d=t.useReducer((function(t){var n=v.get(e);return Object.is(t[0],n)&&t[1]===v&&t[2]===e?t:[n,v,e]}),void 0,(function(){return[v.get(e),v,e]})),g=d[0],p=g[0],h=g[1],b=g[2],j=d[1],y=p;if(h===v&&b===e||(j(),y=v.get(e)),t.useEffect((function(){var t=v.sub(e,(function(){if(l)try{var t=v.get(e);o(t)&&a(f(t,(function(){return v.get(e)})))}catch(e){}"number"!=typeof u?j():setTimeout(j,u)}));return j(),t}),[v,e,u,l]),t.useDebugValue(y),o(y)){var m=f(y,(function(){return v.get(e)}));return l&&a(m),s(m)}return y}function v(e,n){var r=i(n),u=t.useCallback((function(){for(var t=arguments.length,n=new Array(t),u=0;u<t;u++)n[u]=arguments[u];return r.set.apply(r,[e].concat(n))}),[r,e]);return u}e.Provider=function(e){var r=e.children,i=e.store,o=t.useRef(void 0);return i||o.current||(o.current=n.createStore()),t.createElement(u.Provider,{value:i||o.current},r)},e.useAtom=function(e,t){return[l(e,t),v(e,t)]},e.useAtomValue=l,e.useSetAtom=v,e.useStore=i}));
