/**
 * Test to verify conversation message order for tool calling
 * This ensures that tool messages always follow assistant messages with tool_calls
 */

import type { OpenRouterMessage } from '../ui/types/ui_types'

// Validate conversation message order for tool calling
export function validateConversationOrder(conversation: OpenRouterMessage[]): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []
  
  for (let i = 0; i < conversation.length; i++) {
    const message = conversation[i]
    
    if (message.role === 'tool') {
      // Tool messages must have tool_call_id
      if (!message.tool_call_id) {
        errors.push(`Message ${i}: tool message missing tool_call_id`)
        continue
      }
      
      // Find the preceding assistant message with tool_calls
      let foundPrecedingToolCall = false
      for (let j = i - 1; j >= 0; j--) {
        const prevMessage = conversation[j]
        
        if (prevMessage.role === 'assistant' && prevMessage.tool_calls) {
          // Check if this tool_call_id exists in the assistant's tool_calls
          const matchingToolCall = prevMessage.tool_calls.find(
            tc => tc.id === message.tool_call_id
          )
          
          if (matchingTool<PERSON>all) {
            foundPrecedingToolCall = true
            break
          }
        }
        
        // If we hit another assistant message without tool_calls, stop looking
        if (prevMessage.role === 'assistant' && !prevMessage.tool_calls) {
          break
        }
      }
      
      if (!foundPrecedingToolCall) {
        errors.push(
          `Message ${i}: tool message with tool_call_id "${message.tool_call_id}" ` +
          `has no preceding assistant message with matching tool_calls`
        )
      }
    }
    
    if (message.role === 'assistant' && message.tool_calls) {
      // Verify tool_calls have unique IDs
      const toolCallIds = message.tool_calls.map(tc => tc.id)
      const uniqueIds = new Set(toolCallIds)
      
      if (toolCallIds.length !== uniqueIds.size) {
        errors.push(`Message ${i}: assistant message has duplicate tool_call IDs`)
      }
      
      // Verify each tool_call has required fields
      message.tool_calls.forEach((toolCall, tcIndex) => {
        if (!toolCall.id) {
          errors.push(`Message ${i}, tool_call ${tcIndex}: missing id`)
        }
        if (!toolCall.function?.name) {
          errors.push(`Message ${i}, tool_call ${tcIndex}: missing function.name`)
        }
        if (toolCall.function?.arguments === undefined) {
          errors.push(`Message ${i}, tool_call ${tcIndex}: missing function.arguments`)
        }
      })
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

// Test function to verify the conversation order
export function testConversationOrder() {
  console.log('🧪 Testing conversation message order validation...')
  
  // Test case 1: Valid conversation
  const validConversation: OpenRouterMessage[] = [
    { role: 'user', content: 'Analyze my selection' },
    { 
      role: 'assistant', 
      content: 'I\'ll analyze your selection.',
      tool_calls: [
        {
          id: 'call_1',
          type: 'function',
          function: { name: 'get_figma_selection_json', arguments: '{}' }
        }
      ]
    },
    { 
      role: 'tool', 
      content: '{"type": "RECTANGLE"}',
      tool_call_id: 'call_1'
    },
    {
      role: 'assistant',
      content: 'Based on your selection, I can see you have a rectangle...'
    }
  ]
  
  const validResult = validateConversationOrder(validConversation)
  console.log('✅ Valid conversation test:', validResult.isValid ? 'PASSED' : 'FAILED')
  if (!validResult.isValid) {
    console.log('Errors:', validResult.errors)
  }
  
  // Test case 2: Invalid conversation (tool message without preceding assistant tool_calls)
  const invalidConversation: OpenRouterMessage[] = [
    { role: 'user', content: 'Analyze my selection' },
    { role: 'assistant', content: 'I\'ll analyze your selection.' },
    { 
      role: 'tool', 
      content: '{"type": "RECTANGLE"}',
      tool_call_id: 'call_1'
    }
  ]
  
  const invalidResult = validateConversationOrder(invalidConversation)
  console.log('❌ Invalid conversation test:', !invalidResult.isValid ? 'PASSED' : 'FAILED')
  if (!invalidResult.isValid) {
    console.log('Expected errors found:', invalidResult.errors)
  }
  
  // Test case 3: Tool message with wrong tool_call_id
  const wrongIdConversation: OpenRouterMessage[] = [
    { role: 'user', content: 'Analyze my selection' },
    { 
      role: 'assistant', 
      content: 'I\'ll analyze your selection.',
      tool_calls: [
        {
          id: 'call_1',
          type: 'function',
          function: { name: 'get_figma_selection_json', arguments: '{}' }
        }
      ]
    },
    { 
      role: 'tool', 
      content: '{"type": "RECTANGLE"}',
      tool_call_id: 'call_2' // Wrong ID
    }
  ]
  
  const wrongIdResult = validateConversationOrder(wrongIdConversation)
  console.log('🔍 Wrong tool_call_id test:', !wrongIdResult.isValid ? 'PASSED' : 'FAILED')
  if (!wrongIdResult.isValid) {
    console.log('Expected errors found:', wrongIdResult.errors)
  }
  
  return {
    validConversation: validResult.isValid,
    invalidConversation: !invalidResult.isValid,
    wrongIdConversation: !wrongIdResult.isValid
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const results = testConversationOrder()
  const allPassed = Object.values(results).every(Boolean)
  
  console.log('\n📊 Conversation Order Test Results:')
  console.log('Valid conversation:', results.validConversation ? '✅ PASSED' : '❌ FAILED')
  console.log('Invalid conversation:', results.invalidConversation ? '✅ PASSED' : '❌ FAILED')
  console.log('Wrong tool_call_id:', results.wrongIdConversation ? '✅ PASSED' : '❌ FAILED')
  
  process.exit(allPassed ? 0 : 1)
}
