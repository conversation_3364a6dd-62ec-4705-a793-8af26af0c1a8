// Unified Figma Client Interface
// Provides a clean, type-safe interface for all Figma plugin communications

import {
  MESSAGE_TYPES,
  WindowSettings,
  type FigmaClientEvents,
  type StorageInterface,
  type WindowInterface,
  type SelectionInterface,
  type CommandsInterface,
  type FigmaEnvironment,
  type EnvironmentInfo
} from './types'
import { MockFigmaClient } from './mock-client'

// Common interface for both implementations
interface IFigmaClient {
  readonly storage: StorageInterface
  readonly window: WindowInterface
  readonly selection: SelectionInterface
  readonly commands: CommandsInterface

  on<K extends keyof FigmaClientEvents>(event: K, listener: (data: FigmaClientEvents[K]) => void): void
  off<K extends keyof FigmaClientEvents>(event: K, listener: (data: FigmaClientEvents[K]) => void): void

  getWindowSettings(defaultSettings: WindowSettings): Promise<WindowSettings>
  setWindowSettings(settings: WindowSettings): Promise<void>

  destroy(): void
}

// Main Figma Client class
export class FigmaClient {
  private pendingRequests = new Map<string, { resolve: (value: unknown) => void, reject: (error: Error) => void }>()
  private eventListeners = new Map<string, Set<(data: unknown) => void>>()
  private isInitialized = false

  // Storage interface
  public readonly storage: StorageInterface
  // Window interface
  public readonly window: WindowInterface
  // Selection interface
  public readonly selection: SelectionInterface
  // Commands interface
  public readonly commands: CommandsInterface

  constructor() {
    this.storage = this.createStorageInterface()
    this.window = this.createWindowInterface()
    this.selection = this.createSelectionInterface()
    this.commands = this.createCommandsInterface()

    this.initialize()
  }

  private initialize() {
    if (this.isInitialized) return

    // Listen for responses from plugin
    window.addEventListener('message', this.handlePluginMessage.bind(this))
    this.isInitialized = true
  }

  private handlePluginMessage(event: MessageEvent) {
    const { type, request_id, success, value, error, data } = event.data.pluginMessage || {}

    console.log('🔄 [FigmaClient] Received message from plugin:', {
      type,
      request_id,
      success,
      error,
      hasData: !!data,
      pendingRequestsCount: this.pendingRequests.size,
      pendingRequestIds: Array.from(this.pendingRequests.keys())
    })

    // Handle storage responses
    if (type === MESSAGE_TYPES.STORAGE_RESPONSE && request_id) {
      console.log('💾 [FigmaClient] Processing storage response:', { request_id, success })
      const pending = this.pendingRequests.get(request_id)
      if (pending) {
        console.log('✅ [FigmaClient] Found pending request for storage response')
        this.pendingRequests.delete(request_id)
        if (success) {
          pending.resolve(value || null)
        } else {
          pending.reject(new Error(error || 'Operation failed'))
        }
      } else {
        console.warn('⚠️ [FigmaClient] No pending request found for storage response:', request_id)
      }
    }

    // Handle selection responses
    if (type === MESSAGE_TYPES.FIGMA_SELECTION_RESPONSE) {
      console.log('🎯 [FigmaClient] Processing selection response:', {
        request_id,
        success,
        hasData: !!data,
        pendingRequestsCount: this.pendingRequests.size
      })

      // Handle both request-response pattern and event-based pattern
      if (request_id) {
        // Request-response pattern
        console.log('🔗 [FigmaClient] Looking for pending request:', request_id)
        const pending = this.pendingRequests.get(request_id)
        if (pending) {
          console.log('✅ [FigmaClient] Found pending request for selection response')
          this.pendingRequests.delete(request_id)
          if (success) {
            // Return the selection data directly
            const responseData = data as Record<string, unknown>
            const resolvedData = responseData?.selection || data || {}
            console.log('📤 [FigmaClient] Resolving selection request with data:', resolvedData)
            pending.resolve(resolvedData)
          } else {
            console.log('❌ [FigmaClient] Selection request failed:', error)
            pending.reject(new Error(error || 'Failed to get selection'))
          }
        } else {
          console.warn('⚠️ [FigmaClient] No pending request found for selection response:', request_id)
        }
      } else {
        console.log('📡 [FigmaClient] Selection response without request_id (event-based)')
      }

      // Always emit event for listeners
      const responseData = data as Record<string, unknown>
      this.emit('selection-changed', {
        data: responseData?.selection || data || {},
        success: success !== false,
        error
      })
    }
  }

  private generateRequestId(): string {
    return `figma_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  private sendMessage(message: Record<string, unknown>, timeout = 10000): Promise<unknown> {
    return new Promise((resolve, reject) => {
      const request_id = this.generateRequestId()

      console.log('📤 [FigmaClient] Sending message:', {
        type: message.type,
        request_id,
        timeout,
        expectsResponse: message.type !== MESSAGE_TYPES.SET_WINDOW_SIZE && message.type !== MESSAGE_TYPES.CANCEL
      })

      if (message.type !== MESSAGE_TYPES.SET_WINDOW_SIZE &&
          message.type !== MESSAGE_TYPES.CANCEL) {
        this.pendingRequests.set(request_id, { resolve, reject })
        message.request_id = request_id
        console.log('⏳ [FigmaClient] Added to pending requests. Total pending:', this.pendingRequests.size)
      } else {
        // For messages that don't expect a response
        console.log('🚀 [FigmaClient] Fire-and-forget message')
        setTimeout(() => resolve(undefined), 100)
      }

      console.log('📡 [FigmaClient] Posting message to parent:', message)
      parent.postMessage({ pluginMessage: message }, '*')

      // Set timeout for requests that expect responses
      if (this.pendingRequests.has(request_id)) {
        setTimeout(() => {
          console.log('⏰ [FigmaClient] Timeout check for request:', request_id)
          console.log('📊 [FigmaClient] Current pending requests:', {
            count: this.pendingRequests.size,
            ids: Array.from(this.pendingRequests.keys())
          })
          if (this.pendingRequests.has(request_id)) {
            console.error('❌ [FigmaClient] Request timed out:', {
              request_id,
              timeout,
              type: message.type
            })
            this.pendingRequests.delete(request_id)
            reject(new Error(`Operation timed out after ${timeout}ms`))
          } else {
            console.log('✅ [FigmaClient] Request completed before timeout:', request_id)
          }
        }, timeout)
      }
    })
  }

  // Event system
  public on<K extends keyof FigmaClientEvents>(event: K, listener: (data: FigmaClientEvents[K]) => void): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set())
    }
    this.eventListeners.get(event)!.add(listener as (data: unknown) => void)
  }

  public off<K extends keyof FigmaClientEvents>(event: K, listener: (data: FigmaClientEvents[K]) => void): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      listeners.delete(listener as (data: unknown) => void)
    }
  }

  // Storage interface implementation
  private createStorageInterface(): StorageInterface {
    return {
      getItem: async <T>(key: string, defaultValue: T): Promise<T> => {
        try {
          const value = await this.sendMessage({
            type: MESSAGE_TYPES.STORAGE_GET,
            key
          })
          return value ? JSON.parse(value as string) : defaultValue
        } catch (error) {
          console.error(`Failed to get storage item: ${key}`, error)
          return defaultValue
        }
      },

      setItem: async <T>(key: string, value: T): Promise<void> => {
        try {
          await this.sendMessage({
            type: MESSAGE_TYPES.STORAGE_SET,
            key,
            value: JSON.stringify(value)
          })

          // Emit storage changed event
          this.emit('storage-changed', { key, value })
        } catch (error) {
          console.error(`Failed to set storage item: ${key}`, error)
          throw error
        }
      },

      removeItem: async (key: string): Promise<void> => {
        try {
          await this.sendMessage({
            type: MESSAGE_TYPES.STORAGE_DELETE,
            key
          })

          // Emit storage changed event
          this.emit('storage-changed', { key, value: null })
        } catch (error) {
          console.error(`Failed to remove storage item: ${key}`, error)
          throw error
        }
      }
    }
  }

  // Window interface implementation
  private createWindowInterface(): WindowInterface {
    return {
      resize: async (width: number, height: number): Promise<void> => {
        try {
          await this.sendMessage({
            type: MESSAGE_TYPES.SET_WINDOW_SIZE,
            width,
            height
          })

          // Emit window resized event
          this.emit('window-resized', { width, height })
        } catch (error) {
          console.error('Failed to resize window', error)
          throw error
        }
      },

      minimize: async (): Promise<void> => {
        return this.window.resize(52, 40)
      },

      close: async (): Promise<void> => {
        try {
          await this.sendMessage({
            type: MESSAGE_TYPES.CANCEL
          })
        } catch (error) {
          console.error('Failed to close plugin', error)
          throw error
        }
      }
    }
  }

  // Selection interface implementation
  private createSelectionInterface(): SelectionInterface {
    return {
      getSelection: async (): Promise<Record<string, unknown>> => {
        console.log('🎯 [FigmaClient] getSelection called')

        try {
          console.log('📤 [FigmaClient] Sending GET_FIGMA_SELECTION message with 5s timeout')
          // Use shorter timeout to avoid long waits
          const data = await this.sendMessage({
            type: MESSAGE_TYPES.GET_FIGMA_SELECTION
          }, 5000) // 5 seconds - more reasonable timeout

          console.log('✅ [FigmaClient] getSelection completed successfully:', {
            hasData: !!data,
            dataType: typeof data,
            isObject: typeof data === 'object' && data !== null
          })

          return (data as Record<string, unknown>) || {}
        } catch (error) {
          console.error('💥 [FigmaClient] Failed to get Figma selection:', {
            error: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack : undefined
          })
          throw error
        }
      },

      // Request selection without waiting for response (event-based)
      requestSelection: (): void => {
        try {
          // Use the unified message sending mechanism without waiting for response
          parent.postMessage({
            pluginMessage: {
              type: MESSAGE_TYPES.GET_FIGMA_SELECTION
            }
          }, '*')
        } catch (error) {
          console.error('Failed to request Figma selection', error)
          throw error
        }
      }
    }
  }

  // Commands interface implementation
  private createCommandsInterface(): CommandsInterface {
    return {
      executeSelectionCommand: (): void => {
        this.selection.requestSelection()
      },

      onSelectionResult: (callback: (result: { data: Record<string, unknown>; success: boolean; error?: string }) => void): (() => void) => {
        this.on('selection-changed', callback)
        return () => this.off('selection-changed', callback)
      }
    }
  }

  // Utility methods for common operations
  public async getWindowSettings(defaultSettings: WindowSettings): Promise<WindowSettings> {
    return this.storage.getItem('figmagent-window-settings', defaultSettings)
  }

  public async setWindowSettings(settings: WindowSettings): Promise<void> {
    await this.storage.setItem('figmagent-window-settings', settings)
    await this.window.resize(settings.width, settings.height)
  }

  // Enhanced environment detection
  public static detectEnvironment(): EnvironmentInfo {
    if (typeof window === 'undefined') {
      return {
        environment: 'unknown',
        isDevelopment: false,
        isProduction: false,
        hasParentWindow: false,
        userAgent: ''
      }
    }

    const hasParentWindow = window.parent && window.parent !== window
    const userAgent = navigator.userAgent
    const isDevelopment = import.meta.env?.DEV || process.env.NODE_ENV === 'development'
    const isProduction = import.meta.env?.PROD || process.env.NODE_ENV === 'production'

    // Check for Figma-specific indicators
    const isFigmaPlugin = hasParentWindow && (
      userAgent.includes('Figma') ||
      window.location.href.includes('figma.com') ||
      typeof (window as any).figma !== 'undefined'
    )

    let environment: FigmaEnvironment = 'unknown'
    if (isFigmaPlugin) {
      environment = 'figma-plugin'
    } else if (isDevelopment || window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
      environment = 'web-dev'
    }

    return {
      environment,
      isDevelopment,
      isProduction,
      hasParentWindow,
      userAgent
    }
  }

  // Legacy method for backward compatibility
  public static isInFigmaEnvironment(): boolean {
    return FigmaClient.detectEnvironment().environment === 'figma-plugin'
  }

  // Cleanup
  public destroy(): void {
    this.pendingRequests.clear()
    this.eventListeners.clear()
    this.isInitialized = false
  }

  // Protected methods for subclass override
  protected emit<K extends keyof FigmaClientEvents>(event: K, data: FigmaClientEvents[K]): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      listeners.forEach(listener => listener(data))
    }
  }
}

let figmaClientInstance: IFigmaClient | null = null

// Create environment-appropriate client instance
function createFigmaClient(): IFigmaClient {
  const envInfo = FigmaClient.detectEnvironment()

  console.log('🔍 [FigmaClient] Environment detection:', envInfo)

  if (envInfo.environment === 'figma-plugin') {
    console.log('🎨 [FigmaClient] Creating real Figma client for plugin environment')
    return new FigmaClient()
  } else if (envInfo.environment === 'web-dev') {
    console.log('🎭 [FigmaClient] Creating mock Figma client for development environment')
    return new MockFigmaClient() as IFigmaClient
  } else {
    console.warn('⚠️ [FigmaClient] Unknown environment, defaulting to real Figma client')
    return new FigmaClient()
  }
}

// Export the createFigmaClient function instead of the instance
export function getFigmaClient(): IFigmaClient {
  if (!figmaClientInstance) {
    figmaClientInstance = createFigmaClient()
  }
  return figmaClientInstance
}

// Utility function to reset client instance (useful for testing)
export function resetFigmaClient(): void {
  if (figmaClientInstance) {
    figmaClientInstance.destroy()
    figmaClientInstance = null
  }
}
