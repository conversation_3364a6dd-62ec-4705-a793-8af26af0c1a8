// Message input component with command suggestions and highlighting

import React, { useState, useCallback, useDeferredValue, useTransition, useMemo } from 'react'
import {
  useMount,
  useUnmount,
  useUpdateEffect,
  usePrevious
} from 'react-use'
import { MessageInputProps, Command } from '../types/ui_types'
import {
  removeCommandTokenAtCursor,
  updateMessageContentText,
  getPlainText
} from '../utils/message_content'
import { useInputPerformance } from '../hooks/useInputPerformance'
import { useInputScheduler } from '../utils/input_scheduler'
import { useMessageHistory } from '../store'


// Suggestion Box Component
interface SuggestionBoxProps {
  suggestions: Command[]
  selectedIndex: number
  onSuggestionClick: (suggestion: Command) => void
  onMouseEnter: (index: number) => void
}

const SuggestionBox: React.FC<SuggestionBoxProps> = ({
  suggestions,
  selectedIndex,
  onSuggestionClick,
  onMouseEnter
}) => {
  return (
    <div
      className="absolute bottom-full left-0 right-0 mb-2 z-50 bg-white border border-gray-200 rounded-lg shadow-2xl font-sans max-h-48 overflow-hidden"
    >
      <div className="max-h-full overflow-y-auto">
        {suggestions.map((suggestion, index) => (
          <div
            key={suggestion.name}
            className={`suggestion-item px-2 py-1 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors duration-100 ${
              index === selectedIndex ? 'bg-blue-50 border-blue-200' : 'hover:bg-gray-50'
            }`}
            title={suggestion.description}
            onMouseDown={(e) => {
              e.preventDefault()
              onSuggestionClick(suggestion)
            }}
            onMouseEnter={() => onMouseEnter(index)}
          >
            <div className={`font-medium text-xs leading-tight ${
              index === selectedIndex ? 'text-blue-600' : 'text-gray-800'
            }`}>
              {suggestion.name}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export const MessageInput: React.FC<MessageInputProps> = ({
  messageContent,
  showSuggestions,
  suggestions,
  selectedSuggestionIndex,
  onMessageContentChange,
  onSendMessage,
  onSuggestionClick,
  onSuggestionMouseEnter,
  onHideSuggestions,
  editableRef
}) => {
  // Optimized performance - only defer for very large content to avoid input lag
  const shouldDefer = messageContent.text.length > 2000 || messageContent.commandTokens.length > 10
  const deferredMessageContent = useDeferredValue(messageContent)
  const contentToUse = shouldDefer ? deferredMessageContent : messageContent
  const [isPending, startTransition] = useTransition()

  // Performance monitoring for input responsiveness
  const inputPerformance = useInputPerformance({
    enabled: process.env.NODE_ENV === 'development',
    onMetricsUpdate: (metrics) => {
      // Log performance issues in development
      if (metrics.inputLatency > 16) {
        console.warn(`🐌 [MessageInput] Input lag detected: ${metrics.inputLatency.toFixed(2)}ms`)
      }
    }
  })

  // Input scheduler for prioritizing operations
  const scheduler = useInputScheduler()

  // Message history navigation
  const messageHistory = useMessageHistory()
  // Add CSS for contenteditable placeholder and command highlighting
  useMount(() => {
    const style = document.createElement('style')
    style.textContent = `
      [contenteditable][data-placeholder]:empty::before {
        content: attr(data-placeholder);
        color: #9ca3af;
        pointer-events: none;
      }
      [contenteditable]:focus[data-placeholder]:empty::before {
        content: attr(data-placeholder);
        color: #d1d5db;
      }
      .command-token {
        background-color: #dbeafe;
        color: #1d4ed8;
        padding: 2px 4px;
        border-radius: 4px;
        font-weight: 500;
        border: 1px solid #93c5fd;
        display: inline-block;
        margin: 0 1px;
        user-select: all;
      }
      .command-token:hover {
        background-color: #bfdbfe;
      }
    `
    document.head.appendChild(style)
  })

  // Cleanup CSS on unmount
  useUnmount(() => {
    const styles = document.querySelectorAll('style')
    styles.forEach(style => {
      if (style.textContent?.includes('.command-token')) {
        document.head.removeChild(style)
      }
    })
  })

  // Ensure focus is maintained when message becomes empty
  useUpdateEffect(() => {
    if (!messageContent.text && editableRef.current && document.activeElement !== editableRef.current) {
      // If message is empty and element is not focused, focus it
      editableRef.current.focus()
    }
  }, [messageContent.text])

  // Memoize and optimize content rendering with deferred values
  const renderMessageContent = useMemo(() => {
    // Use conditional content to avoid unnecessary deferring
    const contentToRender = contentToUse

    if (!contentToRender.text) {
      return ''
    }

    let result = ''
    let lastIndex = 0

    // Sort command tokens by start position
    const sortedTokens = [...contentToRender.commandTokens].sort((a, b) => a.start - b.start)

    for (const token of sortedTokens) {
      // Add text before the command
      if (token.start > lastIndex) {
        const beforeText = contentToRender.text.substring(lastIndex, token.start)
        result += beforeText
      }

      // Add the command as a highlighted span
      const commandSpan = `<span class="command-token" data-command="${token.command}">${token.command}</span>`
      result += commandSpan
      lastIndex = token.end
    }

    // Add remaining text after the last command
    if (lastIndex < contentToRender.text.length) {
      const afterText = contentToRender.text.substring(lastIndex)
      result += afterText
    }

    return result
  }, [contentToUse])

  // Track if we need to update the display (only when commands change)
  const [needsDisplayUpdate, setNeedsDisplayUpdate] = useState(false)

  // Track previous messageContent to detect external changes using react-use
  const prevMessageContent = usePrevious(messageContent)



  // Optimized input change handler - minimize processing for better responsiveness
  const handleInputChange = useCallback((newText: string, cursorPos: number) => {
    // Start performance measurement only in development
    if (process.env.NODE_ENV === 'development') {
      inputPerformance.startProcessingMeasurement()
    }

    // Reset history navigation when user starts typing (lightweight operation)
    if (messageHistory.historyIndex !== -1) {
      messageHistory.resetHistory()
    }

    // Process content changes with minimal overhead
    const updatedContent = updateMessageContentText(messageContent, newText, cursorPos)

    // Only check for command changes if we have tokens (optimization)
    const hasTokens = messageContent.commandTokens.length > 0 || updatedContent.commandTokens.length > 0
    if (hasTokens) {
      const commandsChanged = updatedContent.commandTokens.length !== messageContent.commandTokens.length ||
        updatedContent.commandTokens.some((token, index) =>
          !messageContent.commandTokens[index] ||
          token.command !== messageContent.commandTokens[index].command ||
          token.start !== messageContent.commandTokens[index].start ||
          token.end !== messageContent.commandTokens[index].end
        )

      if (commandsChanged) {
        setNeedsDisplayUpdate(true)
      }
    }

    // Update state immediately for responsive typing
    onMessageContentChange(updatedContent)

    // End performance measurement only in development
    if (process.env.NODE_ENV === 'development') {
      inputPerformance.endProcessingMeasurement()
    }
  }, [messageContent, onMessageContentChange, inputPerformance, messageHistory])

  useUpdateEffect(() => {
    // Check if command tokens changed externally (not from onInput)
    const tokensChanged = !prevMessageContent ||
      messageContent.commandTokens.length !== prevMessageContent.commandTokens.length ||
      messageContent.commandTokens.some((token, index) =>
        !prevMessageContent.commandTokens[index] ||
        token.command !== prevMessageContent.commandTokens[index].command ||
        token.start !== prevMessageContent.commandTokens[index].start ||
        token.end !== prevMessageContent.commandTokens[index].end
      )

    if (tokensChanged) {
      setNeedsDisplayUpdate(true)
    }
  }, [messageContent])

  // Initialize content on mount
  useMount(() => {
    if (editableRef.current && !editableRef.current.innerHTML) {
      editableRef.current.innerHTML = renderMessageContent
    }
  })

  // Update contentEditable only when command tokens change or external updates occur
  useUpdateEffect(() => {
    if (editableRef.current && needsDisplayUpdate) {
      const renderedContent = renderMessageContent
      const currentInnerHTML = editableRef.current.innerHTML

      if (currentInnerHTML !== renderedContent) {

        // Save current cursor position
        const selection = window.getSelection()
        let cursorPosition = 0
        let shouldRestoreCursor = false

        if (selection && selection.rangeCount > 0 && document.activeElement === editableRef.current) {
          const range = selection.getRangeAt(0)
          // Calculate cursor position in text content
          const preCaretRange = range.cloneRange()
          preCaretRange.selectNodeContents(editableRef.current)
          preCaretRange.setEnd(range.endContainer, range.endOffset)
          cursorPosition = preCaretRange.toString().length
          shouldRestoreCursor = true
        }

        // Update content
        editableRef.current.innerHTML = renderedContent

        // Restore cursor position if needed
        if (shouldRestoreCursor) {
          setTimeout(() => {
            if (editableRef.current && document.activeElement === editableRef.current) {
              const selection = window.getSelection()
              if (selection) {
                const range = document.createRange()
                let currentPos = 0
                let targetNode: Node | null = null
                let targetOffset = 0

                const walker = document.createTreeWalker(
                  editableRef.current,
                  NodeFilter.SHOW_TEXT,
                  null
                )

                let node
                while ((node = walker.nextNode())) {
                  const nodeLength = node.textContent?.length || 0
                  if (currentPos + nodeLength >= cursorPosition) {
                    targetNode = node
                    targetOffset = cursorPosition - currentPos
                    break
                  }
                  currentPos += nodeLength
                }

                if (targetNode) {
                  range.setStart(targetNode, Math.min(targetOffset, targetNode.textContent?.length || 0))
                  range.setEnd(targetNode, Math.min(targetOffset, targetNode.textContent?.length || 0))
                  selection.removeAllRanges()
                  selection.addRange(range)
                }
              }
            }
          }, 0)
        }
      }

      setNeedsDisplayUpdate(false)
    }
  }, [needsDisplayUpdate, renderMessageContent])

  // Handle backspace for command deletion
  const handleBackspace = useCallback((e: React.KeyboardEvent) => {
    const selection = window.getSelection()
    if (!selection || !editableRef.current || selection.rangeCount === 0) return

    // Get cursor position in text content
    const range = selection.getRangeAt(0)
    const preCaretRange = range.cloneRange()
    preCaretRange.selectNodeContents(editableRef.current)
    preCaretRange.setEnd(range.endContainer, range.endOffset)
    const cursorPos = preCaretRange.toString().length

    // Check if we're about to delete a command token
    const { content: newContent, deletedToken } = removeCommandTokenAtCursor(messageContent, cursorPos)

    if (deletedToken) {
      e.preventDefault()
      onMessageContentChange(newContent)
      setNeedsDisplayUpdate(true)

      // Set cursor position after the deletion
      setTimeout(() => {
        if (editableRef.current) {
          const newSelection = window.getSelection()
          if (newSelection) {
            const newRange = document.createRange()
            // Position cursor where the command was deleted
            const targetPos = deletedToken.start
            let currentPos = 0
            let targetNode: Node | null = null
            let targetOffset = 0

            const walker = document.createTreeWalker(
              editableRef.current,
              NodeFilter.SHOW_TEXT,
              null
            )

            let node
            while ((node = walker.nextNode())) {
              const nodeLength = node.textContent?.length || 0
              if (currentPos + nodeLength >= targetPos) {
                targetNode = node
                targetOffset = targetPos - currentPos
                break
              }
              currentPos += nodeLength
            }

            if (targetNode) {
              newRange.setStart(targetNode, Math.min(targetOffset, targetNode.textContent?.length || 0))
              newRange.setEnd(targetNode, Math.min(targetOffset, targetNode.textContent?.length || 0))
              newSelection.removeAllRanges()
              newSelection.addRange(newRange)
            }
          }
        }
      }, 0)
    }
    // If no command token is being deleted, let the browser handle backspace naturally
  }, [messageContent, onMessageContentChange])

  return (
    <div className="space-y-3">
      {/* Input Box with Suggestion Overlay */}
      <div className="relative">
        {/* Suggestion Box - Positioned above input */}
        {showSuggestions && suggestions.length > 0 && (
          <SuggestionBox
            suggestions={suggestions}
            selectedIndex={selectedSuggestionIndex}
            onSuggestionClick={onSuggestionClick}
            onMouseEnter={onSuggestionMouseEnter}
          />
        )}

        <div
          ref={editableRef}
          contentEditable
          className={`w-full p-3 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 h-[120px] overflow-y-auto whitespace-pre-wrap text-sm transition-opacity duration-200 font-inherit leading-relaxed ${
            isPending ? 'opacity-90' : 'opacity-100'
          }`}
          data-placeholder="Enter your message or use /sel-figma, /sel-twhtml, /help... (Shift+Enter for new line)"
          suppressContentEditableWarning={true}
          onInput={(e) => {
            // Start input latency measurement
            inputPerformance.startInputMeasurement()

            const newText = e.currentTarget.textContent || ''

            // Get current cursor position
            let cursorPos = newText.length // Default to end
            const selection = window.getSelection()
            if (selection && selection.rangeCount > 0) {
              const range = selection.getRangeAt(0)
              const preCaretRange = range.cloneRange()
              preCaretRange.selectNodeContents(e.currentTarget)
              preCaretRange.setEnd(range.endContainer, range.endOffset)
              cursorPos = preCaretRange.toString().length
            }

            // Use optimized handler for immediate feedback and deferred processing
            handleInputChange(newText, cursorPos)
          }}
          onKeyDown={(e) => {
            if (showSuggestions && suggestions.length > 0) {
              if (e.key === 'ArrowDown') {
                e.preventDefault()
                const newIndex = selectedSuggestionIndex < suggestions.length - 1 ? selectedSuggestionIndex + 1 : 0
                onSuggestionMouseEnter(newIndex)
              } else if (e.key === 'ArrowUp') {
                e.preventDefault()
                const newIndex = selectedSuggestionIndex > 0 ? selectedSuggestionIndex - 1 : suggestions.length - 1
                onSuggestionMouseEnter(newIndex)
              } else if (e.key === 'Enter') {
                e.preventDefault()
                onSuggestionClick(suggestions[selectedSuggestionIndex])
              } else if (e.key === 'Escape') {
                e.preventDefault()
                onHideSuggestions()
              }
            } else if (e.key === 'ArrowUp') {
              // Handle message history navigation when input is empty
              const currentText = getPlainText(messageContent).trim()
              if (currentText === '' && messageHistory.hasHistory) {
                e.preventDefault()
                const historyMessage = messageHistory.navigateHistory('up')
                if (historyMessage !== null) {
                  // Update message content with history message
                  const updatedContent = updateMessageContentText(messageContent, historyMessage, historyMessage.length)
                  onMessageContentChange(updatedContent)

                  // Ensure proper cursor positioning at the end of the text
                  setTimeout(() => {
                    if (editableRef.current) {
                      const selection = window.getSelection()
                      if (selection) {
                        const range = document.createRange()
                        range.selectNodeContents(editableRef.current)
                        range.collapse(false) // Collapse to end
                        selection.removeAllRanges()
                        selection.addRange(range)
                      }
                    }
                  }, 0)
                }
              }
            } else if (e.key === 'ArrowDown') {
              // Handle forward history navigation
              const currentText = getPlainText(messageContent).trim()
              if (currentText === '' && messageHistory.historyIndex !== -1) {
                e.preventDefault()
                const historyMessage = messageHistory.navigateHistory('down')
                if (historyMessage !== null) {
                  // Update message content with history message (or empty string to clear)
                  const updatedContent = updateMessageContentText(messageContent, historyMessage, historyMessage.length)
                  onMessageContentChange(updatedContent)

                  // Ensure proper cursor positioning
                  setTimeout(() => {
                    if (editableRef.current) {
                      const selection = window.getSelection()
                      if (selection) {
                        const range = document.createRange()
                        if (historyMessage === '') {
                          // If clearing input, position cursor at start
                          range.setStart(editableRef.current, 0)
                          range.collapse(true)
                        } else {
                          // If setting text, position cursor at end
                          range.selectNodeContents(editableRef.current)
                          range.collapse(false)
                        }
                        selection.removeAllRanges()
                        selection.addRange(range)
                      }
                    }
                  }, 0)
                }
              }
            } else if (e.key === 'Enter' && !e.shiftKey) {
              e.preventDefault()
              // Reset history navigation when sending a message
              messageHistory.resetHistory()
              onSendMessage()
            } else if (e.key === 'Backspace') {
              handleBackspace(e)

              // Handle backspace to prevent focus loss when content becomes empty
              const currentText = e.currentTarget.textContent || ''
              if (currentText.length <= 1) {
                // If we're about to delete the last character, ensure we maintain focus
                setTimeout(() => {
                  if (editableRef.current && !editableRef.current.textContent) {
                    // Content is now empty, ensure focus is maintained
                    if (document.activeElement !== editableRef.current) {
                      editableRef.current.focus()
                    }
                    // Ensure cursor is positioned correctly
                    const selection = window.getSelection()
                    if (selection) {
                      const range = document.createRange()
                      range.selectNodeContents(editableRef.current)
                      range.collapse(false)
                      selection.removeAllRanges()
                      selection.addRange(range)
                    }
                  }
                }, 0)
              }
            }
          }}
          onBlur={() => {
            // Hide suggestions when contenteditable loses focus
            setTimeout(() => {
              onHideSuggestions()
            }, 150) // Small delay to allow suggestion clicks
          }}
        />
      </div>

      {/* Controls Row - Below Input */}
      <div className="flex items-center justify-end gap-2">
        {/* Send Button */}
        <button
          onClick={onSendMessage}
          disabled={!messageContent.text.trim()}
          className="w-8 h-8 bg-blue-600 text-white rounded-lg flex items-center justify-center hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
          title="Send (Enter)"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M2 21L23 12L2 3V10L17 12L2 14V21Z" fill="currentColor"/>
          </svg>
        </button>
      </div>
    </div>
  )
}
