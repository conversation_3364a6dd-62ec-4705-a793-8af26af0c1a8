// OpenRouter Library - Unified interface for OpenRouter API
// Export all public APIs

export {
  OpenRouterClient,
  getOpenRouterClient
} from './client'

export {
  FIGMA_TOOLS,
  executeTool,
  executeFigmaSelectionJson,
  executeFigmaSelectionCss,
  executeFigmaSelectionTailwindHtml
} from './tools'

// Re-export types from ui_types for convenience
export type {
  OpenRouterConfig,
  AiMessage,
  AiRequest,
  AiResponse,
  AiStreamChunk,
  AiModel,
  AiTool,
  AiToolCall,
  // Legacy aliases for backward compatibility
  OpenRouterMessage,
  OpenRouterRequest,
  OpenRouterResponse,
  OpenRouterStreamChunk,
  OpenRouterModel,
  OpenRouterTool,
  OpenRouterToolCall
} from '../../ui/types/ui_types'

export {
  DEFAULT_OPENROUTER_CONFIG
} from '../../ui/types/ui_types'
