// Development Tools Tab
// Comprehensive testing utilities for the mock Figma client in development mode

import { useState, useEffect } from 'react'
import { getFigmaClient, FigmaClient } from '../../lib/figma'
import ChatInterfaceTest from './ChatInterface.test'
import InputPerformanceTest from './InputPerformanceTest'
import { VendorPersistenceTest } from './VendorPersistenceTest'

export function DevToolsTab() {
  const [envInfo, setEnvInfo] = useState<any>(null)
  const [selectionData, setSelectionData] = useState<any>(null)
  const [storageTest, setStorageTest] = useState<string>('')
  const [windowTest, setWindowTest] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)
  const [storageKeys, setStorageKeys] = useState<string[]>([])
  const [showChatTest, setShowChatTest] = useState(false)
  const [showInputTest, setShowInputTest] = useState(false)

  // Auto-check environment on mount
  useEffect(() => {
    checkEnvironment()
    loadStorageKeys()
  }, [])

  // Check environment detection
  const checkEnvironment = () => {
    const info = FigmaClient.detectEnvironment()
    setEnvInfo(info)
    console.log('🔍 Environment Info:', info)
  }

  // Load storage keys
  const loadStorageKeys = () => {
    if (typeof window !== 'undefined' && window.__figmaDevHelpers) {
      const keys = window.__figmaDevHelpers.getStorageKeys()
      setStorageKeys(keys)
    }
  }

  // Test mock selection
  const testSelection = async () => {
    setIsLoading(true)
    try {
      const client = getFigmaClient()
      const selection = await client.selection.getSelection()
      setSelectionData(selection)
      console.log('🎯 Selection Data:', selection)
    } catch (error) {
      console.error('❌ Selection Error:', error)
      setSelectionData({ error: error instanceof Error ? error.message : 'Unknown error' })
    } finally {
      setIsLoading(false)
    }
  }

  // Test storage operations
  const testStorage = async () => {
    try {
      const client = getFigmaClient()
      const testKey = 'dev-test-key'
      const testValue = { message: 'Hello from dev mode!', timestamp: Date.now() }

      // Set item
      await client.storage.setItem(testKey, testValue)
      console.log('💾 Stored test data')

      // Get item
      const retrieved = await client.storage.getItem(testKey, null)
      console.log('📦 Retrieved test data:', retrieved)
      setStorageTest(JSON.stringify(retrieved, null, 2))

      // Remove item
      await client.storage.removeItem(testKey)
      console.log('🗑️ Removed test data')

      // Refresh storage keys
      loadStorageKeys()
    } catch (error) {
      console.error('❌ Storage Error:', error)
      setStorageTest(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  // Test window operations
  const testWindow = async () => {
    try {
      const client = getFigmaClient()
      setWindowTest('Testing window operations...')

      console.log('🪟 Testing window resize...')
      await client.window.resize(500, 700)
      setWindowTest(prev => prev + '\n✅ Resize to 500x700 completed')

      console.log('🪟 Testing window minimize...')
      await client.window.minimize()
      setWindowTest(prev => prev + '\n✅ Minimize completed')

      console.log('🪟 Testing window close...')
      await client.window.close()
      setWindowTest(prev => prev + '\n✅ Close completed')

    } catch (error) {
      console.error('❌ Window Error:', error)
      setWindowTest(prev => prev + `\n❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  // Clear all mock storage
  const clearAllStorage = () => {
    if (typeof window !== 'undefined' && window.__figmaDevHelpers) {
      window.__figmaDevHelpers.clearStorage()
      setStorageKeys([])
      setStorageTest('')
      console.log('🧹 All mock storage cleared')
    }
  }

  // Toggle selection state
  const toggleSelection = () => {
    if (typeof window !== 'undefined' && window.__figmaDevHelpers) {
      window.__figmaDevHelpers.toggleSelection()
      console.log('🔄 Selection state toggled')
    }
  }

  return (
    <div className="flex flex-col h-full p-4 space-y-6 overflow-y-auto">
      <div className="flex-shrink-0">
        <h1 className="text-xl font-bold text-gray-800 mb-2">🛠️ Development Tools</h1>
        <p className="text-sm text-gray-600">
          Testing utilities for the mock Figma client in development mode
        </p>
      </div>

      {/* Environment Information */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h2 className="text-lg font-semibold text-blue-800 mb-3">Environment Detection</h2>

        <button
          onClick={checkEnvironment}
          className="mb-3 px-3 py-2 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors"
        >
          Refresh Environment Info
        </button>

        {envInfo && (
          <div className="bg-blue-100 p-3 rounded text-sm space-y-1">
            <div><strong>Environment:</strong> {envInfo.environment}</div>
            <div><strong>Development Mode:</strong> {envInfo.isDevelopment ? 'Yes' : 'No'}</div>
            <div><strong>Production Mode:</strong> {envInfo.isProduction ? 'Yes' : 'No'}</div>
            <div><strong>Has Parent Window:</strong> {envInfo.hasParentWindow ? 'Yes' : 'No'}</div>
            <div><strong>User Agent:</strong> {envInfo.userAgent}</div>
          </div>
        )}
      </div>

      {/* Mock Client Testing */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <h2 className="text-lg font-semibold text-green-800 mb-3">Mock Client Testing</h2>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 mb-4">
          <button
            onClick={testSelection}
            disabled={isLoading}
            className="px-3 py-2 bg-green-600 text-white text-sm rounded hover:bg-green-700 disabled:opacity-50 transition-colors"
          >
            {isLoading ? 'Loading...' : 'Test Selection'}
          </button>
          <button
            onClick={testStorage}
            className="px-3 py-2 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors"
          >
            Test Storage
          </button>
          <button
            onClick={testWindow}
            className="px-3 py-2 bg-purple-600 text-white text-sm rounded hover:bg-purple-700 transition-colors"
          >
            Test Window
          </button>
          <button
            onClick={toggleSelection}
            className="px-3 py-2 bg-orange-600 text-white text-sm rounded hover:bg-orange-700 transition-colors"
          >
            Toggle Selection
          </button>
          <button
            onClick={clearAllStorage}
            className="px-3 py-2 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition-colors"
          >
            Clear Storage
          </button>
          <button
            onClick={loadStorageKeys}
            className="px-3 py-2 bg-gray-600 text-white text-sm rounded hover:bg-gray-700 transition-colors"
          >
            Refresh Keys
          </button>
        </div>

        {/* Selection Results */}
        {selectionData && (
          <div className="mb-4">
            <h3 className="text-sm font-medium text-green-700 mb-2">Selection Data</h3>
            <pre className="text-xs text-green-600 bg-green-100 p-3 rounded overflow-auto max-h-48 border">
              {JSON.stringify(selectionData, null, 2)}
            </pre>
          </div>
        )}

        {/* Storage Results */}
        {storageTest && (
          <div className="mb-4">
            <h3 className="text-sm font-medium text-green-700 mb-2">Storage Test Results</h3>
            <pre className="text-xs text-green-600 bg-green-100 p-3 rounded overflow-auto max-h-32 border">
              {storageTest}
            </pre>
          </div>
        )}

        {/* Window Results */}
        {windowTest && (
          <div className="mb-4">
            <h3 className="text-sm font-medium text-green-700 mb-2">Window Test Results</h3>
            <pre className="text-xs text-green-600 bg-green-100 p-3 rounded overflow-auto max-h-32 border whitespace-pre-wrap">
              {windowTest}
            </pre>
          </div>
        )}
      </div>

      {/* Storage Management */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h2 className="text-lg font-semibold text-yellow-800 mb-3">Storage Management</h2>

        <div className="mb-3">
          <h3 className="text-sm font-medium text-yellow-700 mb-2">Current Storage Keys ({storageKeys.length})</h3>
          {storageKeys.length > 0 ? (
            <div className="bg-yellow-100 p-3 rounded text-sm space-y-1 max-h-32 overflow-y-auto border">
              {storageKeys.map((key, index) => (
                <div key={index} className="font-mono text-xs">
                  {key}
                </div>
              ))}
            </div>
          ) : (
            <div className="bg-yellow-100 p-3 rounded text-sm text-yellow-600 border">
              No storage keys found
            </div>
          )}
        </div>
      </div>

      {/* Console Helpers */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h2 className="text-lg font-semibold text-gray-800 mb-3">Console Helpers</h2>
        <div className="text-sm text-gray-600 space-y-2">
          <div><code className="bg-gray-200 px-2 py-1 rounded text-xs">window.__figmaDevHelpers.toggleSelection()</code> - Toggle selection state</div>
          <div><code className="bg-gray-200 px-2 py-1 rounded text-xs">window.__figmaDevHelpers.getStorageKeys()</code> - Get all storage keys</div>
          <div><code className="bg-gray-200 px-2 py-1 rounded text-xs">window.__figmaDevHelpers.clearStorage()</code> - Clear all mock storage</div>
        </div>
      </div>



      {/* Responsive Design Testing */}
      <div className="bg-indigo-50 border border-indigo-200 rounded-lg p-4">
        <h2 className="text-lg font-semibold text-indigo-800 mb-3">Responsive Design Info</h2>
        <div className="text-sm text-indigo-600 space-y-1">
          <div><strong>Window Size:</strong> {typeof window !== 'undefined' ? `${window.innerWidth}x${window.innerHeight}` : 'Unknown'}</div>
          <div><strong>Screen Size:</strong> {typeof window !== 'undefined' ? `${window.screen.width}x${window.screen.height}` : 'Unknown'}</div>
          <div><strong>Device Pixel Ratio:</strong> {typeof window !== 'undefined' ? window.devicePixelRatio : 'Unknown'}</div>
        </div>
      </div>

      {/* Vendor Persistence Testing */}
      <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
        <h2 className="text-lg font-semibold text-orange-800 mb-3">🔧 Vendor Persistence Testing</h2>
        <VendorPersistenceTest />
      </div>

      {/* Performance Testing */}
      <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
        <h2 className="text-lg font-semibold text-purple-800 mb-3">🚀 Performance Testing</h2>
        <p className="text-sm text-purple-600 mb-4">
          Test the recent fixes for auto-scroll and input performance issues.
        </p>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-4">
          <button
            onClick={() => setShowChatTest(!showChatTest)}
            className={`px-3 py-2 text-white text-sm rounded transition-colors ${
              showChatTest ? 'bg-purple-700 hover:bg-purple-800' : 'bg-purple-600 hover:bg-purple-700'
            }`}
          >
            {showChatTest ? 'Hide' : 'Show'} Chat Interface Test
          </button>
          <button
            onClick={() => setShowInputTest(!showInputTest)}
            className={`px-3 py-2 text-white text-sm rounded transition-colors ${
              showInputTest ? 'bg-purple-700 hover:bg-purple-800' : 'bg-purple-600 hover:bg-purple-700'
            }`}
          >
            {showInputTest ? 'Hide' : 'Show'} Input Performance Test
          </button>
        </div>

        {/* Chat Interface Test */}
        {showChatTest && (
          <div className="mb-4">
            <h3 className="text-sm font-medium text-purple-700 mb-2">Chat Interface Auto-Scroll Test</h3>
            <div className="bg-white border border-purple-200 rounded p-2">
              <ChatInterfaceTest />
            </div>
          </div>
        )}

        {/* Input Performance Test */}
        {showInputTest && (
          <div className="mb-4">
            <h3 className="text-sm font-medium text-purple-700 mb-2">Input Performance Test</h3>
            <div className="bg-white border border-purple-200 rounded p-2">
              <InputPerformanceTest />
            </div>
          </div>
        )}

        <div className="text-xs text-purple-600 bg-purple-100 p-3 rounded">
          <strong>Test Instructions:</strong>
          <ul className="list-disc list-inside mt-1 space-y-1">
            <li><strong>Chat Test:</strong> Verify messages auto-scroll to bottom when added. Test with many messages.</li>
            <li><strong>Input Test:</strong> Type rapidly in the input field to verify no lag with large message counts.</li>
            <li><strong>Runtime Error Fix:</strong> Loading indicators should now work without "getTime is not a function" errors.</li>
            <li><strong>Code Block Performance:</strong> Large JSON/code blocks in AI responses should use virtualization for better performance.</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
