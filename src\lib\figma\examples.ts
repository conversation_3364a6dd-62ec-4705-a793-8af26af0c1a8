// Example usage of the unified FigmaClient interface
// This file demonstrates how to use the new FigmaClient for various operations

import { getFigmaClient, DEFAULT_WINDOW_SETTINGS } from './index'

// Example: Storage operations
export async function storageExamples() {
  console.log('📦 Storage Examples:')
  const client = getFigmaClient()

  // Get window settings with default fallback
  const settings = await client.getWindowSettings(DEFAULT_WINDOW_SETTINGS)
  console.log('Current settings:', settings)

  // Save new settings (automatically resizes window)
  await client.setWindowSettings({
    width: 500,
    height: 700
  })

  // Direct storage operations
  await client.storage.setItem('my-custom-key', { data: 'example' })
  const customData = await client.storage.getItem('my-custom-key', null)
  console.log('Custom data:', customData)
}

// Example: Window operations
export async function windowExamples() {
  console.log('🪟 Window Examples:')
  const client = getFigmaClient()

  // Resize window
  await client.window.resize(600, 800)

  // Minimize window
  await client.window.minimize()

  // Close plugin
  // await client.window.close()
}

// Example: Selection operations
export async function selectionExamples() {
  console.log('🎯 Selection Examples:')
  const client = getFigmaClient()

  // Get current Figma selection
  const selection = await client.selection.getSelection()
  console.log('Current selection:', selection)
}

// Example: Event handling
export function eventExamples() {
  console.log('📡 Event Examples:')
  const client = getFigmaClient()

  // Listen for storage changes
  client.on('storage-changed', (data) => {
    console.log('Storage changed:', data.key, data.value)
  })

  // Listen for window resize events
  client.on('window-resized', (data) => {
    console.log('Window resized:', data.width, 'x', data.height)
  })

  // Listen for selection changes
  client.on('selection-changed', (data) => {
    console.log('Selection changed:', data.data)
  })
}

// Example: Error handling
export async function errorHandlingExample() {
  console.log('⚠️ Error Handling Examples:')
  const client = getFigmaClient()

  try {
    await client.storage.setItem('test-key', 'test-value')
    console.log('✅ Storage operation successful')
  } catch (error) {
    console.error('❌ Storage operation failed:', error)
  }

  try {
    await client.window.resize(1000, 1000)
    console.log('✅ Window resize successful')
  } catch (error) {
    console.error('❌ Window resize failed:', error)
  }
}

// Example: Environment detection
export function environmentExample() {
  console.log('🔍 Environment Detection:')
  const client = getFigmaClient()

  if (client.constructor.name === 'DevelopmentFigmaClient') {
    console.log('🔧 Running in development mode')
  } else {
    console.log('🎨 Running in Figma plugin environment')
  }
}

// Run all examples
export async function runAllExamples() {
  console.log('🚀 Running FigmaClient Examples...')

  environmentExample()
  eventExamples()

  try {
    await storageExamples()
    await windowExamples()
    await selectionExamples()
    await errorHandlingExample()

    console.log('✅ All examples completed successfully')
  } catch (error) {
    console.error('❌ Examples failed:', error)
  }
}

// Cleanup function
export function cleanup() {
  console.log('🧹 Cleaning up FigmaClient...')
  getFigmaClient().destroy()
}
