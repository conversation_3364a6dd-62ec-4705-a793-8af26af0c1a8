System.register(["@babel/core","@babel/template"],function(m){"use strict";var r;return{setters:[null,function(o){r=o.default}],execute:function(){m("default",d);function o(t,i,e=[]){const n=[...c,...e];if(t.isIdentifier(i)&&n.includes(i.name))return!0;if(t.isMemberExpression(i)){const{property:a}=i;if(t.isIdentifier(a)&&n.includes(a.name))return!0}return!1}const c=["atom","atomFamily","atomWithDefault","atomWithObservable","atomWithReducer","atomWithReset","atomWithStorage","freezeAtom","loadable","selectAtom","splitAtom","unwrap","atomWithMachine","atomWithImmer","atomWithProxy","atomWithQuery","atomWithMutation","atomWithSubscription","atomWithStore","atomWithHash","atomWithLocation","focusAtom","atomWithValidate","validateAtoms","atomWithCache","atomWithRecoilValue"],u=r.default||r;function d({types:t},i){return{visitor:{ExportDefaultDeclaration(e,n){const{node:a}=e;if(t.isCallExpression(a.declaration)&&o(t,a.declaration.callee,i==null?void 0:i.customAtomNames)){const l=(n.filename||"unknown").replace(/\.\w+$/,"");let s=l.split("/").pop();s==="index"&&(s=l.slice(0,-6).split("/").pop()||"unknown");const f=u(`
          const %%atomIdentifier%% = %%atom%%;
          export default %%atomIdentifier%%
          `)({atomIdentifier:t.identifier(s),atom:a.declaration});e.replaceWithMultiple(f)}},VariableDeclarator(e){t.isIdentifier(e.node.id)&&t.isCallExpression(e.node.init)&&o(t,e.node.init.callee,i==null?void 0:i.customAtomNames)&&e.parentPath.insertAfter(t.expressionStatement(t.assignmentExpression("=",t.memberExpression(t.identifier(e.node.id.name),t.identifier("debugLabel")),t.stringLiteral(e.node.id.name))))}}}}}}});
