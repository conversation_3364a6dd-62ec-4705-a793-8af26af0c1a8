// Performance testing utilities for chat interface
// Used to validate input responsiveness with large message counts

import { Message } from '../types/ui_types'

/**
 * Generate test messages for performance testing
 */
export function generateTestMessages(count: number): Message[] {
  const messages: Message[] = []
  const now = new Date()

  for (let i = 0; i < count; i++) {
    const isUser = i % 3 === 0
    const isAi = i % 3 === 1
    const messageType = isUser ? 'user' : isAi ? 'ai' : 'system'
    
    // Create varied message content to simulate real usage
    let text = ''
    if (messageType === 'user') {
      text = `User message ${i + 1}: This is a test message to simulate user input.`
    } else if (messageType === 'ai') {
      text = `AI response ${i + 1}: Here's a detailed response with some code:\n\n\`\`\`javascript\nfunction example() {\n  console.log('Hello world');\n  return 'test';\n}\n\`\`\`\n\nThis response includes multiple paragraphs to test rendering performance with longer content.`
    } else {
      text = `System message ${i + 1}: Command executed successfully.`
    }

    // Add some messages with command outputs (JSON)
    if (i % 10 === 0 && messageType === 'system') {
      text = `Figma selection data:\n\n\`\`\`json\n{\n  "selection": [\n    {\n      "id": "test-${i}",\n      "name": "Test Node ${i}",\n      "type": "FRAME",\n      "width": 100,\n      "height": 100\n    }\n  ],\n  "timestamp": "${now.toISOString()}"\n}\n\`\`\``
    }

    messages.push({
      id: `test-message-${i}`,
      text,
      timestamp: new Date(now.getTime() - (count - i) * 1000),
      type: messageType,
      ...(messageType === 'ai' && { vendor: 'openrouter', model: 'gpt-4o-mini' })
    })
  }

  return messages
}

/**
 * Measure input performance
 */
export class InputPerformanceTester {
  private startTime: number = 0
  private measurements: number[] = []

  startMeasurement() {
    this.startTime = performance.now()
  }

  endMeasurement() {
    if (this.startTime > 0) {
      const duration = performance.now() - this.startTime
      this.measurements.push(duration)
      this.startTime = 0
      return duration
    }
    return 0
  }

  getStats() {
    if (this.measurements.length === 0) {
      return { avg: 0, max: 0, min: 0, count: 0 }
    }

    const avg = this.measurements.reduce((sum, val) => sum + val, 0) / this.measurements.length
    const max = Math.max(...this.measurements)
    const min = Math.min(...this.measurements)

    return {
      avg: Math.round(avg * 100) / 100,
      max: Math.round(max * 100) / 100,
      min: Math.round(min * 100) / 100,
      count: this.measurements.length
    }
  }

  reset() {
    this.measurements = []
    this.startTime = 0
  }
}

/**
 * Test input responsiveness with simulated typing
 */
export function testInputResponsiveness(
  inputElement: HTMLElement,
  testText: string,
  onProgress?: (progress: number) => void
): Promise<{ avgLatency: number; maxLatency: number; measurements: number[] }> {
  return new Promise((resolve) => {
    const tester = new InputPerformanceTester()
    const measurements: number[] = []
    let charIndex = 0

    const typeNextChar = () => {
      if (charIndex >= testText.length) {
        const stats = tester.getStats()
        resolve({
          avgLatency: stats.avg,
          maxLatency: stats.max,
          measurements: [...measurements]
        })
        return
      }

      tester.startMeasurement()
      
      // Simulate typing by updating the input
      const currentText = testText.substring(0, charIndex + 1)
      inputElement.textContent = currentText
      
      // Trigger input event
      const event = new Event('input', { bubbles: true })
      inputElement.dispatchEvent(event)

      // Measure the time it takes for the UI to respond
      requestAnimationFrame(() => {
        const latency = tester.endMeasurement()
        measurements.push(latency)
        
        if (onProgress) {
          onProgress((charIndex + 1) / testText.length)
        }

        charIndex++
        
        // Continue typing with a small delay to simulate real typing
        setTimeout(typeNextChar, 50)
      })
    }

    typeNextChar()
  })
}

/**
 * Performance test configuration
 */
export const PERFORMANCE_TEST_CONFIG = {
  // Number of messages to test with
  MESSAGE_COUNTS: [10, 25, 50, 100, 200],
  
  // Test text for typing simulation
  TEST_TEXT: '/sel-figma hello world this is a test message with commands',
  
  // Performance thresholds (in milliseconds)
  THRESHOLDS: {
    GOOD: 16,      // 60fps
    ACCEPTABLE: 33, // 30fps
    POOR: 100      // 10fps
  }
}

/**
 * Log performance test results
 */
export function logPerformanceResults(
  messageCount: number,
  results: { avgLatency: number; maxLatency: number; measurements: number[] }
) {
  const { avgLatency, maxLatency } = results
  const { THRESHOLDS } = PERFORMANCE_TEST_CONFIG

  let status = '🟢 GOOD'
  if (avgLatency > THRESHOLDS.POOR) {
    status = '🔴 POOR'
  } else if (avgLatency > THRESHOLDS.ACCEPTABLE) {
    status = '🟡 ACCEPTABLE'
  }

  console.log(`📊 Performance Test Results (${messageCount} messages):`)
  console.log(`   Status: ${status}`)
  console.log(`   Average Latency: ${avgLatency.toFixed(2)}ms`)
  console.log(`   Max Latency: ${maxLatency.toFixed(2)}ms`)
  console.log(`   Measurements: ${results.measurements.length}`)
  
  if (avgLatency > THRESHOLDS.GOOD) {
    console.warn(`⚠️  Input latency above 16ms threshold. Consider further optimization.`)
  }
}
