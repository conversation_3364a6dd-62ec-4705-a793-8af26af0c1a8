// Chat and Message Management Hooks
// Hooks for managing chat messages, message content, and message history

import { atom, useAtom, useAtomValue, useSetAtom } from 'jotai'
import { useCallback, useMemo } from 'react'
import { Message, MessageContent, Command } from '../types/ui_types'
import { createEmptyMessageContent } from '../utils/message_content'

// =============================================================================
// CHAT ATOMS
// =============================================================================

// All chat messages
export const messagesAtom = atom<Message[]>([])

// Current message being composed
export const messageContentAtom = atom<MessageContent>(createEmptyMessageContent())

// Message input suggestions state
export const showSuggestionsAtom = atom<boolean>(false)
export const suggestionsAtom = atom<Command[]>([])
export const selectedSuggestionIndexAtom = atom<number>(0)

// UI state for message interactions
export const copiedMessageIdAtom = atom<string | null>(null)

// Message history navigation state
export const messageHistoryIndexAtom = atom<number>(-1) // -1 means no history navigation active

// Command execution state (merged from useCommandHooks.ts)
export const lastCommandAtom = atom<string | null>(null)

// Messages with automatic cleanup for performance
export const messagesWithCleanupAtom = atom(
  (get) => get(messagesAtom),
  (get, set, newMessages: Message[] | ((prev: Message[]) => Message[])) => {
    const MAX_MESSAGES = 500
    const CLEANUP_THRESHOLD = 600

    const currentMessages = get(messagesAtom)
    const updatedMessages = typeof newMessages === 'function'
      ? newMessages(currentMessages)
      : newMessages

    // Apply cleanup if needed
    if (updatedMessages.length > CLEANUP_THRESHOLD) {
      const cleaned = updatedMessages.slice(-MAX_MESSAGES)
      console.log(`🧹 Jotai: Cleaned up messages: ${updatedMessages.length} → ${cleaned.length}`)
      set(messagesAtom, cleaned)
    } else {
      set(messagesAtom, updatedMessages)
    }
  }
)

// Clear all chat data
export const clearChatDataAtom = atom(
  null,
  (_get, set) => {
    set(messagesAtom, [])
    set(messageContentAtom, createEmptyMessageContent())
    set(showSuggestionsAtom, false)
    set(suggestionsAtom, [])
    set(selectedSuggestionIndexAtom, 0)
    set(copiedMessageIdAtom, null)
    console.log('🧹 Jotai: Cleared all chat data')
  }
)

/**
 * Hook for managing chat messages
 */
export function useChatMessages() {
  const messages = useAtomValue(messagesAtom)
  const setMessages = useSetAtom(messagesWithCleanupAtom)
  const clearChatData = useSetAtom(clearChatDataAtom)

  return {
    messages,
    setMessages,
    addMessage: useCallback((message: Message) => {
      setMessages(prev => [...prev, message])
    }, [setMessages]),
    clearMessages: useCallback(() => {
      clearChatData()
    }, [clearChatData])
  }
}

/**
 * Hook for managing message history navigation
 */
export function useMessageHistory() {
  const messages = useAtomValue(messagesAtom)
  const [historyIndex, setHistoryIndex] = useAtom(messageHistoryIndexAtom)

  // Get user messages only, in chronological order
  const userMessages = useMemo(() => {
    return messages
      .filter(msg => msg.type === 'user')
      .map(msg => msg.text)
  }, [messages])

  const navigateHistory = useCallback((direction: 'up' | 'down') => {
    if (userMessages.length === 0) return null

    let newIndex: number
    if (direction === 'up') {
      // Navigate backwards in history (older messages)
      if (historyIndex === -1) {
        // Start from the most recent message
        newIndex = userMessages.length - 1
      } else if (historyIndex > 0) {
        newIndex = historyIndex - 1
      } else {
        // Already at oldest message
        return null
      }
    } else {
      // Navigate forwards in history (newer messages)
      if (historyIndex === -1 || historyIndex >= userMessages.length - 1) {
        // Reset to no history navigation
        setHistoryIndex(-1)
        return ''
      } else {
        newIndex = historyIndex + 1
      }
    }

    setHistoryIndex(newIndex)
    return userMessages[newIndex]
  }, [userMessages, historyIndex, setHistoryIndex])

  const resetHistory = useCallback(() => {
    setHistoryIndex(-1)
  }, [setHistoryIndex])

  return {
    userMessages,
    historyIndex,
    navigateHistory,
    resetHistory,
    hasHistory: userMessages.length > 0
  }
}

/**
 * Hook for managing message content (current input)
 */
export function useMessageContent() {
  const [messageContent, setMessageContent] = useAtom(messageContentAtom)

  return {
    messageContent,
    setMessageContent,
    clearMessageContent: useCallback(() => {
      setMessageContent({ text: '', commandTokens: [] })
    }, [setMessageContent])
  }
}

/**
 * Hook for managing command suggestions
 */
export function useCommandSuggestions() {
  const [showSuggestions, setShowSuggestions] = useAtom(showSuggestionsAtom)
  const [suggestions, setSuggestions] = useAtom(suggestionsAtom)
  const [selectedIndex, setSelectedIndex] = useAtom(selectedSuggestionIndexAtom)

  return {
    showSuggestions,
    setShowSuggestions,
    suggestions,
    setSuggestions,
    selectedIndex,
    setSelectedIndex,
    hideSuggestions: useCallback(() => {
      setShowSuggestions(false)
      setSuggestions([])
      setSelectedIndex(0)
    }, [setShowSuggestions, setSuggestions, setSelectedIndex])
  }
}

/**
 * Hook for managing copied message state
 */
export function useCopiedMessage() {
  const [copiedMessageId, setCopiedMessageId] = useAtom(copiedMessageIdAtom)

  return {
    copiedMessageId,
    setCopiedMessageId,
    clearCopiedMessage: useCallback(() => {
      setCopiedMessageId(null)
    }, [setCopiedMessageId])
  }
}

// =============================================================================
// COMMAND EXECUTION (Merged from useCommandHooks.ts)
// =============================================================================

// Command execution atom
export const commandExecutionAtom = atom(
  null,
  (_get, set, command: string) => {
    set(lastCommandAtom, command)
    // Additional command execution logic can be added here
  }
)

/**
 * Hook for managing command execution
 * Merged from useCommandHooks.ts for consolidation
 */
export function useCommandExecution() {
  const lastCommand = useAtomValue(lastCommandAtom)
  const executeCommand = useSetAtom(commandExecutionAtom)

  return {
    lastCommand,
    executeCommand: useCallback((command: string) => {
      executeCommand(command)
    }, [executeCommand])
  }
}
