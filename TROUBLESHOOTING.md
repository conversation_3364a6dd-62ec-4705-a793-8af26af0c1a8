# 🔧 FigmaAgent 故障排除指南

## 🚨 常见问题解决方案

### 1. Figma 插件导入错误

#### 问题：`Plugin error: This plugin template uses TypeScript. Follow the instructions in README.md to generate code.js`

**原因**: Figma 期望插件主文件名为 `code.js`，但项目配置生成的是 `plugin.js`。

**解决方案**:
```bash
# 1. 清理旧的构建文件
npm run clean

# 2. 重新构建插件
npm run build:prod

# 3. 验证输出文件
ls dist/
# 应该看到: code.js, index.html, manifest.json
```

**验证修复**:
- 检查 `dist/manifest.json` 中的 `"main": "./code.js"`
- 确认 `dist/code.js` 文件存在
- 在 Figma 中重新导入插件

#### 问题：`Cannot find manifest.json`

**解决方案**:
```bash
# 确保从项目根目录导入插件
# 在 Figma 中选择整个项目文件夹，而不是 dist/ 文件夹
```

### 2. 开发服务器问题

#### 问题：`npm run dev` 启动失败

**可能原因和解决方案**:

1. **端口被占用**:
   ```bash
   # 检查端口使用情况
   netstat -ano | findstr :5173
   
   # 杀死占用进程或使用不同端口
   npm run dev -- --port 5174
   ```

2. **依赖问题**:
   ```bash
   # 重新安装依赖
   npm run clean
   npm install
   ```

3. **Node.js 版本不兼容**:
   ```bash
   # 检查 Node.js 版本
   node --version
   # 需要 18+ 版本
   ```

#### 问题：热重载不工作

**解决方案**:
```bash
# 1. 检查 Vite 配置
# 2. 清理缓存
npm run clean

# 3. 重启开发服务器
npm run dev
```

### 3. 构建问题

#### 问题：TypeScript 编译错误

**常见错误和解决方案**:

1. **类型定义缺失**:
   ```bash
   # 安装缺失的类型定义
   npm install --save-dev @types/jest @testing-library/react
   ```

2. **导入路径错误**:
   ```typescript
   // 错误
   import { AiMessage } from './types'
   
   // 正确
   import type { AiMessage } from './types'
   ```

3. **环境变量类型错误**:
   ```typescript
   // 使用类型断言
   const apiKey = import.meta.env.VITE_API_KEY as string
   ```

#### 问题：构建输出文件缺失

**解决方案**:
```bash
# 1. 检查构建配置
cat vite.config.plugin.ts
cat vite.config.ui.ts

# 2. 清理并重新构建
npm run clean
npm run build:prod

# 3. 验证输出
ls -la dist/
```

### 4. AI 服务集成问题

#### 问题：API 请求失败

**检查清单**:
1. **API 密钥配置**:
   - 在设置中正确配置 API 密钥
   - 检查密钥格式和权限

2. **网络访问权限**:
   - 检查 `manifest.json` 中的 `allowedDomains`
   - 确认域名拼写正确

3. **CORS 问题**:
   ```javascript
   // 检查浏览器控制台是否有 CORS 错误
   // 确保 API 端点支持跨域请求
   ```

#### 问题：AI 响应格式错误

**调试步骤**:
1. 检查浏览器开发者工具的网络标签
2. 查看 API 响应格式
3. 验证客户端解析逻辑

### 5. 存储和状态问题

#### 问题：设置不保存

**解决方案**:
```bash
# 1. 检查 Figma clientStorage 权限
# 2. 验证存储键名一致性
# 3. 检查序列化/反序列化逻辑
```

#### 问题：状态同步问题

**调试方法**:
1. 检查 Jotai 原子定义
2. 验证状态更新逻辑
3. 使用 React DevTools 检查状态

### 6. 性能问题

#### 问题：插件运行缓慢

**优化建议**:
1. **减少 Figma API 调用**:
   ```typescript
   // 批量处理选择
   const selection = figma.currentPage.selection;
   // 避免在循环中调用 API
   ```

2. **优化数据序列化**:
   ```typescript
   // 只序列化必要的属性
   const minimalData = {
     id: node.id,
     type: node.type,
     name: node.name
   };
   ```

3. **使用 Web Workers**:
   ```typescript
   // 将重计算移到 Web Worker
   ```

### 7. 调试技巧

#### 启用详细日志

1. **插件端调试**:
   ```typescript
   console.log('🔧 [Plugin]', message);
   ```

2. **UI 端调试**:
   ```typescript
   console.log('🎨 [UI]', data);
   ```

3. **网络请求调试**:
   ```typescript
   // 在浏览器开发者工具中查看网络标签
   ```

#### 使用 VSCode 调试器

1. 设置断点
2. 使用 F5 启动调试配置
3. 检查变量值和调用栈

### 8. 环境特定问题

#### Windows 特定问题

1. **PowerShell 执行策略**:
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

2. **路径分隔符问题**:
   ```javascript
   // 使用 path.join() 而不是字符串拼接
   const filePath = path.join(__dirname, 'file.js');
   ```

#### macOS/Linux 特定问题

1. **权限问题**:
   ```bash
   chmod +x scripts/dev-start.sh
   ```

2. **Node.js 版本管理**:
   ```bash
   # 使用 nvm
   nvm use 18
   ```

### 9. 获取帮助

#### 收集调试信息

运行以下命令收集系统信息：
```bash
# 系统信息
node --version
npm --version
git --version

# 项目信息
npm list --depth=0
npm run type-check 2>&1 | head -20

# 构建信息
npm run build:prod 2>&1 | tail -20
```

#### 报告问题

包含以下信息：
1. 错误消息的完整文本
2. 重现步骤
3. 系统环境信息
4. 相关的控制台日志

---

💡 **提示**: 大多数问题可以通过清理缓存和重新构建解决：
```bash
npm run clean && npm run setup
```
