/**
 * Test for multi-turn streaming tool calling implementation
 * This test verifies that the AI can execute tools and continue the conversation
 * to analyze the tool results in a streaming fashion.
 */

import { getOpenRouterClient } from '../lib/openrouter/client'
import { getDeepSeekClient } from '../lib/deepseek/client'
import type { AiMessage, AiToolCall, ToolExecutionResult } from '../ui/types/ui_types'

// Mock configuration for testing
const mockOpenRouterConfig = {
  apiKey: 'test-key',
  model: 'openai/gpt-4o-mini',
  temperature: 0.7,
  maxTokens: 1000,
  systemPrompt: 'You are a helpful AI assistant.'
}

const mockDeepSeekConfig = {
  apiKey: 'test-key',
  model: 'deepseek-chat',
  temperature: 0.7,
  maxTokens: 1000,
  systemPrompt: 'You are a helpful AI assistant.'
}

// Test multi-turn streaming with OpenRouter
async function testOpenRouterMultiTurnStreaming() {
  console.log('🧪 Testing OpenRouter multi-turn streaming...')

  const client = getOpenRouterClient(mockOpenRouterConfig)
  const conversation: OpenRouterMessage[] = []

  let streamedContent = ''
  let toolCallsExecuted: Array<{ toolCall: OpenRouterToolCall, result: ToolExecutionResult }> = []
  let turnsCompleted: Array<{ turn: number, hasToolCalls: boolean }> = []

  try {
    await client.sendMultiTurnStreamingMessage(
      'Analyze my Figma selection and provide design recommendations',
      conversation,
      3, // maxTurns
      // onChunk
      (chunk: string) => {
        streamedContent += chunk
        process.stdout.write(chunk) // Real-time output
      },
      // onComplete
      () => {
        console.log('\n✅ OpenRouter multi-turn streaming completed')
        console.log(`📝 Final conversation length: ${conversation.length} messages`)
        console.log(`🔧 Tool calls executed: ${toolCallsExecuted.length}`)
        console.log(`🔄 Turns completed: ${turnsCompleted.length}`)
      },
      // onError
      (error: Error) => {
        console.error('❌ OpenRouter streaming error:', error.message)
      },
      // abortController
      undefined,
      // onToolCall
      (toolCall: OpenRouterToolCall, result: ToolExecutionResult) => {
        console.log(`\n🔧 Tool executed: ${toolCall.function.name}`)
        console.log(`📊 Result success: ${result.success}`)
        toolCallsExecuted.push({ toolCall, result })
      },
      // onTurnComplete
      (turn: number, hasToolCalls: boolean) => {
        console.log(`\n🔄 Turn ${turn} completed, hasToolCalls: ${hasToolCalls}`)
        turnsCompleted.push({ turn, hasToolCalls })
      }
    )

    // Verify the conversation flow
    console.log('\n📋 Conversation Analysis:')
    conversation.forEach((msg, index) => {
      console.log(`${index + 1}. ${msg.role}: ${msg.content?.substring(0, 100)}...`)
      if (msg.tool_calls) {
        console.log(`   🔧 Tool calls: ${msg.tool_calls.length}`)
      }
    })

    return {
      success: true,
      conversationLength: conversation.length,
      toolCallsExecuted: toolCallsExecuted.length,
      turnsCompleted: turnsCompleted.length,
      streamedContent: streamedContent.length
    }

  } catch (error) {
    console.error('❌ OpenRouter test failed:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    }
  }
}

// Test multi-turn streaming with DeepSeek
async function testDeepSeekMultiTurnStreaming() {
  console.log('\n🧪 Testing DeepSeek multi-turn streaming...')

  const client = getDeepSeekClient(mockDeepSeekConfig)
  const conversation: OpenRouterMessage[] = []

  let streamedContent = ''
  let toolCallsExecuted: Array<{ toolCall: OpenRouterToolCall, result: ToolExecutionResult }> = []
  let turnsCompleted: Array<{ turn: number, hasToolCalls: boolean }> = []

  try {
    await client.sendMultiTurnStreamingMessage(
      'Get my Figma selection data and analyze it',
      conversation,
      3, // maxTurns
      // onChunk
      (chunk: string) => {
        streamedContent += chunk
        process.stdout.write(chunk) // Real-time output
      },
      // onComplete
      () => {
        console.log('\n✅ DeepSeek multi-turn streaming completed')
        console.log(`📝 Final conversation length: ${conversation.length} messages`)
        console.log(`🔧 Tool calls executed: ${toolCallsExecuted.length}`)
        console.log(`🔄 Turns completed: ${turnsCompleted.length}`)
      },
      // onError
      (error: Error) => {
        console.error('❌ DeepSeek streaming error:', error.message)
      },
      // abortController
      undefined,
      // onToolCall
      (toolCall: OpenRouterToolCall, result: ToolExecutionResult) => {
        console.log(`\n🔧 Tool executed: ${toolCall.function.name}`)
        console.log(`📊 Result success: ${result.success}`)
        toolCallsExecuted.push({ toolCall, result })
      },
      // onTurnComplete
      (turn: number, hasToolCalls: boolean) => {
        console.log(`\n🔄 Turn ${turn} completed, hasToolCalls: ${hasToolCalls}`)
        turnsCompleted.push({ turn, hasToolCalls })
      }
    )

    return {
      success: true,
      conversationLength: conversation.length,
      toolCallsExecuted: toolCallsExecuted.length,
      turnsCompleted: turnsCompleted.length,
      streamedContent: streamedContent.length
    }

  } catch (error) {
    console.error('❌ DeepSeek test failed:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    }
  }
}

// Main test runner
export async function runMultiTurnStreamingTests() {
  console.log('🚀 Starting Multi-Turn Streaming Tool Calling Tests\n')

  const results = {
    openRouter: await testOpenRouterMultiTurnStreaming(),
    deepSeek: await testDeepSeekMultiTurnStreaming()
  }

  console.log('\n📊 Test Results Summary:')
  console.log('OpenRouter:', results.openRouter.success ? '✅ PASSED' : '❌ FAILED')
  console.log('DeepSeek:', results.deepSeek.success ? '✅ PASSED' : '❌ FAILED')

  return results
}

// Run tests if this file is executed directly
if (require.main === module) {
  runMultiTurnStreamingTests()
    .then(results => {
      const allPassed = results.openRouter.success && results.deepSeek.success
      process.exit(allPassed ? 0 : 1)
    })
    .catch(error => {
      console.error('💥 Test runner failed:', error)
      process.exit(1)
    })
}
