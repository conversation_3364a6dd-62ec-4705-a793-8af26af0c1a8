/**
 * Test to verify the fix for tool message ordering
 * This test ensures that assistant messages with tool_calls are added before tool messages
 */

import { validateConversationOrder } from './conversation_order_test'
import type { OpenRouterMessage, OpenRouterToolCall } from '../ui/types/ui_types'

// Mock the tool execution to simulate the fixed behavior
function simulateFixedToolExecution(): OpenRouterMessage[] {
  const conversation: OpenRouterMessage[] = []
  
  // 1. User message
  conversation.push({
    role: 'user',
    content: 'Analyze my Figma selection and provide recommendations'
  })
  
  // 2. Assistant message with tool_calls (added FIRST)
  const assistantMessage: OpenRouterMessage = {
    role: 'assistant',
    content: 'I\'ll analyze your Figma selection to provide design recommendations.',
    tool_calls: [
      {
        id: 'call_abc123',
        type: 'function',
        function: {
          name: 'get_figma_selection_json',
          arguments: '{}'
        }
      }
    ]
  }
  conversation.push(assistantMessage)
  
  // 3. Tool message (added AFTER assistant message with tool_calls)
  const toolMessage: OpenRouterMessage = {
    role: 'tool',
    content: JSON.stringify({
      type: 'RECTANGLE',
      width: 200,
      height: 100,
      fills: [{ type: 'SOLID', color: { r: 0.2, g: 0.4, b: 0.8 } }]
    }, null, 2),
    tool_call_id: 'call_abc123'
  }
  conversation.push(toolMessage)
  
  // 4. Final assistant response analyzing the tool results
  conversation.push({
    role: 'assistant',
    content: 'Based on your selection, I can see you have a blue rectangle (200x100px). Here are my design recommendations:\n\n1. Consider using consistent spacing\n2. The blue color works well for UI elements\n3. You might want to add rounded corners for a modern look'
  })
  
  return conversation
}

// Test the broken behavior (for comparison)
function simulateBrokenToolExecution(): OpenRouterMessage[] {
  const conversation: OpenRouterMessage[] = []
  
  // 1. User message
  conversation.push({
    role: 'user',
    content: 'Analyze my Figma selection and provide recommendations'
  })
  
  // 2. Tool message added FIRST (this is wrong!)
  const toolMessage: OpenRouterMessage = {
    role: 'tool',
    content: JSON.stringify({
      type: 'RECTANGLE',
      width: 200,
      height: 100
    }, null, 2),
    tool_call_id: 'call_abc123'
  }
  conversation.push(toolMessage)
  
  // 3. Assistant message with tool_calls added AFTER (this is wrong!)
  const assistantMessage: OpenRouterMessage = {
    role: 'assistant',
    content: 'I\'ll analyze your Figma selection.',
    tool_calls: [
      {
        id: 'call_abc123',
        type: 'function',
        function: {
          name: 'get_figma_selection_json',
          arguments: '{}'
        }
      }
    ]
  }
  conversation.push(assistantMessage)
  
  return conversation
}

// Main test function
export function testToolMessageOrderFix() {
  console.log('🧪 Testing tool message order fix...\n')
  
  // Test the fixed behavior
  console.log('📋 Testing FIXED behavior (assistant message with tool_calls BEFORE tool message):')
  const fixedConversation = simulateFixedToolExecution()
  const fixedResult = validateConversationOrder(fixedConversation)
  
  console.log('Conversation order:')
  fixedConversation.forEach((msg, i) => {
    const toolCallsInfo = msg.tool_calls ? ` (${msg.tool_calls.length} tool_calls)` : ''
    const toolCallIdInfo = msg.tool_call_id ? ` (tool_call_id: ${msg.tool_call_id})` : ''
    console.log(`  ${i + 1}. ${msg.role}${toolCallsInfo}${toolCallIdInfo}`)
  })
  
  console.log(`\nValidation result: ${fixedResult.isValid ? '✅ VALID' : '❌ INVALID'}`)
  if (!fixedResult.isValid) {
    console.log('Errors:', fixedResult.errors)
  }
  
  // Test the broken behavior
  console.log('\n📋 Testing BROKEN behavior (tool message BEFORE assistant message with tool_calls):')
  const brokenConversation = simulateBrokenToolExecution()
  const brokenResult = validateConversationOrder(brokenConversation)
  
  console.log('Conversation order:')
  brokenConversation.forEach((msg, i) => {
    const toolCallsInfo = msg.tool_calls ? ` (${msg.tool_calls.length} tool_calls)` : ''
    const toolCallIdInfo = msg.tool_call_id ? ` (tool_call_id: ${msg.tool_call_id})` : ''
    console.log(`  ${i + 1}. ${msg.role}${toolCallsInfo}${toolCallIdInfo}`)
  })
  
  console.log(`\nValidation result: ${brokenResult.isValid ? '✅ VALID' : '❌ INVALID'}`)
  if (!brokenResult.isValid) {
    console.log('Expected errors:', brokenResult.errors)
  }
  
  // Summary
  console.log('\n📊 Test Results Summary:')
  console.log(`Fixed implementation: ${fixedResult.isValid ? '✅ PASSED' : '❌ FAILED'}`)
  console.log(`Broken implementation: ${!brokenResult.isValid ? '✅ CORRECTLY DETECTED AS INVALID' : '❌ FAILED TO DETECT ISSUE'}`)
  
  const testPassed = fixedResult.isValid && !brokenResult.isValid
  console.log(`\nOverall test: ${testPassed ? '✅ PASSED' : '❌ FAILED'}`)
  
  if (testPassed) {
    console.log('\n🎉 The fix correctly ensures that:')
    console.log('   1. Assistant messages with tool_calls are added BEFORE tool execution')
    console.log('   2. Tool messages are added AFTER the assistant message')
    console.log('   3. Tool messages have correct tool_call_id references')
    console.log('   4. The conversation order follows OpenAI API requirements')
  }
  
  return {
    fixedValid: fixedResult.isValid,
    brokenInvalid: !brokenResult.isValid,
    overallPassed: testPassed
  }
}

// Run test if this file is executed directly
if (require.main === module) {
  const result = testToolMessageOrderFix()
  process.exit(result.overallPassed ? 0 : 1)
}
