import { useState, useRef, useMemo, useCallback, useEffect } from 'react'
import {
  useMount,
  useWindowSize,
  useUpdateEffect,
  useToggle,
  useCounter,
  useSetState,
  useTimeoutFn
} from 'react-use'
import { MasonryVirtualScroll } from './VirtualScroll'

interface NewsItem {
  id: string
  title: string
  summary: string
  url: string
  publishedAt: string
  source: string
  category: string
  type: 'text' | 'image' | 'video'
  media?: {
    url: string
    thumbnail?: string
    alt?: string
  }
  size: 'small' | 'medium' | 'large'
}

// Performance optimization constants with virtual scrolling
const MAX_NEWS_ITEMS = 1000 // Maximum items to keep in memory (increased for virtual scrolling)
const ITEMS_PER_PAGE = 20    // Items to load per page (increased for better UX)
const ESTIMATED_ITEM_HEIGHT = 200 // Estimated height for virtual scrolling

interface NewsTabProps {
  // Future props for customization
}

export function NewsTab(_props: NewsTabProps) {
  const [news, setNews] = useState<NewsItem[]>([])

  // Use react-use hooks for better state management
  const [loading, toggleLoading] = useToggle(false)
  const [loadingMore, toggleLoadingMore] = useToggle(false)
  const [hasMore, toggleHasMore] = useToggle(true)
  const [page, { inc: incrementPage, reset: resetPage }] = useCounter(1)

  // Group related state with useSetState
  const [newsState, setNewsState] = useSetState({
    selectedCategory: 'all',
    error: null as string | null
  })

  const scrollContainerRef = useRef<HTMLDivElement>(null)

  const categories = [
    { id: 'all', label: 'All News' },
    { id: 'design', label: 'Design' },
    { id: 'tech', label: 'Technology' },
    { id: 'figma', label: 'Figma' },
    { id: 'ui-ux', label: 'UI/UX' }
  ]

  // Generate mock news data with different types and sizes for performance testing
  const generateMockNews = (startId: number, count: number): NewsItem[] => {
    const titles = [
      'Figma Announces New AI-Powered Design Features',
      'The Future of Design Systems in 2024',
      'New CSS Features That Will Change Web Design',
      'UX Research Methods for Remote Teams',
      'Accessibility in Design: Beyond Compliance',
      'Revolutionary Prototyping Tools Released',
      'Design Trends That Will Define 2024',
      'Building Inclusive User Interfaces',
      'The Rise of Voice User Interfaces',
      'Micro-Interactions: Small Details, Big Impact',
      'Color Theory in Digital Design',
      'Typography Best Practices for Web',
      'Mobile-First Design Strategies',
      'Design System Documentation Tips',
      'User Testing in the Age of AI',
      'Advanced Prototyping Techniques',
      'Design Leadership in Remote Teams',
      'The Psychology of User Interface Design',
      'Sustainable Design Practices',
      'Cross-Platform Design Consistency',
      'Data-Driven Design Decisions',
      'Design Ethics and Responsibility',
      'The Evolution of Design Tools',
      'Creating Emotional User Experiences',
      'Design System Governance',
      'Inclusive Design Methodologies',
      'The Future of Web Typography',
      'Design Thinking for Product Innovation',
      'User Research Best Practices',
      'Design Collaboration Tools Comparison'
    ]

    const summaries = [
      'Discover the latest innovations that are reshaping the design industry.',
      'Industry experts share insights on emerging trends and methodologies.',
      'Explore cutting-edge techniques and tools for modern designers.',
      'Learn best practices from leading design teams around the world.',
      'Deep dive into the principles that drive exceptional user experiences.',
      'A comprehensive guide to implementing new design workflows.',
      'Understanding the impact of technology on design processes.',
      'Practical tips for creating more accessible and inclusive designs.',
      'How emerging technologies are changing the way we design.',
      'Case studies from successful design projects and their lessons.',
      'Revolutionary approaches to user interface design and development.',
      'Insights into the psychology behind effective design decisions.',
      'Strategies for building scalable and maintainable design systems.',
      'The intersection of artificial intelligence and creative design.',
      'Methods for conducting effective user research in digital products.',
      'Exploring the future of design collaboration and remote work.',
      'Best practices for creating inclusive and accessible digital experiences.',
      'The role of data analytics in informing design decisions.',
      'Innovative prototyping techniques for rapid iteration.',
      'Building design cultures that foster creativity and innovation.'
    ]

    const sources = ['Figma Blog', 'Design Systems', 'CSS-Tricks', 'UX Research', 'A11Y Design', 'Smashing Magazine', 'UX Planet', 'Designer News']
    const categories = ['figma', 'design', 'tech', 'ui-ux']
    const types: ('text' | 'image' | 'video')[] = ['text', 'image', 'video']
    const sizes: ('small' | 'medium' | 'large')[] = ['small', 'medium', 'large']

    const imageUrls = [
      'https://images.unsplash.com/photo-1558655146-9f40138edfeb?w=400&h=200&fit=crop',
      'https://images.unsplash.com/photo-1586717791821-3f44a563fa4c?w=400&h=200&fit=crop',
      'https://images.unsplash.com/photo-1561070791-2526d30994b5?w=400&h=200&fit=crop',
      'https://images.unsplash.com/photo-1559028006-448665bd7c7f?w=400&h=200&fit=crop',
      'https://images.unsplash.com/photo-1542744094-3a31f272c490?w=400&h=200&fit=crop'
    ]

    return Array.from({ length: count }, (_, index) => {
      const id = startId + index
      const type = types[Math.floor(Math.random() * types.length)]
      const size = sizes[Math.floor(Math.random() * sizes.length)]

      return {
        id: id.toString(),
        title: titles[Math.floor(Math.random() * titles.length)] + ` #${id}`,
        summary: summaries[Math.floor(Math.random() * summaries.length)],
        url: `https://example.com/article/${id}`,
        publishedAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
        source: sources[Math.floor(Math.random() * sources.length)],
        category: categories[Math.floor(Math.random() * categories.length)],
        type,
        size,
        media: type !== 'text' ? {
          url: type === 'video' ? `https://example.com/video/${id}.mp4` : imageUrls[Math.floor(Math.random() * imageUrls.length)],
          thumbnail: type === 'video' ? imageUrls[Math.floor(Math.random() * imageUrls.length)] : undefined,
          alt: `${type} for article ${id}`
        } : undefined
      }
    })
  }

  // Load initial news when component mounts or category changes
  useUpdateEffect(() => {
    loadNews(true)
  }, [newsState.selectedCategory])

  // Virtual scrolling handles infinite scroll automatically

  const loadNews = useCallback(async (reset = false) => {
    if (reset) {
      toggleLoading(true)
      resetPage()
      toggleHasMore(true)
    }
    setNewsState({ error: null })

    try {
      // Reduced delay for performance testing
      await new Promise(resolve => setTimeout(resolve, 200))

      const startId = reset ? 1 : (page - 1) * ITEMS_PER_PAGE + 1
      const newItems = generateMockNews(startId, ITEMS_PER_PAGE)

      // Filter by category
      const filteredItems = newsState.selectedCategory === 'all'
        ? newItems
        : newItems.filter(item => item.category === newsState.selectedCategory)

      if (reset) {
        setNews(filteredItems)
      } else {
        setNews(prev => {
          const combined = [...prev, ...filteredItems]
          // Performance optimization: limit items in memory
          if (combined.length > MAX_NEWS_ITEMS) {
            return combined.slice(-MAX_NEWS_ITEMS) // Keep only the latest items
          }
          return combined
        })
      }

      // Infinite news generation - never stop loading
      // toggleHasMore(false) // Commented out for infinite generation
    } catch (err) {
      setNewsState({ error: 'Failed to load news. Please try again.' })
      console.error('Error loading news:', err)
    } finally {
      toggleLoading(false)
    }
  }, [page, newsState.selectedCategory, toggleLoading, resetPage, toggleHasMore, setNewsState])

  const loadMoreNews = useCallback(async () => {
    if (loadingMore || !hasMore) return

    toggleLoadingMore(true)
    incrementPage()

    try {
      // Reduced delay for performance testing
      await new Promise(resolve => setTimeout(resolve, 300))

      const startId = page * ITEMS_PER_PAGE + 1
      const newItems = generateMockNews(startId, ITEMS_PER_PAGE)

      // Filter by category
      const filteredItems = newsState.selectedCategory === 'all'
        ? newItems
        : newItems.filter(item => item.category === newsState.selectedCategory)

      setNews(prev => {
        const combined = [...prev, ...filteredItems]
        // Performance optimization: limit items in memory
        if (combined.length > MAX_NEWS_ITEMS) {
          // Keep the latest items and remove old ones
          return combined.slice(-MAX_NEWS_ITEMS)
        }
        return combined
      })

      // Infinite news generation - never stop loading
      // toggleHasMore(false) // Commented out for infinite generation
    } catch (err) {
      setNewsState({ error: 'Failed to load more news. Please try again.' })
      console.error('Error loading more news:', err)
    } finally {
      toggleLoadingMore(false)
    }
  }, [page, newsState.selectedCategory, loadingMore, hasMore, toggleLoadingMore, incrementPage, setNewsState])

  // Optimize event handlers with react-use
  const formatDate = useMemo(() => {
    const formatter = new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })

    return (dateString: string) => formatter.format(new Date(dateString))
  }, [])

  const openNewsItem = useCallback((url: string) => {
    // In a real implementation, this might open in a new tab or in-app browser
    window.open(url, '_blank')
  }, [])

  const refreshNews = useCallback(() => {
    loadNews(true)
  }, [loadNews])

  // Optimized news card renderer for virtual scrolling
  const renderNewsCard = useCallback((item: NewsItem, _index: number) => {
    const heightClasses = {
      small: 'min-h-[100px] sm:min-h-[120px]',
      medium: 'min-h-[160px] sm:min-h-[200px]',
      large: 'min-h-[220px] sm:min-h-[280px]'
    }

    return (
      <article
        className={`w-full ${heightClasses[item.size]} bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer overflow-hidden group mb-3 sm:mb-4`}
        onClick={() => openNewsItem(item.url)}
      >
        {/* Media Section */}
        {item.media && (
          <div className={`relative ${
            item.size === 'large' ? 'h-32 sm:h-48' :
            item.size === 'medium' ? 'h-24 sm:h-32' :
            'h-20 sm:h-24'
          } overflow-hidden`}>
            {item.type === 'video' ? (
              <div className="relative w-full h-full">
                <img
                  src={item.media.thumbnail}
                  alt={item.media.alt}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                />
                <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
                  <div className="w-8 h-8 sm:w-12 sm:h-12 bg-white bg-opacity-90 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 sm:w-6 sm:h-6 text-gray-800 ml-0.5 sm:ml-1" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </div>
                </div>
                <div className="absolute top-1 right-1 sm:top-2 sm:right-2 bg-red-600 text-white text-xs px-1.5 py-0.5 sm:px-2 sm:py-1 rounded">
                  VIDEO
                </div>
              </div>
            ) : (
              <img
                src={item.media.url}
                alt={item.media.alt}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                onError={(e) => {
                  // Fallback to placeholder if image fails to load
                  e.currentTarget.src = `https://via.placeholder.com/400x200/f3f4f6/9ca3af?text=Image+${item.id}`
                }}
              />
            )}
          </div>
        )}

        {/* Content Section */}
        <div className="p-3 sm:p-4 flex flex-col h-full">
          <div className="flex items-start justify-between mb-2">
            <h3 className={`font-medium text-gray-900 flex-1 mr-2 ${
              item.size === 'large' ? 'text-base sm:text-lg line-clamp-2' : 'text-sm sm:text-base line-clamp-2'
            }`}>
              {item.title}
            </h3>
            <span className="text-xs sm:text-sm text-gray-500 whitespace-nowrap">
              {formatDate(item.publishedAt)}
            </span>
          </div>

          <p className={`text-gray-600 mb-2 sm:mb-3 flex-1 ${
            item.size === 'large' ? 'text-xs sm:text-sm line-clamp-3 sm:line-clamp-4' :
            item.size === 'medium' ? 'text-xs sm:text-sm line-clamp-2 sm:line-clamp-3' :
            'text-xs line-clamp-2'
          }`}>
          {/* No change needed here since we're already using Tailwind classes */}
            {item.summary}
          </p>

          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-0 mt-auto">
            <span className="text-xs sm:text-sm text-gray-500 order-2 sm:order-1">{item.source}</span>
            <div className="flex items-center gap-1 sm:gap-2 order-1 sm:order-2">
              {item.type !== 'text' && (
                <span className={`text-xs px-1.5 py-0.5 sm:px-2 sm:py-1 rounded-full ${
                  item.type === 'video' ? 'bg-red-100 text-red-700' : 'bg-blue-100 text-blue-700'
                }`}>
                  {item.type.toUpperCase()}
                </span>
              )}
              <span className={`text-xs px-1.5 py-0.5 sm:px-2 sm:py-1 rounded-full ${
                item.category === 'figma' ? 'bg-purple-100 text-purple-700' :
                item.category === 'design' ? 'bg-green-100 text-green-700' :
                item.category === 'tech' ? 'bg-blue-100 text-blue-700' :
                item.category === 'ui-ux' ? 'bg-orange-100 text-orange-700' :
                'bg-gray-100 text-gray-700'
              }`}>
                {categories.find(c => c.id === item.category)?.label || item.category}
              </span>
            </div>
          </div>
        </div>
      </article>
    )
  }, [categories, formatDate, openNewsItem])

  // State for dynamic container height with react-use optimization
  const [containerHeight, setContainerHeight] = useState(() => {
    // Initial calculation
    return Math.max(400, window.innerHeight - 200)
  })

  // Calculate dynamic container height based on available space
  const calculateContainerHeight = useCallback(() => {
    // Get the actual header height dynamically
    const headerElement = document.querySelector('[data-news-header]')
    const headerHeight = headerElement ? headerElement.getBoundingClientRect().height : 120

    // Calculate available height: window height minus header height and some padding
    const availableHeight = window.innerHeight - headerHeight - 40 // 40px for padding

    // Ensure minimum height for usability
    return Math.max(300, availableHeight)
  }, [])

  // Use react-use hook for window size tracking
  const { width, height } = useWindowSize()

  // Use react-use useTimeoutFn for debounced height updates
  const [, , resetHeightUpdate] = useTimeoutFn(() => {
    const newHeight = calculateContainerHeight()
    setContainerHeight(newHeight)
  }, 100)

  // Update container height when window size changes
  useUpdateEffect(() => {
    resetHeightUpdate()
  }, [width, height, calculateContainerHeight])

  // Initial height calculation on mount with useTimeoutFn
  const [, , resetInitialHeight] = useTimeoutFn(() => {
    const newHeight = calculateContainerHeight()
    setContainerHeight(newHeight)
  }, 200)

  useMount(() => {
    resetInitialHeight()
  })

  // Enhanced performance monitoring for virtual scrolling
  useEffect(() => {
    if (news.length > 0) {
      console.log(`📰 Virtual Scroll Performance:`)
      console.log(`   Total items in memory: ${news.length} (max: ${MAX_NEWS_ITEMS})`)
      console.log(`   DOM elements: ~10-15 (regardless of total items)`)
      console.log(`   Memory efficiency: ${((1 - (15 / news.length)) * 100).toFixed(1)}% reduction`)
      console.log(`   Page: ${page}, Items per page: ${ITEMS_PER_PAGE}`)
    }
  }, [news.length, page])

  return (
    <div className="flex flex-col h-full bg-gray-50">
      {/* Header */}
      <div data-news-header className="bg-white border-b border-gray-200 p-3 sm:p-4 lg:p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 mb-3 sm:mb-4">
          <h2 className="text-lg sm:text-xl lg:text-2xl font-semibold text-gray-900">📰 Design News</h2>
          <button
            onClick={refreshNews}
            disabled={loading}
            className="self-start sm:self-auto p-2 sm:p-2.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors disabled:opacity-50"
            title="Refresh news"
          >
            <svg className={`w-4 h-4 sm:w-5 sm:h-5 ${loading ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap gap-1.5 sm:gap-2">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setNewsState({ selectedCategory: category.id })}
              className={`px-2.5 sm:px-3 py-1 sm:py-1.5 text-xs sm:text-sm rounded-full transition-colors ${
                newsState.selectedCategory === category.id
                  ? 'bg-blue-100 text-blue-700 border border-blue-200'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              {category.label}
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div
        ref={scrollContainerRef}
        className="flex-1 overflow-y-auto"
      >
        {loading && news.length === 0 && (
          <div className="flex items-center justify-center py-8 sm:py-12">
            <div className="flex items-center space-x-2 text-gray-500">
              <svg className="animate-spin w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <span className="text-sm sm:text-base">Loading news...</span>
            </div>
          </div>
        )}

        {newsState.error && (
          <div className="p-3 sm:p-4 m-3 sm:m-4 bg-red-50 border border-red-200 rounded-md">
            <div className="flex items-center">
              <svg className="w-4 h-4 sm:w-5 sm:h-5 text-red-400 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-red-700 text-sm sm:text-base">{newsState.error}</span>
            </div>
          </div>
        )}

        {!loading && !newsState.error && news.length === 0 && (
          <div className="flex items-center justify-center py-8 sm:py-12 text-gray-500">
            <div className="text-center px-4">
              <svg className="w-10 h-10 sm:w-12 sm:h-12 mx-auto mb-2 sm:mb-3 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2.5 2.5 0 00-2.5-2.5H15" />
              </svg>
              <p className="text-sm sm:text-base">No news found for this category</p>
            </div>
          </div>
        )}

        {news.length > 0 && (
          <div className="px-3 sm:px-4 lg:px-6">
            {/* Virtual Scrolling Single Column Layout */}
            <MasonryVirtualScroll
              items={news}
              columns={1}
              estimatedItemHeight={ESTIMATED_ITEM_HEIGHT}
              containerHeight={containerHeight}
              renderItem={renderNewsCard}
              gap={16}
              onScrollEnd={loadingMore || !hasMore ? undefined : loadMoreNews}
            />

            {/* Auto-loading indicator */}
            {loadingMore && (
              <div className="flex items-center justify-center py-4 sm:py-6">
                <div className="flex items-center space-x-2 text-gray-500">
                  <svg className="animate-spin w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  <span className="text-xs sm:text-sm font-medium">Loading more news...</span>
                </div>
              </div>
            )}

            {/* Infinite scroll - keep scrolling for more news */}
          </div>
        )}
      </div>
    </div>
  )
}
