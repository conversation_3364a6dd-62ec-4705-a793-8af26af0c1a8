// Figma Plugin Types and Interfaces
// Consolidated types for all Figma plugin communications

// Window settings interface
export interface WindowSettings {
  width: number
  height: number
}

// Default window settings
export const DEFAULT_WINDOW_SETTINGS: WindowSettings = {
  width: 400,
  height: 600
}

// Storage keys
export const STORAGE_KEYS = {
  WINDOW_SETTINGS: 'figmagent-window-settings',
  OPENROUTER_CONFIG: 'figmagent-openrouter-config',
  DEEPSEEK_CONFIG: 'figmagent-deepseek-config',
  AI_VENDOR: 'figmagent-ai-vendor',
  TERMINOLOGY: 'figmagent-terminology',
  CODE_BLOCK_SETTINGS: 'figmagent-code-block-settings',
  CONVERSATIONS: 'figmagent-conversations',
  CURRENT_CONVERSATION_ID: 'figmagent-current-conversation-id',
  CONVERSATION_PREFIX: 'figmagent-conversation-'
} as const

// Environment detection types
export type FigmaEnvironment = 'figma-plugin' | 'web-dev' | 'unknown'

export interface EnvironmentInfo {
  environment: FigmaEnvironment
  isDevelopment: boolean
  isProduction: boolean
  hasParentWindow: boolean
  userAgent: string
}

// Message types for plugin communication
export const MESSAGE_TYPES = {
  CANCEL: 'cancel',
  SET_WINDOW_SIZE: 'set_window_size',
  GET_FIGMA_SELECTION: 'get_figma_selection',
  FIGMA_SELECTION_RESPONSE: 'figma_selection_response',
  // Storage operations
  STORAGE_GET: 'storage_get',
  STORAGE_SET: 'storage_set',
  STORAGE_DELETE: 'storage_delete',
  STORAGE_RESPONSE: 'storage_response'
} as const

// Base plugin message interface
export interface PluginMessage {
  type: string
  request_id?: string
}

// Window management messages
export interface CancelMessage extends PluginMessage {
  type: 'cancel'
}

export interface SetWindowSizeMessage extends PluginMessage {
  type: 'set_window_size'
  width: number
  height: number
}

// Selection messages
export interface GetFigmaSelectionMessage extends PluginMessage {
  type: 'get_figma_selection'
}

export interface FigmaSelectionResponse extends PluginMessage {
  type: 'figma_selection_response'
  data: Record<string, unknown>
  success?: boolean
  error?: string
}

// Storage messages
export interface StorageGetMessage extends PluginMessage {
  type: 'storage_get'
  key: string
}

export interface StorageSetMessage extends PluginMessage {
  type: 'storage_set'
  key: string
  value: string
}

export interface StorageDeleteMessage extends PluginMessage {
  type: 'storage_delete'
  key: string
}

export interface StorageResponseMessage extends PluginMessage {
  type: 'storage_response'
  success: boolean
  value?: string
  error?: string
}

// Union type for all UI messages
export type UIMessage =
  | CancelMessage
  | SetWindowSizeMessage
  | GetFigmaSelectionMessage
  | FigmaSelectionResponse
  | StorageGetMessage
  | StorageSetMessage
  | StorageDeleteMessage
  | StorageResponseMessage

// Event types for client notifications
export interface FigmaClientEvents {
  'storage-changed': { key: string; value: unknown }
  'window-resized': { width: number; height: number }
  'selection-changed': { data: Record<string, unknown>; success: boolean; error?: string }
  'selection-raw': { data: Record<string, unknown>; success: boolean; error?: string }
}

// Client interface definitions
export interface StorageInterface {
  getItem<T>(key: string, defaultValue: T): Promise<T>
  setItem<T>(key: string, value: T): Promise<void>
  removeItem(key: string): Promise<void>
}

export interface WindowInterface {
  resize(width: number, height: number): Promise<void>
  minimize(): Promise<void>
  close(): Promise<void>
}

export interface SelectionInterface {
  getSelection(): Promise<Record<string, unknown>>
  requestSelection(): void
}

// Commands interface for command execution
export interface CommandsInterface {
  executeSelectionCommand(): void
  onSelectionResult(callback: (result: { data: Record<string, unknown>; success: boolean; error?: string }) => void): () => void
}
