// Performance profiler for debugging ChatInterface performance issues

import React, { useEffect, useState, useRef } from 'react'
import { useInputScheduler } from '../utils/input_scheduler'

interface PerformanceMetrics {
  renderTime: number
  domNodes: number
  memoryUsage: number
  inputLatency: number
  lastUpdate: Date
  // New input-specific metrics
  averageInputLatency: number
  maxInputLatency: number
  inputEventCount: number
  deferredRenderCount: number
  scheduledTaskCount: number
}

interface PerformanceProfilerProps {
  enabled?: boolean
  onMetricsUpdate?: (metrics: PerformanceMetrics) => void
}

export const PerformanceProfiler: React.FC<PerformanceProfilerProps> = ({
  enabled = false,
  onMetricsUpdate
}) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    domNodes: 0,
    memoryUsage: 0,
    inputLatency: 0,
    lastUpdate: new Date(),
    averageInputLatency: 0,
    maxInputLatency: 0,
    inputEventCount: 0,
    deferredRenderCount: 0,
    scheduledTaskCount: 0
  })

  const [isVisible, setIsVisible] = useState(false)
  const renderStartTime = useRef<number>(0)
  const inputStartTime = useRef<number>(0)
  const inputLatencySamples = useRef<number[]>([])

  // Access scheduler for task monitoring
  const scheduler = useInputScheduler()

  // Measure DOM nodes
  const measureDomNodes = (): number => {
    const chatContainer = document.querySelector('[data-index]')?.parentElement?.parentElement
    if (!chatContainer) return 0

    return chatContainer.querySelectorAll('*').length
  }

  // Measure memory usage (if available)
  const measureMemoryUsage = (): number => {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      return memory.usedJSHeapSize / 1024 / 1024 // MB
    }
    return 0
  }

  // Measure input latency
  const measureInputLatency = (): void => {
    const inputElement = document.querySelector('input[type="text"], textarea') as HTMLInputElement
    if (!inputElement) return

    const handleInput = () => {
      if (inputStartTime.current > 0) {
        const latency = performance.now() - inputStartTime.current

        // Add to samples for statistical analysis
        inputLatencySamples.current.push(latency)
        if (inputLatencySamples.current.length > 50) {
          inputLatencySamples.current.shift() // Keep only recent samples
        }

        setMetrics(prev => ({ ...prev, inputLatency: latency }))
      }
    }

    const handleKeyDown = () => {
      inputStartTime.current = performance.now()
    }

    inputElement.addEventListener('keydown', handleKeyDown)
    inputElement.addEventListener('input', handleInput)

    return () => {
      inputElement.removeEventListener('keydown', handleKeyDown)
      inputElement.removeEventListener('input', handleInput)
    }
  }

  // Update metrics with enhanced input tracking
  const updateMetrics = () => {
    // Calculate input latency statistics
    const averageInputLatency = inputLatencySamples.current.length > 0
      ? inputLatencySamples.current.reduce((sum, val) => sum + val, 0) / inputLatencySamples.current.length
      : 0
    const maxInputLatency = inputLatencySamples.current.length > 0
      ? Math.max(...inputLatencySamples.current)
      : 0

    const newMetrics: PerformanceMetrics = {
      renderTime: performance.now() - renderStartTime.current,
      domNodes: measureDomNodes(),
      memoryUsage: measureMemoryUsage(),
      inputLatency: metrics.inputLatency,
      lastUpdate: new Date(),
      averageInputLatency,
      maxInputLatency,
      inputEventCount: inputLatencySamples.current.length,
      deferredRenderCount: 0, // This would be tracked by the components using deferred values
      scheduledTaskCount: scheduler.getPendingTaskCount()
    }

    setMetrics(newMetrics)
    onMetricsUpdate?.(newMetrics)
  }

  // Performance observer for render timing
  useEffect(() => {
    if (!enabled) return

    renderStartTime.current = performance.now()

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry) => {
        if (entry.entryType === 'measure' || entry.entryType === 'navigation') {
          updateMetrics()
        }
      })
    })

    try {
      observer.observe({ entryTypes: ['measure', 'navigation'] })
    } catch (e) {
      // Fallback for browsers that don't support all entry types
      console.warn('Performance observer not fully supported')
    }

    // Set up input latency measurement
    const cleanup = measureInputLatency()

    // Update metrics periodically
    const interval = setInterval(updateMetrics, 1000)

    return () => {
      observer.disconnect()
      cleanup?.()
      clearInterval(interval)
    }
  }, [enabled])

  // Keyboard shortcut to toggle visibility
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'P') {
        setIsVisible(prev => !prev)
      }
    }

    window.addEventListener('keydown', handleKeyPress)
    return () => window.removeEventListener('keydown', handleKeyPress)
  }, [])

  if (!enabled || !isVisible) {
    return (
      <div
        className="performance-profiler-toggle"
        onClick={() => setIsVisible(true)}
        title="Click to show performance metrics (or press Ctrl+Shift+P)"
      >
        📊
      </div>
    )
  }

  const getPerformanceColor = (value: number, thresholds: [number, number]): string => {
    if (value < thresholds[0]) return '#22c55e' // green
    if (value < thresholds[1]) return '#f59e0b' // yellow
    return '#ef4444' // red
  }

  return (
    <div className="performance-profiler">
      <div className="profiler-header">
        <h4>Performance Metrics</h4>
        <button onClick={() => setIsVisible(false)}>×</button>
      </div>

      <div className="metrics-grid">
        <div className="metric">
          <label>Render Time</label>
          <span
            className="metric-value"
            style={{ color: getPerformanceColor(metrics.renderTime, [16, 33]) }}
          >
            {metrics.renderTime.toFixed(1)}ms
          </span>
        </div>

        <div className="metric">
          <label>DOM Nodes</label>
          <span
            className="metric-value"
            style={{ color: getPerformanceColor(metrics.domNodes, [1000, 5000]) }}
          >
            {metrics.domNodes.toLocaleString()}
          </span>
        </div>

        <div className="metric">
          <label>Memory Usage</label>
          <span
            className="metric-value"
            style={{ color: getPerformanceColor(metrics.memoryUsage, [50, 100]) }}
          >
            {metrics.memoryUsage.toFixed(1)}MB
          </span>
        </div>

        <div className="metric">
          <label>Input Latency</label>
          <span
            className="metric-value"
            style={{ color: getPerformanceColor(metrics.inputLatency, [16, 33]) }}
          >
            {metrics.inputLatency.toFixed(1)}ms
          </span>
        </div>

        <div className="metric">
          <label>Avg Input Latency</label>
          <span
            className="metric-value"
            style={{ color: getPerformanceColor(metrics.averageInputLatency, [16, 33]) }}
          >
            {metrics.averageInputLatency.toFixed(1)}ms
          </span>
        </div>

        <div className="metric">
          <label>Max Input Latency</label>
          <span
            className="metric-value"
            style={{ color: getPerformanceColor(metrics.maxInputLatency, [16, 50]) }}
          >
            {metrics.maxInputLatency.toFixed(1)}ms
          </span>
        </div>

        <div className="metric">
          <label>Input Events</label>
          <span className="metric-value">
            {metrics.inputEventCount}
          </span>
        </div>

        <div className="metric">
          <label>Scheduled Tasks</label>
          <span
            className="metric-value"
            style={{ color: getPerformanceColor(metrics.scheduledTaskCount, [5, 20]) }}
          >
            {metrics.scheduledTaskCount}
          </span>
        </div>
      </div>

      <div className="profiler-footer">
        <span className="last-update">
          Updated: {metrics.lastUpdate.toLocaleTimeString()}
        </span>
      </div>

      <style jsx>{`
        .performance-profiler-toggle {
          position: fixed;
          bottom: 20px;
          left: 20px;
          width: 40px;
          height: 40px;
          background: #1f2937;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          font-size: 18px;
          z-index: 1000;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          transition: all 0.2s;
        }

        .performance-profiler-toggle:hover {
          background: #374151;
          transform: scale(1.1);
        }

        .performance-profiler {
          position: fixed;
          bottom: 20px;
          left: 20px;
          width: 320px;
          background: white;
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
          z-index: 1000;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .profiler-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 16px;
          border-bottom: 1px solid #e5e7eb;
          background: #f9fafb;
          border-radius: 8px 8px 0 0;
        }

        .profiler-header h4 {
          margin: 0;
          font-size: 14px;
          font-weight: 600;
          color: #1f2937;
        }

        .profiler-header button {
          background: none;
          border: none;
          font-size: 18px;
          cursor: pointer;
          color: #6b7280;
          padding: 0;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .profiler-header button:hover {
          color: #374151;
        }

        .metrics-grid {
          padding: 16px;
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 8px;
          max-height: 300px;
          overflow-y: auto;
        }

        .metric {
          display: flex;
          flex-direction: column;
          gap: 4px;
        }

        .metric label {
          font-size: 11px;
          color: #6b7280;
          font-weight: 500;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .metric-value {
          font-size: 16px;
          font-weight: 600;
          font-family: 'SF Mono', Monaco, monospace;
        }

        .profiler-footer {
          padding: 8px 16px;
          border-top: 1px solid #e5e7eb;
          background: #f9fafb;
          border-radius: 0 0 8px 8px;
        }

        .last-update {
          font-size: 10px;
          color: #9ca3af;
        }
      `}</style>
    </div>
  )
}

export default PerformanceProfiler
