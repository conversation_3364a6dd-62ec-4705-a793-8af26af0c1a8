// Debug component to verify virtualization is working correctly

import React, { useEffect, useState } from 'react'

interface VirtualizationDebugProps {
  totalMessages: number
  className?: string
}

export const VirtualizationDebug: React.FC<VirtualizationDebugProps> = ({ 
  totalMessages, 
  className = '' 
}) => {
  const [renderedElements, setRenderedElements] = useState(0)
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const updateCount = () => {
      // Count virtual message elements in the DOM
      const virtualMessages = document.querySelectorAll('[data-index]')
      setRenderedElements(virtualMessages.length)
    }

    // Update count periodically
    const interval = setInterval(updateCount, 1000)
    updateCount() // Initial count

    return () => clearInterval(interval)
  }, [])

  // Toggle visibility with keyboard shortcut
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'D') {
        setIsVisible(prev => !prev)
      }
    }

    window.addEventListener('keydown', handleKeyPress)
    return () => window.removeEventListener('keydown', handleKeyPress)
  }, [])

  if (!isVisible) {
    return (
      <div 
        className={`fixed bottom-4 right-4 bg-blue-600 text-white px-2 py-1 rounded text-xs cursor-pointer ${className}`}
        onClick={() => setIsVisible(true)}
        title="Click to show virtualization debug info (or press Ctrl+Shift+D)"
      >
        Debug
      </div>
    )
  }

  const efficiency = totalMessages > 0 ? ((totalMessages - renderedElements) / totalMessages * 100).toFixed(1) : '0'

  return (
    <div className={`fixed bottom-4 right-4 bg-gray-900 text-white p-3 rounded shadow-lg text-xs ${className}`}>
      <div className="flex justify-between items-center mb-2">
        <h4 className="font-semibold">Virtualization Debug</h4>
        <button 
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-white"
        >
          ×
        </button>
      </div>
      <div className="space-y-1">
        <div>Total Messages: <span className="font-mono">{totalMessages}</span></div>
        <div>Rendered in DOM: <span className="font-mono">{renderedElements}</span></div>
        <div>Efficiency: <span className="font-mono text-green-400">{efficiency}%</span></div>
        <div className="text-gray-400 text-xs mt-2">
          Press Ctrl+Shift+D to toggle
        </div>
      </div>
    </div>
  )
}

export default VirtualizationDebug
