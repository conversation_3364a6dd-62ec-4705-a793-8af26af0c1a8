// Test for integrated conversation flow in multi-turn tool calling
// This test verifies that tool results are seamlessly integrated into AI messages

import { OpenRouterClient } from './client'
import { OpenRouterConfig, AiMessage, AiToolCall, ToolExecutionResult } from '../../ui/types/ui_types'

// Mock configuration for testing
const mockConfig: OpenRouterConfig = {
  apiKey: 'test-key',
  model: 'openai/gpt-4o-mini',
  temperature: 0.7,
  maxTokens: 1000,
  systemPrompt: ''
}

// Mock fetch for testing
const mockFetch = jest.fn()
global.fetch = mockFetch

// Mock tool execution
jest.mock('./tools', () => ({
  executeTool: jest.fn().mockResolvedValue({
    success: true,
    data: {
      figma: {
        id: 'test-123',
        type: 'FRAME',
        name: 'Test Frame',
        x: 100,
        y: 200,
        width: 300,
        height: 400,
        css: {
          'background-color': '#ffffff',
          'border-radius': '8px',
          'padding': '16px'
        }
      }
    }
  }),
  FIGMA_TOOLS: [
    {
      type: 'function',
      function: {
        name: 'get_figma_selection_json',
        description: 'Get Figma selection as JSON',
        parameters: {
          type: 'object',
          properties: {},
          required: []
        }
      }
    }
  ]
}))

describe('Integrated Conversation Flow', () => {
  let client: OpenRouterClient

  beforeEach(() => {
    client = new OpenRouterClient(mockConfig)
    mockFetch.mockClear()
  })

  test('should integrate tool calls and results into conversation flow', async () => {
    // Mock API responses for integrated conversation
    const mockResponses = [
      {
        id: 'test-1',
        model: 'openai/gpt-4o-mini',
        choices: [{
          message: {
            role: 'assistant',
            content: 'I\'ll analyze your Figma selection to provide insights.',
            tool_calls: [{
              id: 'call-1',
              type: 'function',
              function: {
                name: 'get_figma_selection_json',
                arguments: '{}'
              }
            }]
          },
          finish_reason: 'tool_calls'
        }],
        usage: { prompt_tokens: 10, completion_tokens: 15, total_tokens: 25 }
      },
      {
        id: 'test-2',
        model: 'openai/gpt-4o-mini',
        choices: [{
          message: {
            role: 'assistant',
            content: 'Based on the selection data, I can see you have a 300x400 frame with clean styling. The white background and rounded corners create a modern card-like appearance. Here are my recommendations:\n\n1. **Visual Hierarchy**: Consider adding subtle shadows for depth\n2. **Responsive Design**: The current dimensions work well for desktop\n3. **Accessibility**: The contrast is good with the white background'
          },
          finish_reason: 'stop'
        }],
        usage: { prompt_tokens: 80, completion_tokens: 60, total_tokens: 140 }
      }
    ]

    mockResponses.forEach(response => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(response)
      })
    })

    const conversation: OpenRouterMessage[] = []
    const conversationFlow: Array<{
      turn: number
      type: 'ai_thinking' | 'tool_call' | 'tool_result' | 'ai_response'
      content: string
      toolName?: string
    }> = []

    const response = await client.sendMultiTurnMessage(
      'Analyze my Figma selection and provide design recommendations',
      conversation,
      5,
      (toolCall, result) => {
        conversationFlow.push({
          turn: 1,
          type: 'tool_call',
          content: `Executing ${toolCall.function.name}`,
          toolName: toolCall.function.name
        })
        conversationFlow.push({
          turn: 1,
          type: 'tool_result',
          content: JSON.stringify(result.data, null, 2)
        })
      },
      (turn, response) => {
        const content = response.choices[0]?.message?.content || ''
        const hasToolCalls = response.choices[0]?.message?.tool_calls && response.choices[0].message.tool_calls.length > 0

        if (content && hasToolCalls) {
          conversationFlow.push({
            turn,
            type: 'ai_thinking',
            content
          })
        } else if (content && !hasToolCalls) {
          conversationFlow.push({
            turn,
            type: 'ai_response',
            content
          })
        }
      }
    )

    // Verify integrated conversation flow
    expect(conversationFlow).toHaveLength(4)

    // Turn 1: AI thinking + tool execution
    expect(conversationFlow[0]).toMatchObject({
      turn: 1,
      type: 'ai_thinking',
      content: expect.stringContaining('analyze your Figma selection')
    })

    expect(conversationFlow[1]).toMatchObject({
      type: 'tool_call',
      toolName: 'get_figma_selection_json'
    })

    expect(conversationFlow[2]).toMatchObject({
      type: 'tool_result',
      content: expect.stringContaining('Test Frame')
    })

    // Turn 2: Final AI response with analysis
    expect(conversationFlow[3]).toMatchObject({
      turn: 2,
      type: 'ai_response',
      content: expect.stringContaining('recommendations')
    })

    // Verify conversation contains proper context
    expect(conversation).toHaveLength(4) // user + ai_thinking + tool_result + ai_response
    expect(conversation[1].role).toBe('assistant')
    expect(conversation[1].content).toContain('analyze your Figma selection')
    expect(conversation[2].role).toBe('tool')
    expect(conversation[3].role).toBe('assistant')
    expect(conversation[3].content).toContain('recommendations')
  })

  test('should handle multiple tool calls in integrated flow', async () => {
    // Mock responses for multiple tool calls
    const mockResponses = [
      {
        id: 'test-1',
        model: 'openai/gpt-4o-mini',
        choices: [{
          message: {
            role: 'assistant',
            content: 'I\'ll get both the structure and styling data for comprehensive analysis.',
            tool_calls: [{
              id: 'call-1',
              type: 'function',
              function: {
                name: 'get_figma_selection_json',
                arguments: '{}'
              }
            }]
          },
          finish_reason: 'tool_calls'
        }],
        usage: { prompt_tokens: 10, completion_tokens: 15, total_tokens: 25 }
      },
      {
        id: 'test-2',
        model: 'openai/gpt-4o-mini',
        choices: [{
          message: {
            role: 'assistant',
            content: 'Now let me get the CSS styles to complete the analysis.',
            tool_calls: [{
              id: 'call-2',
              type: 'function',
              function: {
                name: 'get_figma_selection_css',
                arguments: '{}'
              }
            }]
          },
          finish_reason: 'tool_calls'
        }],
        usage: { prompt_tokens: 30, completion_tokens: 15, total_tokens: 45 }
      },
      {
        id: 'test-3',
        model: 'openai/gpt-4o-mini',
        choices: [{
          message: {
            role: 'assistant',
            content: 'Perfect! I now have both the structure and styling. Here\'s my complete analysis with actionable recommendations.'
          },
          finish_reason: 'stop'
        }],
        usage: { prompt_tokens: 80, completion_tokens: 25, total_tokens: 105 }
      }
    ]

    mockResponses.forEach(response => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(response)
      })
    })

    const conversation: OpenRouterMessage[] = []
    let toolCallCount = 0

    const response = await client.sendMultiTurnMessage(
      'Get comprehensive data about my selection',
      conversation,
      5,
      (toolCall, result) => {
        toolCallCount++
      }
    )

    // Verify multiple tool calls were executed
    expect(toolCallCount).toBe(2)
    expect(mockFetch).toHaveBeenCalledTimes(3)

    // Verify conversation structure includes all interactions
    expect(conversation.length).toBeGreaterThan(5) // user + multiple ai + tool messages

    // Check for proper conversation flow
    const aiMessages = conversation.filter(msg => msg.role === 'assistant')
    const toolMessages = conversation.filter(msg => msg.role === 'tool')

    expect(aiMessages.length).toBe(3) // Three AI responses
    expect(toolMessages.length).toBe(2) // Two tool executions
  })

  test('should maintain chronological order in conversation', async () => {
    const mockResponse = {
      id: 'test-1',
      model: 'openai/gpt-4o-mini',
      choices: [{
        message: {
          role: 'assistant',
          content: 'Let me check your selection.',
          tool_calls: [{
            id: 'call-1',
            type: 'function',
            function: {
              name: 'get_figma_selection_json',
              arguments: '{}'
            }
          }]
        },
        finish_reason: 'tool_calls'
      }],
      usage: { prompt_tokens: 10, completion_tokens: 5, total_tokens: 15 }
    }

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve(mockResponse)
    })

    const conversation: OpenRouterMessage[] = []
    const timestamps: Date[] = []

    await client.sendMultiTurnMessage(
      'Check my selection',
      conversation,
      5,
      (toolCall, result) => {
        timestamps.push(new Date())
      },
      (turn, response) => {
        timestamps.push(new Date())
      }
    )

    // Verify chronological order
    for (let i = 1; i < timestamps.length; i++) {
      expect(timestamps[i].getTime()).toBeGreaterThanOrEqual(timestamps[i-1].getTime())
    }

    // Verify conversation maintains proper order
    expect(conversation[0].role).toBe('user')
    expect(conversation[1].role).toBe('assistant')
    if (conversation.length > 2) {
      expect(conversation[2].role).toBe('tool')
    }
  })
})
