// AI Tool Calling Functions for Figma Commands
// Defines the available tools that AI models can call to interact with Figma

import { AiTool, ToolExecutionResult } from '../../ui/types/ui_types'
import { getFigmaClient } from '../figma'

// Tool definitions for AI function calling (vendor-agnostic)
export const FIGMA_TOOLS: AiTool[] = [
  {
    type: 'function',
    function: {
      name: 'get_figma_selection_json',
      description: 'Extract the currently selected Figma objects as detailed JSON data including all properties, CSS styles, and hierarchy information',
      parameters: {
        type: 'object',
        properties: {},
        required: []
      }
    }
  },
  {
    type: 'function',
    function: {
      name: 'get_figma_selection_css',
      description: 'Extract CSS styles from the currently selected Figma objects, formatted as CSS rules with selectors',
      parameters: {
        type: 'object',
        properties: {},
        required: []
      }
    }
  },
  {
    type: 'function',
    function: {
      name: 'get_figma_selection_tailwind_html',
      description: 'Generate Tailwind CSS HTML structure from the currently selected Figma objects, including nested children and converted CSS classes',
      parameters: {
        type: 'object',
        properties: {},
        required: []
      }
    }
  }
]

// Type definitions for better type safety
interface FigmaNode {
  type?: string
  name?: string
  id?: string
  characters?: string
  css?: Record<string, unknown>
  children?: Array<{ figma?: FigmaNode } | FigmaNode>
  mainComponent?: string | { id: string }
  [key: string]: unknown
}

// Helper function to convert CSS object to CSS string
const styleObjectToCss = (styleObj: Record<string, unknown>): string => {
  return Object.entries(styleObj)
    .map(([property, value]) => `  ${property}: ${value};`)
    .join('\n')
}

// Helper function to convert CSS object to Tailwind classes
const convertCssToTailwind = (cssObj: Record<string, unknown>): string => {
  try {
    // Import the library dynamically to avoid issues
    const CssToTailwindTranslator = require('css-to-tailwind-translator').default || require('css-to-tailwind-translator')
    const cssString = styleObjectToCss(cssObj)
    const fullCssRule = `.temp-class {\n${cssString}\n}`
    const result = CssToTailwindTranslator(fullCssRule)
    return result.data[0]?.resultVal || ''
  } catch (error) {
    console.warn('Failed to convert CSS to Tailwind:', error)
    return ''
  }
}

// Helper function to get appropriate HTML tag for Figma node type
const getHtmlTag = (nodeType: string): string => {
  const tagMap: Record<string, string> = {
    'FRAME': 'div',
    'GROUP': 'div',
    'RECTANGLE': 'div',
    'ELLIPSE': 'div',
    'TEXT': 'span',
    'VECTOR': 'svg',
    'INSTANCE': 'div',
    'COMPONENT': 'div',
    'BOOLEAN_OPERATION': 'div'
  }
  return tagMap[nodeType] || 'div'
}

// Helper function to get display name for node
const getNodeDisplayName = (node: FigmaNode, nodeType: string): string => {
  if (nodeType === 'TEXT' && node.name) {
    return String(node.name)
  }
  return String(node.name || 'unnamed')
}

// Helper function to sanitize text for HTML
const sanitizeForHtml = (text: string): string => {
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;')
}

// Pure function to generate HTML structure from Figma nodes
const generateHtmlFromNode = (node: FigmaNode, depth: number = 0): string => {
  if (!node || typeof node !== 'object') {
    return `${'  '.repeat(depth)}<!-- Invalid node -->`
  }

  const indent = '  '.repeat(depth)
  const nodeType = String(node.type || 'UNKNOWN')
  const name = getNodeDisplayName(node, nodeType)
  const tag = getHtmlTag(nodeType)

  // Convert CSS to Tailwind classes
  let tailwindClasses = ''
  if (node.css && typeof node.css === 'object') {
    tailwindClasses = convertCssToTailwind(node.css)
  }

  // Generate comments with node information
  const nodeInfo = [
    `Type: ${nodeType}`,
    `ID: ${node.id || 'unknown'}`,
    node.mainComponent ? `Component: ${typeof node.mainComponent === 'object' ? node.mainComponent.id : node.mainComponent}` : null
  ].filter(Boolean).join(', ')

  let html = `${indent}<!-- ${name} (${nodeInfo}) -->\n`
  html += `${indent}<${tag}`

  if (tailwindClasses) {
    html += ` class="${tailwindClasses}"`
  }

  // Handle self-closing tags for certain node types
  if (nodeType === 'VECTOR' || (nodeType === 'TEXT' && !node.children?.length)) {
    if (nodeType === 'TEXT' && node.name) {
      const textContent = sanitizeForHtml(String(node.name))
      html += `>${textContent}</${tag}>`
    } else {
      html += ' />'
    }
    return html
  } else {
    html += '>'

    // Process children if they exist
    if (node.children && Array.isArray(node.children) && node.children.length > 0) {
      html += '\n'
      for (const child of node.children) {
        const childNode = (child as any).figma || child
        const childHtml = generateHtmlFromNode(childNode as FigmaNode, depth + 1)
        if (childHtml.trim()) {
          html += childHtml + '\n'
        }
      }
      html += indent
    } else if (nodeType === 'TEXT' && node.characters) {
      const textContent = sanitizeForHtml(String(node.characters))
      html += textContent
    }

    html += `</${tag}>`
    return html
  }
}

// Core tool execution functions
export async function executeFigmaSelectionJson(): Promise<ToolExecutionResult> {
  try {
    const selection = await getFigmaClient().selection.getSelection()
    return {
      success: true,
      data: selection
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

export async function executeFigmaSelectionCss(): Promise<ToolExecutionResult> {
  try {
    const selection = await getFigmaClient().selection.getSelection()
    let cssText = ''

    if (Array.isArray(selection) && selection.length > 0) {
      cssText = selection.map((nodeWrapper: any, index: number) => {
        const node = nodeWrapper.figma || nodeWrapper
        const name = String(node.name || `element-${index}`)
        const selector = `.${name.toLowerCase().replace(/\s+/g, '-')}`

        if (node.css && typeof node.css === 'object') {
          const cssStyles = styleObjectToCss(node.css)
          return `${selector} {\n${cssStyles}\n}`
        } else {
          return `${selector} {\n  /* No CSS available for this element */\n}`
        }
      }).join('\n\n')
    } else {
      cssText = '/* No selection found */'
    }

    return {
      success: true,
      data: cssText
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

export async function executeFigmaSelectionTailwindHtml(): Promise<ToolExecutionResult> {
  try {
    const selection = await getFigmaClient().selection.getSelection()
    let htmlText = ''

    if (Array.isArray(selection) && selection.length > 0) {
      htmlText = selection.map((nodeWrapper: any) => {
        const node = nodeWrapper.figma || nodeWrapper
        return generateHtmlFromNode(node, 0)
      }).join('\n\n')
    } else {
      htmlText = '<!-- No selection found -->'
    }

    return {
      success: true,
      data: htmlText
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

// Tool execution mapping
export const TOOL_EXECUTORS: Record<string, () => Promise<ToolExecutionResult>> = {
  'get_figma_selection_json': executeFigmaSelectionJson,
  'get_figma_selection_css': executeFigmaSelectionCss,
  'get_figma_selection_tailwind_html': executeFigmaSelectionTailwindHtml
}

// Execute a tool by name
export async function executeTool(toolName: string, args: Record<string, unknown> = {}): Promise<ToolExecutionResult> {
  const executor = TOOL_EXECUTORS[toolName]
  if (!executor) {
    return {
      success: false,
      error: `Unknown tool: ${toolName}`
    }
  }

  try {
    return await executor()
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Tool execution failed'
    }
  }
}
