// OpenRouter Models Types
// Types for the models data fetched from OpenRouter API

export interface OpenRouterModelPricing {
  prompt: number
  completion: number
}

export interface OpenRouterModelData {
  id: string
  name: string
  description: string
  pricing: OpenRouterModelPricing
  contextLength: number
  maxCompletionTokens?: number
}

export interface OpenRouterModelsResponse {
  models: OpenRouterModelData[]
  lastUpdated: number | null
  totalCount: number
}

// Autocomplete suggestion item
export interface ModelSuggestion {
  id: string
  name: string
  description: string
  pricing: OpenRouterModelPricing
  contextLength: number
  matchScore: number // For sorting suggestions by relevance
}
