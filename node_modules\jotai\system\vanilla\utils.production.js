System.register(["jotai/vanilla"],function(R){"use strict";var m;return{setters:[function(W){m=W.atom}],execute:function(){R({atomFamily:$,atomWithDefault:X,atomWithLazy:mt,atomWithObservable:nt,atomWithReducer:P,atomWithRefresh:ft,atomWithReset:J,atomWithStorage:tt,createJSONStorage:z,freezeAtom:T,freezeAtomCreator:q,loadable:it,selectAtom:K,splitAtom:U,unstable_withStorageValidator:Y,unwrap:lt});const W=R("RESET",Symbol(""));function J(t){const e=m(t,(i,c,u)=>{const a=typeof u=="function"?u(i(e)):u;c(e,a===W?t:a)});return e}function P(t,e){return m(t,function(i,c,u){c(this,e(i(this),u))})}function $(t,e){let i=null;const c=new Map,u=new Set,a=r=>{let o;if(e===void 0)o=c.get(r);else for(const[l,f]of c)if(e(l,r)){o=f;break}if(o!==void 0)if(i!=null&&i(o[1],r))a.remove(r);else return o[0];const s=t(r);return c.set(r,[s,Date.now()]),n("CREATE",r,s),s},n=(r,o,s)=>{for(const l of u)l({type:r,param:o,atom:s})};return a.unstable_listen=r=>(u.add(r),()=>{u.delete(r)}),a.getParams=()=>c.keys(),a.remove=r=>{if(e===void 0){if(!c.has(r))return;const[o]=c.get(r);c.delete(r),n("REMOVE",r,o)}else for(const[o,[s]]of c)if(e(o,r)){c.delete(o),n("REMOVE",o,s);break}},a.setShouldRemove=r=>{if(i=r,!!i)for(const[o,[s,l]]of c)i(l,o)&&(c.delete(o),n("REMOVE",o,s))},a}const M=(t,e,i)=>(e.has(i)?e:e.set(i,t())).get(i),C=new WeakMap,F=(t,e,i,c)=>{const u=M(()=>new WeakMap,C,e),a=M(()=>new WeakMap,u,i);return M(t,a,c)};function K(t,e,i=Object.is){return F(()=>{const c=Symbol(),u=([n,r])=>{if(r===c)return e(n);const o=e(n,r);return i(r,o)?r:o},a=m(n=>{const r=n(a),o=n(t);return u([o,r])});return a.init=c,a},t,e,i)}const A=new WeakSet,L=t=>{if(typeof t!="object"||t===null)return t;Object.freeze(t);const e=Object.getOwnPropertyNames(t);for(const i of e)L(t[i]);return t};function T(t){if(A.has(t))return t;A.add(t);const e=t.read;if(t.read=function(i,c){return L(e.call(this,i,c))},"write"in t){const i=t.write;t.write=function(c,u,...a){return i.call(this,c,(...n)=>(n[0]===t&&(n[1]=L(n[1])),u(...n)),...a)}}return t}function q(t){return(...e)=>T(t(...e))}const V=(t,e,i)=>(e.has(i)?e:e.set(i,t())).get(i),B=new WeakMap,G=(t,e,i)=>{const c=V(()=>new WeakMap,B,e);return V(t,c,i)},H={},j=t=>!!t.write,Q=t=>typeof t=="function";function U(t,e){return G(()=>{const i=new WeakMap,c=(n,r)=>{let o=i.get(n);if(o)return o;const s=r&&i.get(r),l=[],f=[];return n.forEach((b,v)=>{const d=e?e(b):v;f[v]=d;const g=s&&s.atomList[s.keyList.indexOf(d)];if(g){l[v]=g;return}const O=w=>{const y=w(u),h=w(t),S=c(h,y==null?void 0:y.arr).keyList.indexOf(d);if(S<0||S>=h.length){const p=n[c(n).keyList.indexOf(d)];if(p)return p;throw new Error("splitAtom: index out of bounds for read")}return h[S]},E=(w,y,h)=>{const S=w(u),p=w(t),k=c(p,S==null?void 0:S.arr).keyList.indexOf(d);if(k<0||k>=p.length)throw new Error("splitAtom: index out of bounds for write");const D=Q(h)?h(p[k]):h;Object.is(p[k],D)||y(t,[...p.slice(0,k),D,...p.slice(k+1)])};l[v]=j(t)?m(O,E):m(O)}),s&&s.keyList.length===f.length&&s.keyList.every((b,v)=>b===f[v])?o=s:o={arr:n,atomList:l,keyList:f},i.set(n,o),o},u=m(n=>{const r=n(u),o=n(t);return c(o,r==null?void 0:r.arr)});u.init=void 0;const a=j(t)?m(n=>n(u).atomList,(n,r,o)=>{switch(o.type){case"remove":{const s=n(a).indexOf(o.atom);if(s>=0){const l=n(t);r(t,[...l.slice(0,s),...l.slice(s+1)])}break}case"insert":{const s=o.before?n(a).indexOf(o.before):n(a).length;if(s>=0){const l=n(t);r(t,[...l.slice(0,s),o.value,...l.slice(s)])}break}case"move":{const s=n(a).indexOf(o.atom),l=o.before?n(a).indexOf(o.before):n(a).length;if(s>=0&&l>=0){const f=n(t);s<l?r(t,[...f.slice(0,s),...f.slice(s+1,l),f[s],...f.slice(l)]):r(t,[...f.slice(0,l),f[s],...f.slice(l,s),...f.slice(s+1)])}break}}}):m(n=>n(u).atomList);return a},t,e||H)}function X(t){const e=Symbol(),i=m(e),c=m((u,a)=>{const n=u(i);return n!==e?n:t(u,a)},(u,a,n)=>{if(n===W)a(i,e);else if(typeof n=="function"){const r=u(c);a(i,n(r))}else a(i,n)});return c}const I=t=>typeof(t==null?void 0:t.then)=="function";function Y(t){return e=>({...e,getItem:(i,c)=>{const u=n=>t(n)?n:c,a=e.getItem(i,c);return I(a)?a.then(u):u(a)}})}function z(t=()=>{try{return window.localStorage}catch(i){return}},e){var i;let c,u;const a={getItem:(o,s)=>{var l,f;const b=d=>{if(d=d||"",c!==d){try{u=JSON.parse(d,e==null?void 0:e.reviver)}catch(g){return s}c=d}return u},v=(f=(l=t())==null?void 0:l.getItem(o))!=null?f:null;return I(v)?v.then(b):b(v)},setItem:(o,s)=>{var l;return(l=t())==null?void 0:l.setItem(o,JSON.stringify(s,e==null?void 0:e.replacer))},removeItem:o=>{var s;return(s=t())==null?void 0:s.removeItem(o)}},n=o=>(s,l,f)=>o(s,b=>{let v;try{v=JSON.parse(b||"")}catch(d){v=f}l(v)});let r;try{r=(i=t())==null?void 0:i.subscribe}catch(o){}return!r&&typeof window!="undefined"&&typeof window.addEventListener=="function"&&window.Storage&&(r=(o,s)=>{if(!(t()instanceof window.Storage))return()=>{};const l=f=>{f.storageArea===t()&&f.key===o&&s(f.newValue)};return window.addEventListener("storage",l),()=>{window.removeEventListener("storage",l)}}),r&&(a.subscribe=n(r)),a}const Z=z();function tt(t,e,i=Z,c){const u=c==null?void 0:c.getOnInit,a=m(u?i.getItem(t,e):e);return a.onMount=n=>{n(i.getItem(t,e));let r;return i.subscribe&&(r=i.subscribe(t,n,e)),r},m(n=>n(a),(n,r,o)=>{const s=typeof o=="function"?o(n(a)):o;return s===W?(r(a,e),i.removeItem(t)):I(s)?s.then(l=>(r(a,l),i.setItem(t,l))):(r(a,s),i.setItem(t,s))})}const et=t=>typeof(t==null?void 0:t.then)=="function";function nt(t,e){const i=u=>{if("e"in u)throw u.e;return u.d},c=m(u=>{var a;let n=t(u);const r=(a=n[Symbol.observable])==null?void 0:a.call(n);r&&(n=r);let o;const s=()=>new Promise(h=>{o=h}),l=e&&"initialValue"in e?{d:typeof e.initialValue=="function"?e.initialValue():e.initialValue}:s();let f,b;const v=h=>{b=h,o==null||o(h),f==null||f(h)};let d,g;const O=()=>!f,E=()=>{d&&(d.unsubscribe(),d=void 0)},w=()=>{d&&(clearTimeout(g),d.unsubscribe()),d=n.subscribe({next:h=>v({d:h}),error:h=>v({e:h}),complete:()=>{}}),O()&&e!=null&&e.unstable_timeout&&(g=setTimeout(E,e.unstable_timeout))};w();const y=m(b||l);return y.onMount=h=>(f=h,b&&h(b),d?clearTimeout(g):w(),()=>{f=void 0,e!=null&&e.unstable_timeout?g=setTimeout(E,e.unstable_timeout):E()}),[y,n,s,w,O]});return m(u=>{const[a]=u(c),n=u(a);return et(n)?n.then(i):i(n)},(u,a,n)=>{const[r,o,s,l,f]=u(c);if("next"in o)f()&&(a(r,s()),l()),o.next(n);else throw new Error("observable is not subject")})}const x=new WeakMap,rt=(t,e)=>(x.has(e)?x:x.set(e,t())).get(e),ot=t=>typeof(t==null?void 0:t.then)=="function",N={state:"loading"};function it(t){return rt(()=>{const e=new WeakMap,i=m(0),c=m((u,{setSelf:a})=>{u(i);let n;try{n=u(t)}catch(l){return{state:"hasError",error:l}}if(!ot(n))return{state:"hasData",data:n};const r=n,o=e.get(r);return o||(r.then(l=>{e.set(r,{state:"hasData",data:l}),a()},l=>{e.set(r,{state:"hasError",error:l}),a()}),e.get(r)||(e.set(r,N),N))},(u,a)=>{a(i,n=>n+1)});return m(u=>u(c))},t)}const _=(t,e,i)=>(e.has(i)?e:e.set(i,t())).get(i),st=new WeakMap,at=(t,e,i)=>{const c=_(()=>new WeakMap,st,e);return _(t,c,i)},ut=t=>typeof(t==null?void 0:t.then)=="function",ct=()=>{};function lt(t,e=ct){return at(()=>{const i=new WeakMap,c=new WeakMap,u=m(0),a=m((n,{setSelf:r})=>{n(u);const o=n(a),s=n(t);if(!ut(s))return{v:s};if(s!==(o==null?void 0:o.p)&&s.then(l=>{c.set(s,l),r()},l=>{i.set(s,l),r()}),i.has(s))throw i.get(s);return c.has(s)?{p:s,v:c.get(s)}:o&&"v"in o?{p:s,f:e(o.v),v:o.v}:{p:s,f:e()}},(n,r)=>{r(u,o=>o+1)});return a.init=void 0,m(n=>{const r=n(a);return"f"in r?r.f:r.v},(n,r,...o)=>r(t,...o))},t,e)}function ft(t,e){const i=m(0);return m((c,u)=>(c(i),t(c,u)),(c,u,...a)=>{if(a.length===0)u(i,n=>n+1);else if(e)return e(c,u,...a)})}function mt(t){const e=m(void 0);return delete e.init,Object.defineProperty(e,"init",{get(){return t()}}),e}}}});
