!function(e){"function"==typeof define&&define.amd?define(e):e()}((function(){"use strict";var e=e||{};function s(e){return{subLanguage:"xml",contains:[e.COMMENT("\x3c!--","--\x3e",{relevance:10}),{begin:/^(\s*)(<script>)/gm,end:/^(\s*)(<\/script>)/gm,subLanguage:"javascript",excludeBegin:!0,excludeEnd:!0},{begin:/^(\s*)(<script lang=["']ts["']>)/gm,end:/^(\s*)(<\/script>)/gm,subLanguage:"typescript",excludeBegin:!0,excludeEnd:!0},{begin:/^(\s*)(<style(\sscoped)?>)/gm,end:/^(\s*)(<\/style>)/gm,subLanguage:"css",excludeBegin:!0,excludeEnd:!0},{begin:/^(\s*)(<style lang=["'](scss|sass)["'](\sscoped)?>)/gm,end:/^(\s*)(<\/style>)/gm,subLanguage:"scss",excludeBegin:!0,excludeEnd:!0},{begin:/^(\s*)(<style lang=["']stylus["'](\sscoped)?>)/gm,end:/^(\s*)(<\/style>)/gm,subLanguage:"stylus",excludeBegin:!0,excludeEnd:!0}]}}e.exports=function(e){e.registerLanguage("vue",s)},e.exports.definer=s}));
