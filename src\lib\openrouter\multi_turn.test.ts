// Test for multi-turn tool calling functionality
// This test verifies that the multi-turn conversation system works correctly

import { OpenRouterClient } from './client'
import { OpenRouterConfig, AiMessage, AiToolCall, ToolExecutionResult } from '../../ui/types/ui_types'

// Mock configuration for testing
const mockConfig: OpenRouterConfig = {
  apiKey: 'test-key',
  model: 'openai/gpt-4o-mini',
  temperature: 0.7,
  maxTokens: 1000
}

// Mock fetch for testing
const mockFetch = jest.fn()
global.fetch = mockFetch

// Mock tool execution
jest.mock('./tools', () => ({
  executeTool: jest.fn().mockResolvedValue({
    success: true,
    data: [
      {
        figma: {
          id: 'test-123',
          type: 'FRAME',
          name: 'Test Frame',
          x: 100,
          y: 200,
          width: 300,
          height: 400
        }
      }
    ]
  }),
  FIGMA_TOOLS: [
    {
      type: 'function',
      function: {
        name: 'get_figma_selection_json',
        description: 'Get Figma selection as JSON',
        parameters: {
          type: 'object',
          properties: {},
          required: []
        }
      }
    }
  ]
}))

describe('Multi-Turn Tool Calling', () => {
  let client: OpenRouterClient

  beforeEach(() => {
    client = new OpenRouterClient(mockConfig)
    mockFetch.mockClear()
  })

  test('should handle single turn with tool call', async () => {
    // Mock API response with tool call
    const mockResponse1 = {
      id: 'test-1',
      model: 'openai/gpt-4o-mini',
      choices: [{
        message: {
          role: 'assistant',
          content: '',
          tool_calls: [{
            id: 'call-1',
            type: 'function',
            function: {
              name: 'get_figma_selection_json',
              arguments: '{}'
            }
          }]
        },
        finish_reason: 'tool_calls'
      }],
      usage: { prompt_tokens: 10, completion_tokens: 5, total_tokens: 15 }
    }

    // Mock API response after tool execution
    const mockResponse2 = {
      id: 'test-2',
      model: 'openai/gpt-4o-mini',
      choices: [{
        message: {
          role: 'assistant',
          content: 'I can see you have a test frame selected. It\'s positioned at (100, 200) with dimensions 300x400 pixels.'
        },
        finish_reason: 'stop'
      }],
      usage: { prompt_tokens: 50, completion_tokens: 20, total_tokens: 70 }
    }

    mockFetch
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse1)
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse2)
      })

    const conversation: AiMessage[] = []
    const toolCalls: Array<{ toolCall: AiToolCall, result: ToolExecutionResult }> = []
    const turnCompletions: Array<{ turn: number, response: any }> = []

    const response = await client.sendMultiTurnMessage(
      'What do you see in my Figma selection?',
      conversation,
      5,
      (toolCall, result) => {
        toolCalls.push({ toolCall, result })
      },
      (turn, response) => {
        turnCompletions.push({ turn, response })
      }
    )

    // Verify the conversation flow
    expect(mockFetch).toHaveBeenCalledTimes(2)
    expect(toolCalls).toHaveLength(1)
    expect(toolCalls[0].toolCall.function.name).toBe('get_figma_selection_json')
    expect(toolCalls[0].result.success).toBe(true)
    expect(turnCompletions).toHaveLength(2)
    expect(response.choices[0].message.content).toContain('test frame')
  })

  test('should handle multiple tool calls in sequence', async () => {
    // Mock multiple API responses
    const responses = [
      {
        id: 'test-1',
        model: 'openai/gpt-4o-mini',
        choices: [{
          message: {
            role: 'assistant',
            content: '',
            tool_calls: [{
              id: 'call-1',
              type: 'function',
              function: {
                name: 'get_figma_selection_json',
                arguments: '{}'
              }
            }]
          },
          finish_reason: 'tool_calls'
        }],
        usage: { prompt_tokens: 10, completion_tokens: 5, total_tokens: 15 }
      },
      {
        id: 'test-2',
        model: 'openai/gpt-4o-mini',
        choices: [{
          message: {
            role: 'assistant',
            content: '',
            tool_calls: [{
              id: 'call-2',
              type: 'function',
              function: {
                name: 'get_figma_selection_css',
                arguments: '{}'
              }
            }]
          },
          finish_reason: 'tool_calls'
        }],
        usage: { prompt_tokens: 30, completion_tokens: 5, total_tokens: 35 }
      },
      {
        id: 'test-3',
        model: 'openai/gpt-4o-mini',
        choices: [{
          message: {
            role: 'assistant',
            content: 'I\'ve analyzed your selection and extracted both the JSON data and CSS styles.'
          },
          finish_reason: 'stop'
        }],
        usage: { prompt_tokens: 80, completion_tokens: 15, total_tokens: 95 }
      }
    ]

    responses.forEach(response => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(response)
      })
    })

    const conversation: AiMessage[] = []
    const toolCalls: Array<{ toolCall: AiToolCall, result: ToolExecutionResult }> = []

    const response = await client.sendMultiTurnMessage(
      'Analyze my Figma selection and get both JSON and CSS data',
      conversation,
      5,
      (toolCall, result) => {
        toolCalls.push({ toolCall, result })
      }
    )

    // Verify multiple tool calls were executed
    expect(mockFetch).toHaveBeenCalledTimes(3)
    expect(toolCalls).toHaveLength(2)
    expect(toolCalls[0].toolCall.function.name).toBe('get_figma_selection_json')
    expect(toolCalls[1].toolCall.function.name).toBe('get_figma_selection_css')
    expect(response.choices[0].message.content).toContain('analyzed')
  })

  test('should respect max turns limit', async () => {
    // Mock responses that would cause infinite loop without limit
    const mockResponse = {
      id: 'test-loop',
      model: 'openai/gpt-4o-mini',
      choices: [{
        message: {
          role: 'assistant',
          content: '',
          tool_calls: [{
            id: 'call-loop',
            type: 'function',
            function: {
              name: 'get_figma_selection_json',
              arguments: '{}'
            }
          }]
        },
        finish_reason: 'tool_calls'
      }],
      usage: { prompt_tokens: 10, completion_tokens: 5, total_tokens: 15 }
    }

    // Mock the same response multiple times
    for (let i = 0; i < 10; i++) {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      })
    }

    const conversation: AiMessage[] = []
    const maxTurns = 3

    const response = await client.sendMultiTurnMessage(
      'Keep calling tools',
      conversation,
      maxTurns
    )

    // Should stop at max turns limit
    expect(mockFetch).toHaveBeenCalledTimes(maxTurns)
  })
})
