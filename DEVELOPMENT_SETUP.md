# 🛠️ FigmaAgent 开发环境配置指南

## ✅ 环境验证结果

### 基础环境
- ✅ **Node.js**: v23.10.0 (满足要求 18+)
- ✅ **npm**: v10.9.2 
- ✅ **Python**: v3.12.9 (满足要求 3.8+)
- ✅ **Git**: v2.48.1

### 项目依赖
- ✅ **依赖安装**: 576 个包已成功安装
- ✅ **AI 模型数据**: 319 个 OpenRouter 模型已更新
- ⚠️ **安全漏洞**: 3 个中等级别漏洞（主要是 prismjs 相关）

### 构建验证
- ✅ **Web 开发服务器**: http://localhost:5173/ (正常运行)
- ✅ **生产构建**: 插件和 UI 构建成功
- ✅ **输出文件**: 
  - `dist/plugin.js` (28.40 kB)
  - `dist/index.html` (1,784.32 kB)
  - `dist/manifest.json` (0.47 kB)

## 🚀 快速开始

### 1. 启动开发服务器
```bash
npm run dev
```
访问: http://localhost:5173/

### 2. 构建 Figma 插件
```bash
npm run build:prod
```

### 3. 在 Figma 中安装插件
1. 打开 Figma 桌面应用
2. 进入 **Plugins → Development → Import plugin from manifest**
3. 选择项目根目录
4. 从 Plugins 菜单运行插件

## 🔧 开发工具配置

### VSCode 配置
已配置以下 VSCode 设置：
- ✅ TypeScript 智能提示和自动导入
- ✅ ESLint 代码检查和自动修复
- ✅ Tailwind CSS 智能提示
- ✅ 保存时自动格式化
- ✅ React 开发支持

### 推荐扩展
已在 `.vscode/extensions.json` 中配置推荐扩展：
- TypeScript 支持
- ESLint 代码检查
- Tailwind CSS 智能提示
- React 开发工具
- Figma 插件开发支持

### 调试配置
已配置 `.vscode/launch.json` 包含：
- Web 开发服务器启动
- 生产/分阶段构建
- TypeScript 类型检查
- ESLint 检查
- OpenRouter 模型更新

## 📝 可用脚本

### 开发脚本
```bash
npm run dev              # 启动 Web 开发服务器
npm run watch:prod       # 监听模式生产构建
npm run watch:staging    # 监听模式分阶段构建
```

### 构建脚本
```bash
npm run build:prod       # 生产构建
npm run build:staging    # 分阶段构建（带调试信息）
npm run build            # 默认生产构建
```

### 代码质量
```bash
npm run type-check       # TypeScript 类型检查
npm run lint             # ESLint 检查
npm run lint:fix         # ESLint 自动修复
```

### 工具脚本
```bash
npm run update-models    # 更新 OpenRouter 模型数据
npm run extract-types    # 提取 Figma API 类型
```

## 🔍 故障排除

### TypeScript 错误
当前存在一些 TypeScript 错误，主要涉及：
- 测试文件缺少 Jest 类型定义
- 一些类型导入问题
- 样式组件的 JSX 属性问题

这些错误不影响基本功能，可以在开发过程中逐步修复。

### 安全漏洞
存在 3 个中等级别的安全漏洞，主要是：
- `prismjs` 版本过低
- `brace-expansion` 正则表达式拒绝服务漏洞

可以通过以下命令修复：
```bash
npm audit fix --force  # 注意：可能导致破坏性更改
```

### 依赖冲突
存在一些 peer dependency 警告，主要是：
- `jotai-devtools` 与 React 19 的兼容性问题

这些警告不影响核心功能。

## 🎯 开发模式

### Web 开发模式
- **URL**: http://localhost:5173/
- **特点**: 热重载、快速开发、模拟 Figma 环境
- **用途**: UI 开发、功能测试、样式调试

### Figma 插件模式
- **安装**: 通过 Figma 开发者菜单导入
- **特点**: 真实 Figma 环境、完整 API 访问
- **用途**: 插件功能测试、Figma API 集成

## 🔑 AI 服务配置

### OpenRouter
1. 获取 API 密钥: https://openrouter.ai/
2. 在设置中配置 API 密钥
3. 选择合适的模型（已加载 319 个可用模型）

### DeepSeek
1. 获取 API 密钥: https://api.deepseek.com/
2. 在设置中配置 API 密钥
3. 选择 DeepSeek 模型

## 📚 下一步

1. **修复 TypeScript 错误**: 逐步解决类型定义问题
2. **配置 AI 服务**: 设置 OpenRouter 或 DeepSeek API 密钥
3. **开始开发**: 根据需求修改和扩展功能
4. **测试插件**: 在 Figma 中测试插件功能

## 🆘 获取帮助

- **文档**: 查看 `/docs` 目录
- **示例**: 查看 `/examples` 目录
- **问题**: 通过 GitHub Issues 报告问题

---

✨ 开发环境配置完成！现在可以开始开发 FigmaAgent 了。
