# FigmaAgent 开发环境快速启动脚本
# PowerShell 脚本用于快速启动开发环境

param(
    [string]$Mode = "web",  # web | plugin | both
    [switch]$Install,       # 是否重新安装依赖
    [switch]$Build,         # 是否先构建
    [switch]$Open           # 是否自动打开浏览器
)

Write-Host "🎨 FigmaAgent 开发环境启动器" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

# 检查 Node.js
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js 未安装或不在 PATH 中" -ForegroundColor Red
    exit 1
}

# 检查 npm
try {
    $npmVersion = npm --version
    Write-Host "✅ npm: v$npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ npm 未安装或不在 PATH 中" -ForegroundColor Red
    exit 1
}

# 重新安装依赖（如果需要）
if ($Install) {
    Write-Host "📦 重新安装依赖..." -ForegroundColor Yellow
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ 依赖安装失败" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ 依赖安装完成" -ForegroundColor Green
}

# 构建（如果需要）
if ($Build) {
    Write-Host "🏗️ 构建项目..." -ForegroundColor Yellow
    npm run build:prod
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ 构建失败" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ 构建完成" -ForegroundColor Green
}

# 根据模式启动
switch ($Mode.ToLower()) {
    "web" {
        Write-Host "🌐 启动 Web 开发服务器..." -ForegroundColor Yellow
        Write-Host "访问地址: http://localhost:5173/" -ForegroundColor Cyan
        
        if ($Open) {
            Start-Sleep -Seconds 2
            Start-Process "http://localhost:5173/"
        }
        
        npm run dev
    }
    
    "plugin" {
        Write-Host "🔌 构建 Figma 插件..." -ForegroundColor Yellow
        npm run build:prod
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 插件构建完成" -ForegroundColor Green
            Write-Host "📁 输出目录: dist/" -ForegroundColor Cyan
            Write-Host "📋 在 Figma 中导入插件:" -ForegroundColor Cyan
            Write-Host "   1. 打开 Figma 桌面应用" -ForegroundColor Gray
            Write-Host "   2. Plugins → Development → Import plugin from manifest" -ForegroundColor Gray
            Write-Host "   3. 选择项目根目录" -ForegroundColor Gray
        } else {
            Write-Host "❌ 插件构建失败" -ForegroundColor Red
            exit 1
        }
    }
    
    "both" {
        Write-Host "🚀 构建插件并启动 Web 服务器..." -ForegroundColor Yellow
        
        # 先构建插件
        npm run build:prod
        if ($LASTEXITCODE -ne 0) {
            Write-Host "❌ 插件构建失败" -ForegroundColor Red
            exit 1
        }
        
        Write-Host "✅ 插件构建完成" -ForegroundColor Green
        Write-Host "🌐 启动 Web 开发服务器..." -ForegroundColor Yellow
        Write-Host "访问地址: http://localhost:5173/" -ForegroundColor Cyan
        
        if ($Open) {
            Start-Sleep -Seconds 2
            Start-Process "http://localhost:5173/"
        }
        
        npm run dev
    }
    
    default {
        Write-Host "❌ 无效的模式: $Mode" -ForegroundColor Red
        Write-Host "可用模式: web, plugin, both" -ForegroundColor Yellow
        exit 1
    }
}

Write-Host "🎉 启动完成！" -ForegroundColor Green
