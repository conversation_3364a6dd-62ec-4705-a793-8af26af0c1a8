import { useState } from 'react'
import { useKey } from 'react-use'
import { useConversationManager, useConversationStorage } from '../store'
import { ConversationSummary } from '../types/ui_types'

// Utility function to parse conversation title into timestamp and description
function parseConversationTitle(title: string): { timestamp: string; description: string } {
  // Check if title matches the format "YY-MM-DD HH:MM:SS - Description"
  const timestampMatch = title.match(/^(\d{2}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) - (.+)$/)

  if (timestampMatch) {
    return {
      timestamp: timestampMatch[1],
      description: timestampMatch[2]
    }
  }

  // Fallback for titles that don't match the expected format
  return {
    timestamp: '',
    description: title
  }
}

interface ConversationManagerProps {
  isOpen: boolean
  onClose: () => void
}

export function ConversationManager({ isOpen, onClose }: ConversationManagerProps) {
  const {
    conversations,
    currentConversationId,
    createNewConversation,
    switchToConversation,
    deleteConversation,
    clearCurrentConversation
  } = useConversationManager()

  const [newConversationTitle, setNewConversationTitle] = useState('')
  const [showNewConversationInput, setShowNewConversationInput] = useState(false)

  // Initialize conversation storage
  useConversationStorage()

  // Handle ESC key to close conversation manager modal
  useKey('Escape', () => {
    if (isOpen) {
      onClose()
    }
  }, {}, [isOpen])

  const handleCreateNew = () => {
    if (showNewConversationInput && newConversationTitle.trim()) {
      createNewConversation(newConversationTitle.trim())
      setNewConversationTitle('')
      setShowNewConversationInput(false)
      onClose()
    } else {
      setShowNewConversationInput(true)
    }
  }

  const handleSwitchConversation = (conversationId: string) => {
    console.log('🔄 [ConversationManager] User clicked conversation:', conversationId)
    switchToConversation(conversationId)
    onClose()
  }

  const handleDeleteConversation = (conversationId: string) => {
    deleteConversation(conversationId)
  }

  const handleClearCurrent = () => {
    clearCurrentConversation()
    onClose()
  }

  const formatDate = (date: Date | string) => {
    const d = new Date(date)
    const now = new Date()
    const diffMs = now.getTime() - d.getTime()
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffDays === 0) {
      return d.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    } else if (diffDays === 1) {
      return 'Yesterday'
    } else if (diffDays < 7) {
      return d.toLocaleDateString([], { weekday: 'short' })
    } else {
      return d.toLocaleDateString([], { month: 'short', day: 'numeric' })
    }
  }

  // Don't render modal if not open
  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 max-h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Conversations</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Actions */}
        <div className="p-4 border-b border-gray-200 space-y-2">
          {/* New Conversation */}
          {showNewConversationInput ? (
            <div className="flex gap-2">
              <input
                type="text"
                placeholder="Conversation title..."
                value={newConversationTitle}
                onChange={(e) => setNewConversationTitle(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') handleCreateNew()
                  if (e.key === 'Escape') {
                    setShowNewConversationInput(false)
                    setNewConversationTitle('')
                  }
                }}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                autoFocus
              />
              <button
                onClick={handleCreateNew}
                className="px-3 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 transition-colors"
              >
                Create
              </button>
              <button
                onClick={() => {
                  setShowNewConversationInput(false)
                  setNewConversationTitle('')
                }}
                className="px-3 py-2 border border-gray-300 text-gray-700 rounded-md text-sm hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
            </div>
          ) : (
            <div className="flex gap-2">
              <button
                onClick={handleCreateNew}
                className="flex-1 px-3 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                New Conversation
              </button>
              <button
                onClick={handleClearCurrent}
                className="px-3 py-2 border border-gray-300 text-gray-700 rounded-md text-sm hover:bg-gray-50 transition-colors"
                title="Clear current conversation"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>
            </div>
          )}
        </div>

        {/* Conversation List */}
        <div className="flex-1 overflow-y-auto">
          {conversations.length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              <p className="text-sm">No conversations yet</p>
              <p className="text-xs mt-1">Create your first conversation to get started</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {conversations
                .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
                .map((conversation) => (
                  <div
                    key={conversation.id}
                    className={`p-4 hover:bg-gray-50 cursor-pointer transition-colors ${
                      conversation.id === currentConversationId ? 'bg-blue-50 border-r-2 border-blue-500' : ''
                    }`}
                    onClick={() => handleSwitchConversation(conversation.id)}
                  >
                    <div className="flex items-start justify-between gap-3">
                      <div className="flex-1 min-w-0 max-w-[calc(100%-60px)]">
                        {(() => {
                          const { timestamp, description } = parseConversationTitle(conversation.title)
                          return (
                            <div className="space-y-1" title={conversation.title}>
                              {timestamp && (
                                <div className="text-xs text-gray-400 truncate max-w-full overflow-hidden text-ellipsis whitespace-nowrap">
                                  {timestamp}
                                </div>
                              )}
                              <h3 className="text-sm font-medium text-gray-900 truncate max-w-full overflow-hidden text-ellipsis whitespace-nowrap">
                                {description}
                              </h3>
                            </div>
                          )
                        })()}
                        <p className="text-xs text-gray-500 mt-1 line-clamp-2">
                          {conversation.previewText}
                        </p>
                        <div className="flex items-center gap-2 mt-2 text-xs text-gray-400">
                          <span>{formatDate(conversation.updatedAt)}</span>
                          <span>•</span>
                          <span>{conversation.messageCount} messages</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-1 flex-shrink-0">
                        {conversation.id === currentConversationId && (
                          <div className="w-2 h-2 bg-blue-500 rounded-full" title="Current conversation" />
                        )}
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleDeleteConversation(conversation.id)
                          }}
                          className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                          title="Delete conversation"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          )}
        </div>


      </div>
    </div>
  )
}
