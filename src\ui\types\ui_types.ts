// UI type definitions for the chat interface

export interface Message {
  id: string
  text: string
  timestamp: Date
  type: 'user' | 'system' | 'ai'
  model?: string // For AI messages, track which model was used
  vendor?: string // For AI messages, track which vendor was used
  isStreaming?: boolean // For AI messages, track if still streaming
  loadingState?: AiLoadingState // For AI messages, track loading progress
  isInterrupted?: boolean // For AI messages, track if response was interrupted/aborted
}

// Inline loading state for AI messages
export interface AiLoadingState {
  isActive: boolean
  phase: 'initializing' | 'thinking' | 'tool_execution' | 'analyzing' | 'completing' | 'timeout'
  statusMessage: string
  currentTool?: string
  toolsExecuted: number
  startTime: Date
  elapsedTime?: number
  position?: 'start' | 'inline' | 'end' // Dynamic positioning for progress indicator
  hasInlineMarker?: boolean // Whether the progress indicator is embedded inline in the text
  timeoutMs?: number // Timeout duration in milliseconds (default: 60000)
  isTimedOut?: boolean // Whether the operation has timed out
}

export interface Command {
  name: string
  description: string
  usage: string
}

export interface SuggestionBoxProps {
  suggestions: Command[]
  selectedIndex: number
  onSuggestionClick: (suggestion: Command) => void
  onMouseEnter: (index: number) => void
}



export interface ChatInterfaceProps {
  messages: Message[]
  copiedMessageId: string | null
  onCopyMessage: (text: string, messageId: string) => void
  messagesEndRef?: React.RefObject<HTMLDivElement | null>
  // Multi-select functionality
  selectedMessageIds?: Set<string>
  isSelectionMode?: boolean
  onToggleSelection?: (messageId: string) => void
  onSelectAll?: () => void
  onClearSelection?: () => void
  onDeleteSelected?: () => void
  onCopySelected?: () => void
  // Auto-scroll control
  forceScrollToBottom?: boolean
  // Resend functionality
  onResendMessage?: (messageId: string) => void
}

// Command token for tracking commands in message input
export interface CommandToken {
  id: string
  command: string
  start: number
  end: number
}

// Message content with both text and command tokens
export interface MessageContent {
  text: string
  commandTokens: CommandToken[]
}

export interface MessageInputProps {
  messageContent: MessageContent
  showSuggestions: boolean
  suggestions: Command[]
  selectedSuggestionIndex: number
  onMessageContentChange: (content: MessageContent) => void
  onSendMessage: () => void
  onSuggestionClick: (suggestion: Command) => void
  onSuggestionMouseEnter: (index: number) => void
  onHideSuggestions: () => void
  editableRef: React.RefObject<HTMLDivElement>
}

export interface MinimizedViewProps {
  connected: boolean
  onConnect: () => void
  onDisconnect: () => void
  onExpand: () => void
}

// Import command prefix from configuration
export { COMMAND_PREFIX } from '../config/commands'

// AI Vendor Types
export type AIVendor = 'openrouter' | 'deepseek'

// Terminology settings for customizable UI labels
export interface TerminologySettings {
  agentName: string // Customizable name for "Agent" (formerly "AI Assistant")
  executorName: string // Customizable name for "Executor" (formerly "Bot")
}

// Default terminology settings
export const DEFAULT_TERMINOLOGY: TerminologySettings = {
  agentName: 'Agent',
  executorName: 'Executor'
}

// Code block display settings
export interface CodeBlockSettings {
  maxHeight: number // Maximum height in pixels for code blocks
  virtualizationThreshold: number // Minimum lines to trigger virtualization
  showLineNumbers: boolean // Whether to show line numbers by default
}

// Default code block settings
export const DEFAULT_CODE_BLOCK_SETTINGS: CodeBlockSettings = {
  maxHeight: 300, // Default 300px max height
  virtualizationThreshold: 50, // Virtualize for 50+ lines
  showLineNumbers: true // Show line numbers by default
}

// OpenRouter API Configuration
export interface OpenRouterConfig {
  apiKey: string
  model: string
  temperature: number
  maxTokens: number
  systemPrompt: string
}

// DeepSeek API Configuration
export interface DeepSeekConfig {
  apiKey: string
  model: string
  temperature: number
  maxTokens: number
  systemPrompt: string
}

// Generic AI model interface (vendor-agnostic)
export interface AiModel {
  id: string
  name: string
  description: string
  pricing: {
    prompt: number
    completion: number
  }
  contextLength: number
}

// Legacy alias for backward compatibility
export type OpenRouterModel = AiModel

// Generic AI API Request/Response Types (vendor-agnostic)
export interface AiMessage {
  role: 'system' | 'user' | 'assistant' | 'tool'
  content: string
  tool_calls?: AiToolCall[]
  tool_call_id?: string
}

export interface AiRequest {
  model: string
  messages: AiMessage[]
  temperature?: number
  max_tokens?: number
  stream?: boolean
  user?: string
  tools?: AiTool[]
  tool_choice?: 'auto' | 'none' | { type: 'function'; function: { name: string } }
}

export interface AiResponse {
  id: string
  choices: Array<{
    message: {
      role: string
      content: string
      tool_calls?: AiToolCall[]
    }
    finish_reason: string
  }>
  usage: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
  model: string
}

export interface AiStreamChunk {
  id: string
  choices: Array<{
    delta: {
      content?: string
      role?: string
      tool_calls?: AiToolCall[]
    }
    finish_reason?: string
  }>
}

// Legacy aliases for backward compatibility
export type OpenRouterMessage = AiMessage
export type OpenRouterRequest = AiRequest
export type OpenRouterResponse = AiResponse
export type OpenRouterStreamChunk = AiStreamChunk

// AI Tool Calling Types (vendor-agnostic)
export interface AiTool {
  type: 'function'
  function: {
    name: string
    description: string
    parameters?: {
      type: 'object'
      properties?: Record<string, unknown>
      required?: string[]
    }
  }
}

// Generic AI tool call interface (vendor-agnostic)
export interface AiToolCall {
  id: string
  type: 'function'
  function: {
    name: string
    arguments: string
  }
}

// Legacy aliases for backward compatibility
export type OpenRouterTool = AiTool
export type OpenRouterToolCall = AiToolCall

// Tool execution result
export interface ToolExecutionResult {
  success: boolean
  data?: unknown
  error?: string
}

// Multi-turn conversation state
export interface MultiTurnState {
  isActive: boolean
  toolCallsExecuted: number
  startTime: Date
}

// Multi-turn conversation callbacks
export interface MultiTurnCallbacks {
  onToolCall?: (toolCall: AiToolCall, result: ToolExecutionResult) => void
  onTurnComplete?: (turn: number, response: AiResponse) => void
  onProgress?: (state: MultiTurnState) => void
}

// Available commands constant
export const AVAILABLE_COMMANDS: Command[] = [
  {
    name: '/sel-figma',
    description: 'Get Figma selection info',
    usage: '/sel-figma'
  },
  {
    name: '/sel-css',
    description: 'Get CSS styles with highlighting',
    usage: '/sel-css'
  },
  {
    name: '/sel-twcss',
    description: 'Get Tailwind CSS classes',
    usage: '/sel-twcss'
  },
  {
    name: '/sel-twhtml',
    description: 'Get Tailwind CSS formatted HTML structure',
    usage: '/sel-twhtml'
  },
  {
    name: '/ai',
    description: 'Chat with Agent',
    usage: '/ai [your message]'
  },
  {
    name: '/p',
    description: 'Repeat previous command',
    usage: '/p'
  },
  {
    name: '/help',
    description: 'Show commands and shortcuts',
    usage: '/help'
  }
]

// Conversation Management Types
export interface Conversation {
  id: string
  title: string
  messages: Message[]
  aiMessages: AiMessage[]
  createdAt: Date
  updatedAt: Date
  previewText: string
}

export interface ConversationSummary {
  id: string
  title: string
  previewText: string
  createdAt: Date
  updatedAt: Date
  messageCount: number
}

export const DEFAULT_SYSTEM_PROMPT = `You are an AI assistant integrated in Figma, offering:
1. Design advice (layouts, color systems, etc.)
2. Code generation (CSS/React components)
3. General inquiries

Interaction principles:
• Keep responses focused
• When design context is needed, actively check the current file
• Clearly state limitations for non-design requests`

export const DEFAULT_MAX_TOKENS = 8192
export const DEFAULT_TEMPERATURE = 0.7

// Default OpenRouter configuration
export const DEFAULT_OPENROUTER_CONFIG: OpenRouterConfig = {
  apiKey: '',
  model: 'deepseek/deepseek-chat-v3-0324:free',
  temperature: DEFAULT_TEMPERATURE,
  maxTokens: DEFAULT_MAX_TOKENS,
  systemPrompt: DEFAULT_SYSTEM_PROMPT
}

// Default DeepSeek configuration
export const DEFAULT_DEEPSEEK_CONFIG: DeepSeekConfig = {
  apiKey: '',
  model: 'deepseek-chat',
  temperature: DEFAULT_TEMPERATURE,
  maxTokens: DEFAULT_MAX_TOKENS,
  systemPrompt: DEFAULT_SYSTEM_PROMPT
}

// DeepSeek models
export const DEEPSEEK_MODELS = [
  'deepseek-chat',
  'deepseek-coder',
  'deepseek-reasoner'
]

// Available languages for syntax highlighting
export const AVAILABLE_LANGUAGES = [
  { value: 'json', label: 'JSON' },
  { value: 'javascript', label: 'JavaScript' },
  { value: 'typescript', label: 'TypeScript' },
  { value: 'vue', label: 'Vue.js' },
  { value: 'python', label: 'Python' },
  { value: 'java', label: 'Java' },
  { value: 'cpp', label: 'C++' },
  { value: 'rust', label: 'Rust' },
  { value: 'go', label: 'Go' },
  { value: 'xml', label: 'XML' },
  { value: 'yaml', label: 'YAML' },
  { value: 'sql', label: 'SQL' },
  { value: 'bash', label: 'Bash' },
  { value: 'text', label: 'Plain Text' }
]
