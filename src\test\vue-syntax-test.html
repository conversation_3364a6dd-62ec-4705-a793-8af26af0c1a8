<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue Syntax Highlighting Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 8px;
        }
        h2 {
            color: #333;
            border-bottom: 2px solid #007acc;
            padding-bottom: 10px;
        }
    </style>
</head>
<body>
    <h1>Vue.js Syntax Highlighting Test</h1>
    
    <div class="test-section">
        <h2>Test Case 1: Vue Single File Component</h2>
        <p>This should be highlighted with Vue syntax highlighting:</p>
        <pre><code class="language-vue">&lt;template&gt;
  &lt;div class="hello-world"&gt;
    &lt;h1 v-if="showTitle"&gt;{{ title }}&lt;/h1&gt;
    &lt;button @click="toggleTitle"&gt;Toggle Title&lt;/button&gt;
    &lt;ul&gt;
      &lt;li v-for="item in items" :key="item.id"&gt;
        {{ item.name }}
      &lt;/li&gt;
    &lt;/ul&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script&gt;
export default {
  name: 'HelloWorld',
  data() {
    return {
      title: 'Hello Vue!',
      showTitle: true,
      items: [
        { id: 1, name: 'Item 1' },
        { id: 2, name: 'Item 2' },
        { id: 3, name: 'Item 3' }
      ]
    }
  },
  methods: {
    toggleTitle() {
      this.showTitle = !this.showTitle
    }
  }
}
&lt;/script&gt;

&lt;style scoped&gt;
.hello-world {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

h1 {
  color: #42b883;
  font-size: 2rem;
}

button {
  background-color: #42b883;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background-color: #369870;
}
&lt;/style&gt;</code></pre>
    </div>

    <div class="test-section">
        <h2>Test Case 2: Vue Composition API</h2>
        <p>This should also be highlighted with Vue syntax highlighting:</p>
        <pre><code class="language-vue">&lt;template&gt;
  &lt;div&gt;
    &lt;input v-model="searchTerm" placeholder="Search..." /&gt;
    &lt;div v-for="user in filteredUsers" :key="user.id"&gt;
      &lt;h3&gt;{{ user.name }}&lt;/h3&gt;
      &lt;p&gt;{{ user.email }}&lt;/p&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, computed } from 'vue'

const searchTerm = ref('')
const users = ref([
  { id: 1, name: 'John Doe', email: '<EMAIL>' },
  { id: 2, name: 'Jane Smith', email: '<EMAIL>' }
])

const filteredUsers = computed(() =&gt; {
  return users.value.filter(user =&gt; 
    user.name.toLowerCase().includes(searchTerm.value.toLowerCase())
  )
})
&lt;/script&gt;</code></pre>
    </div>

    <div class="test-section">
        <h2>Test Case 3: Regular JavaScript (for comparison)</h2>
        <p>This should be highlighted with Prism.js JavaScript syntax highlighting:</p>
        <pre><code class="language-javascript">// Regular JavaScript code
function createVueApp() {
  const { createApp, ref, computed } = Vue
  
  return createApp({
    setup() {
      const count = ref(0)
      const doubleCount = computed(() => count.value * 2)
      
      function increment() {
        count.value++
      }
      
      return {
        count,
        doubleCount,
        increment
      }
    }
  })
}

const app = createVueApp()
app.mount('#app')</code></pre>
    </div>

    <script>
        // This would normally be handled by the React markdown renderer
        // For this test, we're just showing the raw code blocks
        console.log('Vue syntax highlighting test page loaded')
    </script>
</body>
</html>
