import React from 'react'
import { render, screen, act } from '@testing-library/react'
import InlineLoadingIndicator from './InlineLoadingIndicator'
import { AiLoadingState } from '../types/ui_types'

describe('InlineLoadingIndicator', () => {
  const mockLoadingState: AiLoadingState = {
    isActive: true,
    phase: 'thinking',
    statusMessage: 'AI is analyzing your request',
    toolsExecuted: 0,
    startTime: new Date()
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  test('should not render when loading state is inactive', () => {
    const inactiveState: AiLoadingState = {
      ...mockLoadingState,
      isActive: false
    }

    const { container } = render(
      <InlineLoadingIndicator loadingState={inactiveState} />
    )

    expect(container.firstChild).toBeNull()
  })

  test('should render loading indicator when active', () => {
    const { container } = render(
      <InlineLoadingIndicator loadingState={mockLoadingState} />
    )

    expect(container.querySelector('.inline-loading-indicator')).toBeInTheDocument()
  })

  test('should display correct phase icon for each phase', () => {
    const phases = [
      { phase: 'initializing', expectedIcon: '🚀' },
      { phase: 'thinking', expectedIcon: '🤔' },
      { phase: 'tool_execution', expectedIcon: '🔧' },
      { phase: 'analyzing', expectedIcon: '📊' },
      { phase: 'completing', expectedIcon: '✨' }
    ]

    phases.forEach(({ phase, expectedIcon }) => {
      const stateWithPhase: AiLoadingState = {
        ...mockLoadingState,
        phase: phase as any
      }

      const { container, unmount } = render(
        <InlineLoadingIndicator loadingState={stateWithPhase} />
      )

      const phaseIcon = container.querySelector('.phase-icon')
      expect(phaseIcon).toHaveTextContent(expectedIcon)

      unmount()
    })
  })

  test('should display status message', () => {
    const { container } = render(
      <InlineLoadingIndicator loadingState={mockLoadingState} />
    )

    const statusText = container.querySelector('.status-text')
    expect(statusText).toHaveTextContent('AI is analyzing your request')
  })

  test('should display tool badge when current tool is specified', () => {
    const stateWithTool: AiLoadingState = {
      ...mockLoadingState,
      phase: 'tool_execution',
      currentTool: 'get_figma_selection_json',
      statusMessage: 'Executing get_figma_selection_json...'
    }

    const { container } = render(
      <InlineLoadingIndicator loadingState={stateWithTool} />
    )

    const toolBadge = container.querySelector('.tool-badge')
    expect(toolBadge).toHaveTextContent('get_figma_selection_json')
  })

  test('should display tools executed count', () => {
    const stateWithTools: AiLoadingState = {
      ...mockLoadingState,
      toolsExecuted: 3
    }

    const { container } = render(
      <InlineLoadingIndicator loadingState={stateWithTools} />
    )

    const toolsCount = container.querySelector('.tools-count')
    expect(toolsCount).toHaveTextContent('3 tools executed')
  })

  test('should display singular form for one tool executed', () => {
    const stateWithOneTool: AiLoadingState = {
      ...mockLoadingState,
      toolsExecuted: 1
    }

    const { container } = render(
      <InlineLoadingIndicator loadingState={stateWithOneTool} />
    )

    const toolsCount = container.querySelector('.tools-count')
    expect(toolsCount).toHaveTextContent('1 tool executed')
  })

  test('should not display tools count when no tools executed', () => {
    const { container } = render(
      <InlineLoadingIndicator loadingState={mockLoadingState} />
    )

    const toolsCount = container.querySelector('.tools-count')
    expect(toolsCount).not.toBeInTheDocument()
  })

  test('should display elapsed time', async () => {
    // Create a start time 5 seconds ago
    const startTime = new Date(Date.now() - 5000)
    const stateWithTime: AiLoadingState = {
      ...mockLoadingState,
      startTime
    }

    const { container } = render(
      <InlineLoadingIndicator loadingState={stateWithTime} />
    )

    // Wait for the elapsed time to update
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 1100))
    })

    const elapsedTime = container.querySelector('.elapsed-time')
    expect(elapsedTime).toBeInTheDocument()
    // Should show at least 5 seconds
    expect(elapsedTime?.textContent).toMatch(/[5-9]s/)
  })

  test('should format elapsed time correctly', () => {
    const formatElapsedTime = (seconds: number) => {
      if (seconds < 60) return `${seconds}s`
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = seconds % 60
      return `${minutes}m ${remainingSeconds}s`
    }

    expect(formatElapsedTime(30)).toBe('30s')
    expect(formatElapsedTime(60)).toBe('1m 0s')
    expect(formatElapsedTime(90)).toBe('1m 30s')
    expect(formatElapsedTime(125)).toBe('2m 5s')
  })

  test('should animate dots in status message', async () => {
    const { container } = render(
      <InlineLoadingIndicator loadingState={mockLoadingState} />
    )

    const statusText = container.querySelector('.status-text')
    const initialText = statusText?.textContent

    // Wait for animation cycle
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 600))
    })

    const updatedText = statusText?.textContent
    // Text should have changed due to animated dots
    expect(updatedText).not.toBe(initialText)
  })

  test('should display progress bar', () => {
    const { container } = render(
      <InlineLoadingIndicator loadingState={mockLoadingState} />
    )

    const progressBar = container.querySelector('.progress-bar')
    const progressFill = container.querySelector('.progress-fill')
    
    expect(progressBar).toBeInTheDocument()
    expect(progressFill).toBeInTheDocument()
  })

  test('should apply custom className', () => {
    const { container } = render(
      <InlineLoadingIndicator 
        loadingState={mockLoadingState} 
        className="custom-class" 
      />
    )

    const indicator = container.querySelector('.inline-loading-indicator')
    expect(indicator).toHaveClass('custom-class')
  })

  test('should handle missing start time gracefully', () => {
    const stateWithoutStartTime: AiLoadingState = {
      ...mockLoadingState,
      startTime: undefined as any
    }

    const { container } = render(
      <InlineLoadingIndicator loadingState={stateWithoutStartTime} />
    )

    // Should render without crashing
    expect(container.querySelector('.inline-loading-indicator')).toBeInTheDocument()
    
    // Should not show elapsed time
    const elapsedTime = container.querySelector('.elapsed-time')
    expect(elapsedTime).not.toBeInTheDocument()
  })

  test('should clean up timers when unmounted', () => {
    const { unmount } = render(
      <InlineLoadingIndicator loadingState={mockLoadingState} />
    )

    // Should unmount without errors
    expect(() => unmount()).not.toThrow()
  })

  test('should update when loading state changes', () => {
    const { container, rerender } = render(
      <InlineLoadingIndicator loadingState={mockLoadingState} />
    )

    // Initial state
    expect(container.querySelector('.phase-icon')).toHaveTextContent('🤔')

    // Update to tool execution phase
    const updatedState: AiLoadingState = {
      ...mockLoadingState,
      phase: 'tool_execution',
      statusMessage: 'Executing tool...',
      currentTool: 'test_tool'
    }

    rerender(<InlineLoadingIndicator loadingState={updatedState} />)

    // Should update to new phase
    expect(container.querySelector('.phase-icon')).toHaveTextContent('🔧')
    expect(container.querySelector('.tool-badge')).toHaveTextContent('test_tool')
  })
})
