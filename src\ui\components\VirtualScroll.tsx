import { useState, useEffect, useRef, useCallback, useMemo } from 'react'

// Note: Basic VirtualScroll component was removed as it's not being used
// Only MasonryVirtualScroll is currently used in the project

// Masonry virtual scroll for variable height items in grid
interface MasonryVirtualScrollProps<T> {
  items: T[]
  columns: number
  estimatedItemHeight: number
  containerHeight: number
  renderItem: (item: T, index: number) => React.ReactNode
  gap?: number
  onScrollEnd?: () => void
}

export function MasonryVirtualScroll<T>({
  items,
  columns,
  estimatedItemHeight,
  containerHeight,
  renderItem,
  gap = 16,
  onScrollEnd
}: MasonryVirtualScrollProps<T>) {
  const [scrollTop, setScrollTop] = useState(0)
  const [itemHeights, setItemHeights] = useState<Map<number, number>>(new Map())
  const scrollElementRef = useRef<HTMLDivElement>(null)

  // Calculate column heights and item positions
  const layout = useMemo(() => {
    const columnHeights = new Array(columns).fill(0)
    const itemPositions: Array<{ x: number; y: number; column: number }> = []

    items.forEach((_, index) => {
      // Find shortest column
      const shortestColumn = columnHeights.indexOf(Math.min(...columnHeights))
      const height = itemHeights.get(index) || estimatedItemHeight

      itemPositions[index] = {
        x: shortestColumn,
        y: columnHeights[shortestColumn],
        column: shortestColumn
      }

      columnHeights[shortestColumn] += height + gap
    })

    const totalHeight = Math.max(...columnHeights)

    return { itemPositions, totalHeight, columnHeights }
  }, [items, columns, itemHeights, estimatedItemHeight, gap])

  // Find visible items
  const visibleItems = useMemo(() => {
    const visible: Array<{ item: T; index: number; position: { x: number; y: number; column: number } }> = []

    layout.itemPositions.forEach((position, index) => {
      const itemHeight = itemHeights.get(index) || estimatedItemHeight
      const itemTop = position.y
      const itemBottom = itemTop + itemHeight

      // Check if item is in visible area (with some overscan)
      if (itemBottom >= scrollTop - 200 && itemTop <= scrollTop + containerHeight + 200) {
        visible.push({
          item: items[index],
          index,
          position
        })
      }
    })

    return visible
  }, [layout.itemPositions, items, itemHeights, estimatedItemHeight, scrollTop, containerHeight])

  // Handle scroll
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const scrollTop = e.currentTarget.scrollTop
    setScrollTop(scrollTop)

    // Check for infinite scroll
    if (onScrollEnd) {
      const { scrollHeight, clientHeight } = e.currentTarget
      const distanceFromBottom = scrollHeight - clientHeight - scrollTop

      if (distanceFromBottom <= 100) {
        onScrollEnd()
      }
    }
  }, [onScrollEnd])

  // Measure item height
  const measureItem = useCallback((index: number, height: number) => {
    setItemHeights(prev => {
      const newMap = new Map(prev)
      if (newMap.get(index) !== height) {
        newMap.set(index, height)
        return newMap
      }
      return prev
    })
  }, [])

  const columnWidth = `calc((100% - ${(columns - 1) * gap}px) / ${columns})`

  return (
    <div
      ref={scrollElementRef}
      className="overflow-auto"
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div className="relative" style={{ height: layout.totalHeight }}>
        {visibleItems.map(({ item, index, position }) => (
          <div
            key={index}
            className="absolute"
            style={{
              left: `calc(${position.column} * (${columnWidth} + ${gap}px))`,
              top: position.y,
              width: columnWidth
            }}
          >
            <ItemMeasurer
              index={index}
              onMeasure={measureItem}
            >
              {renderItem(item, index)}
            </ItemMeasurer>
          </div>
        ))}
      </div>
    </div>
  )
}

// Helper component to measure item heights
interface ItemMeasurerProps {
  index: number
  onMeasure: (index: number, height: number) => void
  children: React.ReactNode
}

function ItemMeasurer({ index, onMeasure, children }: ItemMeasurerProps) {
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (ref.current) {
      const height = ref.current.offsetHeight
      onMeasure(index, height)
    }
  }, [index, onMeasure])

  return (
    <div ref={ref}>
      {children}
    </div>
  )
}
