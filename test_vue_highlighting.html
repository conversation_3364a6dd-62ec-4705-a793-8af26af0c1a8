<!DOCTYPE html>
<html>
<head>
    <title>Vue Syntax Highlighting Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
        }
        code {
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>🧪 Vue Syntax Highlighting Test</h1>

    <div class="test-container">
        <h2>Test Instructions</h2>
        <div class="info">
            <strong>How to test:</strong>
            <ol>
                <li>Open the Figma plugin</li>
                <li>Send a message containing Vue code wrapped in ```vue code blocks</li>
                <li>Check if the Vue code displays with proper syntax highlighting</li>
                <li>Verify no console errors appear</li>
            </ol>
        </div>
    </div>

    <div class="test-container">
        <h2>Sample Vue Code to Test</h2>
        <p>Copy this code and paste it in a message with ```vue wrapper:</p>

        <pre><code>&lt;template&gt;
  &lt;div class="todo-app"&gt;
    &lt;h1&gt;{{ title }}&lt;/h1&gt;

    &lt;form @submit.prevent="addTodo"&gt;
      &lt;input
        v-model="newTodo"
        placeholder="Add a new todo..."
        required
      /&gt;
      &lt;button type="submit"&gt;Add&lt;/button&gt;
    &lt;/form&gt;

    &lt;ul class="todo-list"&gt;
      &lt;li
        v-for="todo in filteredTodos"
        :key="todo.id"
        :class="{ completed: todo.completed }"
      &gt;
        &lt;input
          type="checkbox"
          v-model="todo.completed"
          @change="updateTodo(todo)"
        /&gt;
        &lt;span @dblclick="editTodo(todo)"&gt;{{ todo.text }}&lt;/span&gt;
        &lt;button @click="deleteTodo(todo.id)"&gt;Delete&lt;/button&gt;
      &lt;/li&gt;
    &lt;/ul&gt;

    &lt;div class="filters"&gt;
      &lt;button
        v-for="filter in filters"
        :key="filter"
        @click="currentFilter = filter"
        :class="{ active: currentFilter === filter }"
      &gt;
        {{ filter }}
      &lt;/button&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, computed, onMounted } from 'vue'
import { useTodoStore } from '@/stores/todo'

// Reactive data
const title = ref('Vue Todo App')
const newTodo = ref('')
const currentFilter = ref('All')
const filters = ['All', 'Active', 'Completed']

// Store
const todoStore = useTodoStore()

// Computed properties
const filteredTodos = computed(() =&gt; {
  switch (currentFilter.value) {
    case 'Active':
      return todoStore.todos.filter(todo =&gt; !todo.completed)
    case 'Completed':
      return todoStore.todos.filter(todo =&gt; todo.completed)
    default:
      return todoStore.todos
  }
})

// Methods
const addTodo = () =&gt; {
  if (newTodo.value.trim()) {
    todoStore.addTodo({
      id: Date.now(),
      text: newTodo.value.trim(),
      completed: false
    })
    newTodo.value = ''
  }
}

const updateTodo = (todo) =&gt; {
  todoStore.updateTodo(todo.id, { completed: todo.completed })
}

const deleteTodo = (id) =&gt; {
  todoStore.deleteTodo(id)
}

const editTodo = (todo) =&gt; {
  const newText = prompt('Edit todo:', todo.text)
  if (newText !== null && newText.trim()) {
    todoStore.updateTodo(todo.id, { text: newText.trim() })
  }
}

// Lifecycle
onMounted(() =&gt; {
  console.log('Todo app mounted')
})
&lt;/script&gt;

&lt;style scoped&gt;
.todo-app {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

h1 {
  color: #2c3e50;
  text-align: center;
  margin-bottom: 30px;
}

form {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

input[type="text"] {
  flex: 1;
  padding: 10px;
  border: 2px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

input[type="text"]:focus {
  outline: none;
  border-color: #3498db;
}

button {
  padding: 10px 20px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: background 0.3s;
}

button:hover {
  background: #2980b9;
}

.todo-list {
  list-style: none;
  padding: 0;
}

.todo-list li {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border-bottom: 1px solid #eee;
}

.todo-list li.completed span {
  text-decoration: line-through;
  color: #888;
}

.filters {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-top: 20px;
}

.filters button.active {
  background: #2c3e50;
}
&lt;/style&gt;</code></pre>
    </div>

    <div class="test-container">
        <h2>Expected Results</h2>
        <div class="success">
            <strong>✅ Success Indicators:</strong>
            <ul>
                <li>Vue directives (v-model, v-for, v-if, @click) should be highlighted in a distinct color</li>
                <li>Template section should show HTML syntax highlighting</li>
                <li>Script section should show JavaScript/TypeScript syntax highlighting</li>
                <li>Style section should show CSS syntax highlighting</li>
                <li>Vue-specific syntax like {{ }} interpolation should be highlighted</li>
                <li>No console errors about Vue language registration</li>
                <li>Code should not appear completely black/unstyled</li>
            </ul>
        </div>

        <div class="error">
            <strong>❌ Failure Indicators:</strong>
            <ul>
                <li>Code appears completely black with no syntax highlighting</li>
                <li>Console errors about Vue language definition</li>
                <li>Vue directives not highlighted differently from regular attributes</li>
                <li>All text appears in the same color</li>
            </ul>
        </div>
    </div>

    <div class="test-container">
        <h2>Console Check</h2>
        <div class="info">
            <strong>Check browser console for:</strong>
            <ul>
                <li>✅ "Vue language registered successfully for syntax highlighting"</li>
                <li>❌ Any errors about Vue language registration</li>
                <li>❌ "languageDefinition.bind is not a function"</li>
                <li>❌ "Language definition for 'vue' could not be registered"</li>
            </ul>
        </div>
    </div>

    <div class="test-container">
        <h2>🔧 Technical Details</h2>
        <div class="info">
            <strong>What was fixed:</strong>
            <ul>
                <li>✅ Fixed import to use definer export from highlightjs-vue directly</li>
                <li>✅ Bypassed broken react-syntax-highlighter Vue language wrapper</li>
                <li>✅ Fixed Vue language registration with Light build</li>
                <li>✅ Preserved theme background for Vue code blocks</li>
                <li>✅ Added proper error handling and logging</li>
                <li>✅ Reduced bundle size by avoiding duplicate dependencies</li>
            </ul>
        </div>
    </div>
</body>
</html>
