﻿# 🎨 FigmaAgent

A modern Figma plugin featuring AI-powered chat capabilities and multi-provider AI integration. Built with React 19, TypeScript, and Tailwind CSS v4.

## ✨ Features

### 🤖 AI Chat Interface
- **🔄 Multi-Provider Support**: OpenRouter and DeepSeek integration with seamless switching
- **⚡ Real-time Streaming**: Live AI responses with progress indicators and abort functionality
- **🔧 Multi-turn Tool Calling**: Advanced AI conversations with Figma context awareness
- **🎯 Minimalist Design**: Clean, responsive interface with Phi (Φ/φ) branding for Figma AI

### ⚙️ Smart Configuration
- **🔄 Bidirectional Synchronization**: Settings automatically sync between chat interface and settings modal
- **🎛️ Provider-Specific Settings**: Streamlined configuration sections that adapt to selected provider
- **💾 Immediate Persistence**: Changes save instantly with real-time feedback
- **📝 Customizable Terminology**: Personalize "Agent" and "Executor" names throughout the interface

### 👨‍💻 Developer Experience
- **🌐 Dual Environment Support**: Works as both Figma plugin and standalone web application
- **🔥 Hot Module Replacement**: Fast development with Vite and automatic reloading
- **🧠 Environment Detection**: Intelligent switching between plugin and web development modes
- **📘 TypeScript First**: Full type safety with auto-generated Figma API types



## 🚀 Quick Start

### 📋 Prerequisites
- 📦 Node.js 18+ and npm
- 🎨 Figma Desktop App (for plugin development)
- 🐍 Python 3.8+ (for model updates)

### 📥 Installation

`ash
# 📂 Clone the repository
git clone <repository-url>
cd figmagent

# 📦 Install dependencies
npm install

# 🤖 Update AI model data (optional)
npm run update-models
`

### 🛠️ Development

#### 🌐 Web Development Mode
`ash
# 🚀 Start development server
npm run dev

# 🌍 Open browser to http://localhost:5174
`

#### 🎨 Figma Plugin Mode
`ash
# 🏗️ Build for plugin
npm run build:prod

# 🎨 In Figma Desktop:
# 1. Go to Plugins → Development → Import plugin from manifest
# 2. Select the project root directory
# 3. Run the plugin from Plugins menu
`

## 📖 Usage Guide

### 🔧 Setting Up AI Providers

1. **⚙️ Open Settings**: Click the gear icon in the top-right corner
2. **🤖 Select Provider**: Choose between OpenRouter or DeepSeek
3. **🔑 Configure API Key**: Enter your API key for the selected provider
4. **🎯 Customize Model**: Select or enter your preferred model ID
5. **🎛️ Adjust Parameters**: Fine-tune temperature, max tokens, and system prompt

### 💬 Using the Chat Interface

1. **🔄 Provider Selection**: Use the Phi (Φ) icon to switch between AI providers
2. **⚙️ Model Configuration**: Modify model settings directly in the chat interface
3. **📤 Send Messages**: Type your message and press Enter or click Send
4. **⚡ Command System**: Use /ai [message] for direct AI interaction
5. **✅ Multi-select**: Hold Ctrl/Cmd to select multiple messages for bulk operations

### 📝 Terminology Customization

Personalize the interface language in Settings → Terminology:
- **🤖 Agent Name**: Customize how AI responses are labeled (default: "Agent")
- **⚙️ Executor Name**: Customize system message labels (default: "Executor")

## 🏗️ Architecture

### 📁 Project Structure
`
src/
 lib/                    # Core libraries
    figma/             # Figma API integration
    openrouter/        # OpenRouter AI client
    deepseek/          # DeepSeek AI client
 plugin/                # Figma plugin code
    plugin.ts          # Main plugin entry point
    message_handlers.ts # Plugin-UI communication
    figma_types.ts     # Auto-generated Figma types
 ui/                    # React UI application
    components/        # React components
    hooks/             # Custom React hooks
    store/             # Jotai state management
    utils/             # Utility functions
    types/             # TypeScript type definitions
 manifest.json5         # Figma plugin manifest
`

### 🛠️ Technology Stack

**🎨 Frontend**
- ⚛️ React 19 with TypeScript
- 🔄 Jotai for state management
- 🎨 Tailwind CSS v4 for styling
- 🪝 React-use for utility hooks

**🏗️ Build System**
- ⚡ Vite for fast development and building
- 🔧 Dual configuration for plugin and web modes
- 📄 JSON5 manifest processing
- 🤖 Automatic type generation

**🧠 AI Integration**
- 🌐 OpenRouter API for multi-model access
- 🧠 DeepSeek API for specialized models
- 📡 Streaming responses with abort support
- 🔧 Tool calling for Figma operations

**📡 Communication**
- 🎨 Figma Plugin API for document access
- 🔄 Real-time bidirectional synchronization

## ⚙️ Configuration

### 🌍 Environment Variables
`ash
# 🛠️ Development mode (automatically detected)
NODE_ENV=development

# 🌐 API endpoints (configured in code)
OPENROUTER_API_URL=https://openrouter.ai/api/v1
DEEPSEEK_API_URL=https://api.deepseek.com
`

### 🏗️ Build Modes
- **🛠️ Development**: `npm run dev` - Web development with hot reload
- **🚀 Production**: `npm run build:prod` - Optimized plugin build
- **🧪 Staging**: `npm run build:staging` - Staging build with debug info



## 🔒 Security & Privacy

- **🔑 API Keys**: Stored securely in Figma's clientStorage
- **🌐 Network Access**: Limited to approved domains in manifest
- **💾 Data Persistence**: All data stored locally, no external tracking

## 🤝 Contributing

1. 🍴 Fork the repository
2. 🌿 Create a feature branch: `git checkout -b feature/amazing-feature`
3. ✨ Make your changes with proper TypeScript types
4. 🧪 Test in both web and plugin modes
5. 📤 Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- **🐛 Issues**: Report bugs and request features via GitHub Issues
- **📚 Documentation**: Check the `/docs` directory for detailed guides
- **💡 Development**: See `/examples` for usage examples and demos

---

Built with ❤️ for the Figma community
