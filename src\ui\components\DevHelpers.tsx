// Development Helper Component
// Provides testing utilities for the mock Figma client in development mode

import { useState, useEffect } from 'react'
import { getFigmaClient, FigmaClient } from '../../lib/figma'

export function DevHelpers() {
  const [envInfo, setEnvInfo] = useState<any>(null)
  const [selectionData, setSelectionData] = useState<any>(null)
  const [storageTest, setStorageTest] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)

  // Auto-check environment on mount
  useEffect(() => {
    checkEnvironment()
  }, [])

  // Check environment detection
  const checkEnvironment = () => {
    const info = FigmaClient.detectEnvironment()
    setEnvInfo(info)
    console.log('🔍 Environment Info:', info)
  }

  // Test mock selection
  const testSelection = async () => {
    setIsLoading(true)
    try {
      const client = getFigmaClient()
      const selection = await client.selection.getSelection()
      setSelectionData(selection)
      console.log('🎯 Selection Data:', selection)
    } catch (error) {
      console.error('❌ Selection Error:', error)
      setSelectionData({ error: error instanceof Error ? error.message : 'Unknown error' })
    } finally {
      setIsLoading(false)
    }
  }

  // Test storage operations
  const testStorage = async () => {
    try {
      const client = getFigmaClient()
      const testKey = 'dev-test-key'
      const testValue = { message: 'Hello from dev mode!', timestamp: Date.now() }

      // Set item
      await client.storage.setItem(testKey, testValue)
      console.log('💾 Stored test data')

      // Get item
      const retrieved = await client.storage.getItem(testKey, null)
      console.log('📦 Retrieved test data:', retrieved)
      setStorageTest(JSON.stringify(retrieved, null, 2))

      // Remove item
      await client.storage.removeItem(testKey)
      console.log('🗑️ Removed test data')
    } catch (error) {
      console.error('❌ Storage Error:', error)
      setStorageTest(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  // Test window operations
  const testWindow = async () => {
    try {
      const client = getFigmaClient()
      console.log('🪟 Testing window resize...')
      await client.window.resize(500, 700)
      console.log('✅ Window resize completed')
    } catch (error) {
      console.error('❌ Window Error:', error)
    }
  }

  // Only show in development environment
  if (!envInfo || envInfo.environment !== 'web-dev') {
    return (
      <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 className="text-sm font-medium text-blue-800 mb-2">Development Helpers</h3>
        <button
          onClick={checkEnvironment}
          className="px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700"
        >
          Check Environment
        </button>
        {envInfo && (
          <div className="mt-2 text-xs text-blue-700">
            Environment: {envInfo.environment} | Dev: {envInfo.isDevelopment ? 'Yes' : 'No'}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
      <h3 className="text-sm font-medium text-green-800 mb-3">🛠️ Development Mode Active</h3>

      <div className="space-y-3">
        {/* Environment Info */}
        <div>
          <h4 className="text-xs font-medium text-green-700 mb-1">Environment</h4>
          <div className="text-xs text-green-600 bg-green-100 p-2 rounded">
            <div>Mode: {envInfo.environment}</div>
            <div>Development: {envInfo.isDevelopment ? 'Yes' : 'No'}</div>
            <div>Has Parent: {envInfo.hasParentWindow ? 'Yes' : 'No'}</div>
          </div>
        </div>

        {/* Test Buttons */}
        <div className="flex flex-wrap gap-2">
          <button
            onClick={testSelection}
            disabled={isLoading}
            className="px-3 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700 disabled:opacity-50"
          >
            {isLoading ? 'Loading...' : 'Test Selection'}
          </button>
          <button
            onClick={testStorage}
            className="px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700"
          >
            Test Storage
          </button>
          <button
            onClick={testWindow}
            className="px-3 py-1 bg-purple-600 text-white text-xs rounded hover:bg-purple-700"
          >
            Test Window
          </button>
        </div>

        {/* Selection Results */}
        {selectionData && (
          <div>
            <h4 className="text-xs font-medium text-green-700 mb-1">Selection Data</h4>
            <pre className="text-xs text-green-600 bg-green-100 p-2 rounded overflow-auto max-h-32">
              {JSON.stringify(selectionData, null, 2)}
            </pre>
          </div>
        )}

        {/* Storage Results */}
        {storageTest && (
          <div>
            <h4 className="text-xs font-medium text-green-700 mb-1">Storage Test</h4>
            <pre className="text-xs text-green-600 bg-green-100 p-2 rounded overflow-auto max-h-32">
              {storageTest}
            </pre>
          </div>
        )}

        {/* Global Helpers Info */}
        <div className="text-xs text-green-600">
          <div className="font-medium mb-1">Console Helpers Available:</div>
          <div>• <code>window.__figmaDevHelpers.toggleSelection()</code></div>
          <div>• <code>window.__figmaDevHelpers.getStorageKeys()</code></div>
          <div>• <code>window.__figmaDevHelpers.clearStorage()</code></div>
        </div>
      </div>
    </div>
  )
}
