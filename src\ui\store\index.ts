// Store exports - centralized access to all Jotai state management
// This file provides a single import point for all store-related functionality

// Export all custom hooks (which now include their respective atoms)

export * from './useUiHooks'
export * from './useChatHooks'
// useCommandHooks merged into useChatHooks
export * from './useAiHooks'
// useTerminologyHooks merged into useStorageHooks
export * from './useConversationHooks'

// Export consolidated storage hooks
export {
  useVendorStorage,
  useOpenRouterStorage,
  useDeepSeekStorage,
  useTerminologyStorage,
  useCodeBlockSettingsStorage,
  useTerminology, // Now exported from useStorageHooks
  useCodeBlockSettings // Now exported from useStorageHooks
} from './useStorageHooks'
export { useConversationStorage } from './useConversationStorage'

// Export provider
export { JotaiProvider, useIsDevelopment } from './provider'

// Export commonly used Jotai utilities
export { useAtom, useAtomValue, useSetAtom } from 'jotai'
export { atomWithStorage } from 'jotai/utils'
