# FigmaAgent 环境变量配置示例
# 复制此文件为 .env 并填入实际值

# ===========================================
# 开发环境配置
# ===========================================

# 开发模式 (development | production | staging)
NODE_ENV=development

# 开发服务器端口
VITE_DEV_PORT=5173

# 是否启用热重载
VITE_HMR=true

# ===========================================
# AI 服务配置 (可选 - 也可在 UI 中配置)
# ===========================================

# OpenRouter API 配置
# 获取密钥: https://openrouter.ai/
VITE_OPENROUTER_API_KEY=your_openrouter_api_key_here
VITE_OPENROUTER_MODEL=anthropic/claude-3.5-sonnet

# DeepSeek API 配置  
# 获取密钥: https://api.deepseek.com/
VITE_DEEPSEEK_API_KEY=your_deepseek_api_key_here
VITE_DEEPSEEK_MODEL=deepseek-chat

# ===========================================
# 调试和开发工具
# ===========================================

# 是否启用调试模式
VITE_DEBUG=true

# 是否启用性能分析
VITE_PERFORMANCE_PROFILING=false

# 是否启用详细日志
VITE_VERBOSE_LOGGING=true

# ===========================================
# Figma 插件配置
# ===========================================

# 插件 ID (从 Figma 开发者控制台获取)
VITE_FIGMA_PLUGIN_ID=1508668258843197249

# 是否启用开发者工具
VITE_ENABLE_DEV_TOOLS=true

# ===========================================
# 网络和安全配置
# ===========================================

# 允许的域名 (用于网络访问)
VITE_ALLOWED_DOMAINS=openrouter.ai,api.deepseek.com

# API 请求超时时间 (毫秒)
VITE_API_TIMEOUT=30000

# ===========================================
# 构建配置
# ===========================================

# 是否启用源码映射
VITE_SOURCEMAP=true

# 是否压缩代码
VITE_MINIFY=true

# 构建目标目录
VITE_BUILD_DIR=dist

# ===========================================
# 实验性功能
# ===========================================

# 是否启用 Zenoh 分布式通信
VITE_ENABLE_ZENOH=false

# 是否启用实验性 AI 功能
VITE_ENABLE_EXPERIMENTAL_AI=false

# ===========================================
# 注意事项
# ===========================================

# 1. 环境变量以 VITE_ 开头的会被打包到客户端代码中
# 2. 敏感信息（如 API 密钥）建议在 UI 中配置而不是环境变量
# 3. 开发环境的配置不会影响生产构建
# 4. 修改环境变量后需要重启开发服务器
