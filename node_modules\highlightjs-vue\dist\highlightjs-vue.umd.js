(function (factory) {
  typeof define === 'function' && define.amd ? define(factory) :
  factory();
}((function () { 'use strict';

  /*
  Language: Vue.js
  Requires: xml.js, javascript.js, typescript.js, css.js, stylus.js, scss.js
  Author: <PERSON> <<EMAIL>>
  Description: Single-File Components of Vue.js Framework
  */
  var module = module ? module : {};

  function hljsDefineVue(hljs) {
    return {
      subLanguage: "xml",
      contains: [
        hljs.COMMENT("<!--", "-->", {
          relevance: 10,
        }),
        {
          begin: /^(\s*)(<script>)/gm,
          end: /^(\s*)(<\/script>)/gm,
          subLanguage: "javascript",
          excludeBegin: true,
          excludeEnd: true,
        },
        {
          begin: /^(\s*)(<script lang=["']ts["']>)/gm,
          end: /^(\s*)(<\/script>)/gm,
          subLanguage: "typescript",
          excludeBegin: true,
          excludeEnd: true,
        },
        {
          begin: /^(\s*)(<style(\sscoped)?>)/gm,
          end: /^(\s*)(<\/style>)/gm,
          subLanguage: "css",
          excludeBegin: true,
          excludeEnd: true,
        },
        {
          begin: /^(\s*)(<style lang=["'](scss|sass)["'](\sscoped)?>)/gm,
          end: /^(\s*)(<\/style>)/gm,
          subLanguage: "scss",
          excludeBegin: true,
          excludeEnd: true,
        },
        {
          begin: /^(\s*)(<style lang=["']stylus["'](\sscoped)?>)/gm,
          end: /^(\s*)(<\/style>)/gm,
          subLanguage: "stylus",
          excludeBegin: true,
          excludeEnd: true,
        },
      ],
    };
  }

  module.exports = function(hljs) {
    hljs.registerLanguage("vue", hljsDefineVue);
  };

  module.exports.definer = hljsDefineVue;

})));
