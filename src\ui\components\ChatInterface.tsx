// Chat interface component for displaying messages with virtualization

import React, { useRef, useCallback, useEffect, useState, useDeferredValue, useMemo } from 'react'
import { useUpdateEffect } from 'react-use'
import { useVirtualizer } from '@tanstack/react-virtual'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import InlineLoadingIndicator from './InlineLoadingIndicator'
import MessageContentWithProgress from './MessageContentWithProgress'
import { Light as HljsSyntaxHighlighter } from 'react-syntax-highlighter'
import { ChatInterfaceProps, Message } from '../types/ui_types'
import { useTerminologyStorage } from '../store'
import UnifiedCodeBlock from './UnifiedCodeBlock'
import PerformanceProfiler from './PerformanceProfiler'
import { scheduleBackgroundTask } from '../utils/input_scheduler'

// Import Vue language definition directly from highlightjs-vue
import { definer as vue } from 'highlightjs-vue'

// Register Vue language for highlight.js
try {
  HljsSyntaxHighlighter.registerLanguage('vue', vue)
  console.log('✅ Vue language registered successfully for syntax highlighting')
} catch (error) {
  console.warn('❌ Failed to register Vue language for syntax highlighting:', error)
}

export const ChatInterface: React.FC<ChatInterfaceProps> = ({
  messages,
  copiedMessageId,
  onCopyMessage,
  selectedMessageIds = new Set(),
  isSelectionMode = false,
  onToggleSelection,
  onSelectAll,
  onClearSelection,
  onDeleteSelected,
  onCopySelected,
  forceScrollToBottom = false,
  onResendMessage
}) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const { terminology } = useTerminologyStorage()







  // Performance monitoring (enabled in development)
  const [performanceEnabled] = useState(() => process.env.NODE_ENV === 'development')

  // Use immediate messages for better responsiveness
  // Only defer rendering when there are many messages to avoid input lag
  // Increased threshold to 200 to prioritize input responsiveness over early optimization
  const shouldUseDeferred = messages.length > 200
  const deferredMessages = useDeferredValue(messages)
  const messagesToRender = shouldUseDeferred ? deferredMessages : messages
  const shouldShowLoadingState = shouldUseDeferred && messages.length !== deferredMessages.length

  // Enhanced size estimation with caching for better performance
  const sizeCache = useRef<Map<string, number>>(new Map())

  const estimateSize = useMemo(() => {
    return (index: number) => {
      // Use immediate messages for size estimation to avoid input lag
      const message = messages[index]
      if (!message) return 120

      // Check cache first for performance
      const cacheKey = `${message.id}-${message.text.length}`
      const cachedSize = sizeCache.current.get(cacheKey)
      if (cachedSize) {
        return cachedSize
      }

      // Fast estimation based on text length and type
      const textLength = message.text.length
      let estimatedHeight = 80 // Base height for message container

      // Quick text-based estimation
      if (textLength < 100) {
        estimatedHeight += 40 // Short message
      } else if (textLength < 500) {
        estimatedHeight += 80 // Medium message
      } else if (textLength < 2000) {
        estimatedHeight += 160 // Long message
      } else {
        estimatedHeight += 300 // Very long message
      }

      // Message type adjustments
      if (message.type === 'user') {
        estimatedHeight += 20 // User message styling
      } else if (message.type === 'ai') {
        estimatedHeight += 40 // AI message with header
      }

      // Quick code block detection (only if text is long enough)
      if (textLength > 100 && message.text.includes('```')) {
        const codeBlockCount = (message.text.match(/```/g) || []).length / 2
        estimatedHeight += codeBlockCount * 150 // Estimate per code block
      }

      const finalHeight = Math.max(estimatedHeight, 100)

      // Cache the result for future use
      sizeCache.current.set(cacheKey, finalHeight)

      // Limit cache size to prevent memory leaks - schedule as background task
      if (sizeCache.current.size > 500) { // Reduced cache size for better memory usage
        scheduleBackgroundTask(() => {
          // Remove oldest entries
          const entries = Array.from(sizeCache.current.entries())
          const toRemove = entries.slice(0, 100) // Remove 100 oldest entries
          toRemove.forEach(([key]) => sizeCache.current.delete(key))
        }, 'cache-cleanup')
      }

      return finalHeight
    }
  }, [messages])

  // Create virtualizer for messages using deferred data with enhanced performance
  const virtualizer = useVirtualizer({
    count: messagesToRender.length,
    getScrollElement: () => containerRef.current,
    estimateSize,
    overscan: 1, // Reduced overscan to minimum for better performance
    measureElement: useCallback((element: Element | null) => {
      // Use actual measured height when available for better accuracy
      if (!element) return 120

      // Cache measured heights for better performance - schedule as background task
      scheduleBackgroundTask(() => {
        const height = element.getBoundingClientRect().height
        const messageElement = element.querySelector('[data-message-id]')
        if (messageElement) {
          const messageId = messageElement.getAttribute('data-message-id')
          if (messageId) {
            sizeCache.current.set(`measured-${messageId}`, height)
          }
        }
      }, 'measure-element')

      return element.getBoundingClientRect().height
    }, []),
    // Enable smooth scrolling for better UX
    scrollPaddingStart: 0,
    scrollPaddingEnd: 0,
    // Optimize for dynamic content
    lanes: 1,
    // Add performance optimizations
    initialRect: { width: 0, height: 0 },
  })

  // Track if user is near bottom for auto-scroll behavior
  const [isNearBottom, setIsNearBottom] = useState(true)
  const previousMessageCount = useRef(messages.length)
  const previousMessagesRef = useRef<Message[]>([])
  const [shouldForceScroll, setShouldForceScroll] = useState(false)
  const lastScrollTime = useRef(0)
  const SCROLL_THROTTLE_MS = 16 // Throttle scrolling to ~60fps for smoother streaming (16ms ≈ 60fps)

  // Handle external force scroll requests
  useEffect(() => {
    if (forceScrollToBottom) {
      setShouldForceScroll(true)
    }
  }, [forceScrollToBottom])

  // Handle scroll events to track user position
  const handleScroll = useCallback(() => {
    if (!containerRef.current) return

    const container = containerRef.current
    const scrollTop = container.scrollTop
    const scrollHeight = container.scrollHeight
    const clientHeight = container.clientHeight

    // Consider "near bottom" if within 100px of the bottom
    const nearBottom = scrollTop + clientHeight >= scrollHeight - 100
    setIsNearBottom(nearBottom)
  }, [])

  // Attach scroll listener
  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    container.addEventListener('scroll', handleScroll, { passive: true })
    return () => container.removeEventListener('scroll', handleScroll)
  }, [handleScroll])

  // Enhanced auto-scroll logic - handles both new messages and streaming updates
  useUpdateEffect(() => {
    const hasNewMessages = messages.length > previousMessageCount.current
    const previousMessages = previousMessagesRef.current

    // Check if any existing message content has changed (streaming updates)
    const hasStreamingUpdates = messages.some((msg, index) => {
      const prevMsg = previousMessages[index]
      return prevMsg && prevMsg.id === msg.id && prevMsg.text !== msg.text && msg.isStreaming
    })

    // Check if any AI message content has changed (tool execution results)
    const hasToolResultUpdates = messages.some((msg, index) => {
      const prevMsg = previousMessages[index]
      const hasUpdate = prevMsg && prevMsg.id === msg.id && prevMsg.text !== msg.text &&
                       msg.type === 'ai' && !msg.isStreaming

      // Log tool result updates for debugging
      if (hasUpdate) {
        console.log('🔧 [ChatInterface] Tool result update detected:', {
          messageId: msg.id,
          prevLength: prevMsg.text.length,
          newLength: msg.text.length,
          isStreaming: msg.isStreaming
        })
      }

      return hasUpdate
    })

    // Check if any message has loading state changes (multi-turn streaming)
    const hasLoadingStateChanges = messages.some((msg, index) => {
      const prevMsg = previousMessages[index]
      return prevMsg && prevMsg.id === msg.id &&
             JSON.stringify(prevMsg.loadingState) !== JSON.stringify(msg.loadingState) &&
             msg.loadingState?.isActive
    })

    // Check if the last message is from user (force scroll for user messages)
    const lastMessage = messages[messages.length - 1]
    const isUserMessage = lastMessage?.type === 'user'
    const isLastMessageStreaming = lastMessage?.isStreaming || lastMessage?.loadingState?.isActive

    // Update refs
    previousMessageCount.current = messages.length
    previousMessagesRef.current = [...messages]

    // Auto-scroll conditions:
    // 1. New messages and user is near bottom
    // 2. User just sent a message (always scroll)
    // 3. AI is streaming and user is near bottom
    // 4. Tool execution results added to AI messages
    // 5. Loading state changes during multi-turn streaming
    // 6. Force scroll flag is set
    const shouldScroll = (hasNewMessages && (isNearBottom || isUserMessage)) ||
                        (hasStreamingUpdates && isNearBottom) ||
                        (hasToolResultUpdates && isNearBottom) ||
                        (hasLoadingStateChanges && isNearBottom) ||
                        shouldForceScroll

    if (shouldScroll && messages.length > 0) {
      const now = Date.now()
      const isStreamingUpdate = hasStreamingUpdates || hasLoadingStateChanges
      const isToolResultUpdate = hasToolResultUpdates

      // Throttle streaming updates to prevent excessive scrolling, but allow tool result updates
      if (isStreamingUpdate && !shouldForceScroll && !isToolResultUpdate && (now - lastScrollTime.current) < SCROLL_THROTTLE_MS) {
        return
      }

      lastScrollTime.current = now

      // Use requestAnimationFrame for smooth scrolling without blocking
      requestAnimationFrame(() => {
        // Log auto-scroll trigger for tool results
        if (isToolResultUpdate) {
          console.log('📜 [ChatInterface] Auto-scrolling to bottom after tool execution results')
        }

        // Scroll to the last message
        virtualizer.scrollToIndex(messages.length - 1, {
          align: 'end',
          // Use smooth scrolling for user messages and tool results, auto for streaming to reduce jank
          behavior: (isUserMessage && !isLastMessageStreaming) || isToolResultUpdate ? 'smooth' : 'auto'
        })
      })

      // Reset force scroll flag
      if (shouldForceScroll) {
        setShouldForceScroll(false)
      }
    }
  }, [messages, isNearBottom, virtualizer, shouldForceScroll])

  // Initial scroll to bottom when component mounts with messages
  useEffect(() => {
    if (messages.length > 0) {
      // Use requestAnimationFrame to ensure virtualizer is ready
      requestAnimationFrame(() => {
        virtualizer.scrollToIndex(messages.length - 1, {
          align: 'end'
        })
        setIsNearBottom(true)
      })
    }
  }, []) // Only run on mount



  // Memoized markdown content renderer for performance with JSON optimization
  const renderMessageContent = useCallback((text: string) => {
    return (
      <div className="prose prose-sm max-w-none prose-slate text-gray-800 text-sm">
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          components={{
            // Custom styling for paragraphs to ensure text color and compact size
            p: ({ children }) => (
              <p className="text-gray-800 text-sm mb-1.5 leading-relaxed">
                {children}
              </p>
            ),
            // Custom styling for headings - more compact
            h1: ({ children }) => (
              <h1 className="text-gray-900 text-lg font-bold mb-2">
                {children}
              </h1>
            ),
            h2: ({ children }) => (
              <h2 className="text-gray-900 text-base font-semibold mb-1.5">
                {children}
              </h2>
            ),
            h3: ({ children }) => (
              <h3 className="text-gray-900 text-sm font-medium mb-1.5">
                {children}
              </h3>
            ),
            // Custom styling for lists - more compact
            ul: ({ children }) => (
              <ul className="text-gray-800 text-sm list-disc list-inside mb-1.5 space-y-0.5">
                {children}
              </ul>
            ),
            ol: ({ children }) => (
              <ol className="text-gray-800 text-sm list-decimal list-inside mb-1.5 space-y-0.5">
                {children}
              </ol>
            ),
            li: ({ children }) => (
              <li className="text-gray-800 text-sm leading-relaxed">
                {children}
              </li>
            ),
            // Custom styling for code blocks with basic syntax highlighting
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            code: (props: any) => {
              const { children, className } = props
              const match = /language-(\w+)/.exec(className || '')
              const language = match ? match[1] : ''
              const isInline = !className?.includes('language-')
              const codeContent = String(children).replace(/\n$/, '')

              if (isInline) {
                return (
                  <code className="bg-gray-100 text-gray-800 px-1 py-0.5 rounded text-xs font-mono">
                    {children}
                  </code>
                )
              }

              // Use UnifiedCodeBlock for all code blocks (including JSON)
              return (
                <UnifiedCodeBlock
                  code={codeContent}
                  language={language || 'text'}
                  showLineNumbers={Boolean(language && ['javascript', 'typescript', 'python', 'java', 'cpp', 'c', 'css', 'html', 'json', 'vue'].includes(language))}
                  variant={language === 'json' ? 'json' : 'standard'}
                />
              )
            },
            // Custom styling for blockquotes - more compact
            blockquote: ({ children }) => (
              <blockquote className="border-l-4 border-gray-300 pl-3 italic text-gray-600 text-sm mb-2">
                {children}
              </blockquote>
            ),
            // Custom styling for strong/bold text
            strong: ({ children }) => (
              <strong className="text-gray-900 font-semibold text-sm">
                {children}
              </strong>
            ),
            // Custom styling for emphasis/italic text
            em: ({ children }) => (
              <em className="text-gray-700 italic text-sm">
                {children}
              </em>
            ),
            // Custom styling for tables - more compact
            table: ({ children }) => (
              <div className="overflow-x-auto mb-2">
                <table className="min-w-full border-collapse border border-gray-300 text-sm">
                  {children}
                </table>
              </div>
            ),
            th: ({ children }) => (
              <th className="border border-gray-300 px-2 py-1 bg-gray-50 font-medium text-left text-gray-800 text-xs">
                {children}
              </th>
            ),
            td: ({ children }) => (
              <td className="border border-gray-300 px-2 py-1 text-gray-800 text-sm">
                {children}
              </td>
            )
          }}
        >
          {text}
        </ReactMarkdown>
      </div>
    )
  }, [])

  // Memoize the message renderer for performance with proper dependencies
  const renderMessage = useCallback((msg: Message, _index: number) => {
    const isSelected = selectedMessageIds.has(msg.id)

    // Track AI message state changes for debugging and safety checks
    if (msg.type === 'ai') {
      const isActive = msg.isStreaming || msg.loadingState?.isActive
      const showCopyButton = !msg.isStreaming && !msg.loadingState?.isActive

      // Safety check: if loadingState exists but phase is 'completing', force isActive to false
      const safeLoadingActive = msg.loadingState?.isActive && msg.loadingState?.phase !== 'completing'
      const safeIsActive = msg.isStreaming || safeLoadingActive
      const safeShowCopyButton = !msg.isStreaming && !safeLoadingActive

      // Log state changes for debugging (only in development)
      if (process.env.NODE_ENV === 'development') {
        console.log(`🎭 [ChatInterface] AI Message State:`, {
          messageId: msg.id.slice(-8),
          isStreaming: msg.isStreaming,
          loadingActive: msg.loadingState?.isActive,
          loadingPhase: msg.loadingState?.phase,
          isInterrupted: msg.isInterrupted,
          safeLoadingActive: safeLoadingActive,
          showBreathingLight: safeIsActive,
          showCopyButton: safeShowCopyButton,
          originalActive: isActive,
          originalCopyButton: showCopyButton
        })
      }
    }

    return (
      <div
        className={`min-h-[40px] ${msg.type === 'user' ? 'flex justify-end' : ''} ${isSelectionMode ? 'relative' : ''}`}
      >
        {/* Selection checkbox */}
        {isSelectionMode && (
          <div className="absolute left-0 top-2 z-10">
            <input
              type="checkbox"
              checked={isSelected}
              onChange={() => onToggleSelection?.(msg.id)}
              className="w-4 h-4 text-blue-600 bg-white border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
            />
          </div>
        )}

        {msg.type === 'user' ? (
          // User message - right aligned with very light gray background
          <div className={`max-w-[80%] rounded-lg px-3 py-2 bg-gray-50 text-gray-800 border border-gray-200 ${isSelectionMode ? 'ml-6' : ''} ${isSelected ? 'ring-2 ring-blue-500' : ''}`}>
            <div className="text-xs opacity-70 mb-1">
              You • {msg.timestamp instanceof Date ? msg.timestamp.toLocaleTimeString() : new Date(msg.timestamp).toLocaleTimeString()}
            </div>
            {renderMessageContent(msg.text)}
          </div>
        ) : msg.type === 'ai' ? (
          // AI message - full width with special AI styling
          <div className={`-mx-3 w-[calc(100%+1.5rem)] ${isSelected ? 'bg-blue-50' : ''}`}>
            <div className={`px-3 py-2 ${isSelectionMode ? 'ml-6' : ''}`}>
            <div className="flex items-center gap-2 mb-2">
              <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-bold text-sm">φ</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-xs text-blue-600 font-medium">
                  {terminology.agentName}
                </span>
                {(msg.vendor && msg.model) && (
                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded">
                    {msg.vendor}:{msg.model}
                  </span>
                )}
                {(() => {
                  // Safe state calculation with additional checks
                  const safeLoadingActive = msg.loadingState?.isActive && msg.loadingState?.phase !== 'completing'
                  const shouldShowBreathingLight = (msg.isStreaming || safeLoadingActive) && !msg.isInterrupted

                  return shouldShowBreathingLight && (
                    <span
                      className="text-blue-500 text-xs animate-pulse"
                      title={`${msg.isStreaming ? 'Streaming' : ''}${msg.isStreaming && safeLoadingActive ? ' & ' : ''}${safeLoadingActive ? `Loading (${msg.loadingState?.phase})` : ''}`}
                    >
                      ●
                    </span>
                  )
                })()}
              </div>
            </div>

            {/* Message Content with Dynamic Progress Indicator */}
            {msg.text && (
              <div className="text-gray-800 mb-2">
                <MessageContentWithProgress
                  content={msg.text}
                  loadingState={msg.loadingState}
                />
              </div>
            )}

            {/* Interruption Indicator */}
            {msg.isInterrupted && (
              <div className="flex items-center gap-2 mt-2 mb-2 px-3 py-2 bg-orange-50 border border-orange-200 rounded-lg">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-orange-600 flex-shrink-0">
                  <rect x="6" y="6" width="12" height="12" fill="currentColor" rx="2"/>
                </svg>
                <span className="text-sm text-orange-700 font-medium">
                  Response interrupted by user
                </span>
              </div>
            )}

            {/* Fallback Loading Indicator (only if not inline) */}
            {msg.loadingState && msg.loadingState.isActive && !msg.loadingState.hasInlineMarker && (
              <InlineLoadingIndicator
                loadingState={msg.loadingState}
                position={msg.loadingState.position || 'start'}
              />
            )}

            {/* Copy and Resend buttons at the end of the message - show when complete or interrupted */}
            {(() => {
              // Safe state calculation for button visibility
              const safeLoadingActive = msg.loadingState?.isActive && msg.loadingState?.phase !== 'completing'
              const shouldShowButtons = (!msg.isStreaming && !safeLoadingActive) || msg.isInterrupted

              return shouldShowButtons && (
                <div className="flex justify-end gap-1 mt-2">
                  {/* Resend button */}
                  {onResendMessage && (
                    <button
                      onClick={() => onResendMessage(msg.id)}
                      className="p-1.5 rounded transition-colors text-gray-400 hover:text-gray-600 hover:bg-gray-50"
                      title="Resend message"
                    >
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M4 12C4 16.4183 7.58172 20 12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C9.25 4 6.82 5.38 5.38 7.5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M2 7.5L5.38 7.5L5.38 11" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </button>
                  )}

                  {/* Copy button */}
                  <button
                    onClick={() => onCopyMessage(msg.text, msg.id)}
                    className={`p-1.5 rounded transition-colors ${
                      copiedMessageId === msg.id
                        ? 'text-green-600 bg-green-50 hover:bg-green-100'
                        : 'text-gray-400 hover:text-gray-600 hover:bg-gray-50'
                    }`}
                    title="Copy message"
                  >
                    {copiedMessageId === msg.id ? (
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 16.17L4.83 12L3.41 13.41L9 19L21 7L19.59 5.59L9 16.17Z" fill="currentColor"/>
                      </svg>
                    ) : (
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M16 1H4C2.9 1 2 1.9 2 3V17H4V3H16V1ZM19 5H8C6.9 5 6 5.9 6 7V21C6 22.1 6.9 23 8 23H19C20.1 23 21 22.1 21 21V7C21 5.9 20.1 5 19 5ZM19 21H8V7H19V21Z" fill="currentColor"/>
                      </svg>
                    )}
                  </button>
                </div>
              )
            })()}
          </div>
        </div>
        ) : (
          // System message - simplified styling to match Agent format
          <div className={`${isSelectionMode ? 'ml-6' : ''} ${isSelected ? 'bg-blue-50 rounded-lg p-2' : ''}`}>
            <div className="flex items-center gap-2 mb-1">
              <div className="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM7 7H17C18.1 7 19 7.9 19 9V18C19 19.1 18.1 20 17 20H7C5.9 20 5 19.1 5 18V9C5 7.9 5.9 7 7 7ZM9 10C8.4 10 8 10.4 8 11C8 11.6 8.4 12 9 12C9.6 12 10 11.6 10 11C10 10.4 9.6 10 9 10ZM15 10C14.4 10 14 10.4 14 11C14 11.6 14.4 12 15 12C15.6 12 16 11.6 16 11C16 10.4 15.6 10 15 10ZM8 14H16V16H8V14Z" fill="#6B7280"/>
                </svg>
              </div>
              <span className="text-xs text-gray-600 font-medium">
                {terminology.executorName}
              </span>
            </div>
            <div className="text-gray-800">
              {renderMessageContent(msg.text)}
            </div>
          </div>
        )}
      </div>
    )
  }, [selectedMessageIds, isSelectionMode, onToggleSelection, terminology, renderMessageContent, copiedMessageId, onCopyMessage, onResendMessage])

  return (
    <>
      <div className="flex-1 mb-3 border border-gray-300 rounded overflow-hidden flex flex-col">
        {/* Selection toolbar */}
      {isSelectionMode && (
        <div className="bg-blue-50 border-b border-blue-200 px-3 py-2 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <span className="text-sm text-blue-700 font-medium">
              {selectedMessageIds.size} message{selectedMessageIds.size !== 1 ? 's' : ''} selected
            </span>
            {/* Select All Checkbox */}
            <label className="flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={selectedMessageIds.size === messages.length && messages.length > 0}
                ref={(input) => {
                  if (input) {
                    // Set indeterminate state when some (but not all) messages are selected
                    input.indeterminate = selectedMessageIds.size > 0 && selectedMessageIds.size < messages.length
                  }
                }}
                onChange={onSelectAll}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                title={selectedMessageIds.size === messages.length && messages.length > 0 ? "Deselect all" : "Select all"}
              />
            </label>
          </div>
          <div className="flex items-center gap-1">
            {/* Copy button - icon only */}
            <button
              onClick={onCopySelected}
              disabled={selectedMessageIds.size === 0}
              className="p-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              title="Copy selected messages"
            >
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M16 1H4C2.9 1 2 1.9 2 3V17H4V3H16V1ZM19 5H8C6.9 5 6 5.9 6 7V21C6 22.1 6.9 23 8 23H19C20.1 23 21 22.1 21 21V7C21 5.9 20.1 5 19 5ZM19 21H8V7H19V21Z" fill="currentColor"/>
              </svg>
            </button>
            {/* Delete button - icon only */}
            <button
              onClick={onDeleteSelected}
              disabled={selectedMessageIds.size === 0}
              className="p-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              title="Delete selected messages"
            >
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 6H5H21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
            {/* Cancel button - icon only */}
            <button
              onClick={onClearSelection}
              className="p-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50 transition-colors"
              title="Cancel selection"
            >
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
          </div>
        </div>
      )}

      <div
        ref={containerRef}
        className="flex-1 overflow-y-auto"
        style={{ minHeight: '200px' }}
      >
        {messagesToRender.length === 0 ? (
          <div className="p-3">
            <p className="text-gray-500 text-sm text-center">No messages yet...</p>
          </div>
        ) : (
          <div
            className="relative w-full"
            style={{
              height: virtualizer.getTotalSize(),
            }}
          >

            {virtualizer.getVirtualItems().map((virtualItem) => {
              const msg = messagesToRender[virtualItem.index]
              if (!msg) return null

              return (
                <div
                  key={msg.id}
                  data-index={virtualItem.index}
                  data-message-id={msg.id}
                  ref={virtualizer.measureElement}
                  className={`absolute top-0 left-0 w-full transition-opacity duration-200 ease-in-out ${
                    shouldShowLoadingState ? 'opacity-70' : 'opacity-100'
                  }`}
                  style={{
                    transform: `translateY(${virtualItem.start}px)`,
                    willChange: 'transform',
                  }}
                >
                  <div className="px-3 py-2 space-y-2" data-message-id={msg.id}>
                    {renderMessage(msg, virtualItem.index)}
                  </div>
                </div>
              )
            })}

            {/* Loading indicator for deferred rendering */}
            {shouldShowLoadingState && (
              <div className="absolute top-2 right-2 z-10">
                <div className="bg-blue-100 text-blue-600 px-2 py-1 rounded-md text-xs flex items-center gap-1">
                  <div className="w-3 h-3 border border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                  Updating...
                </div>
              </div>
            )}
          </div>
        )}
      </div>
      </div>

      {/* Performance Profiler for debugging */}
      <PerformanceProfiler enabled={performanceEnabled} />
    </>
  )
}
