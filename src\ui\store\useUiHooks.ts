// UI State Management Hooks
// Hooks for managing window settings, tabs, modals, and UI state

import { atom, useAtom, useAtomValue, useSetAtom } from 'jotai'
import { useCallback, useMemo } from 'react'
import { WindowSettings, DEFAULT_WINDOW_SETTINGS } from '../../lib/figma'

// =============================================================================
// UI ATOMS
// =============================================================================

// Window state - persisted to storage
export const windowSettingsAtom = atom<WindowSettings>(
  DEFAULT_WINDOW_SETTINGS
)

// UI state - not persisted
export const isMinimizedAtom = atom<boolean>(false)
export const activeTabAtom = atom<string>('chat')
export const showSettingsAtom = atom<boolean>(false)

/**
 * Hook for managing window settings
 */
export function useWindowSettings() {
  const [windowSettings, setWindowSettings] = useAtom(windowSettingsAtom)

  return {
    windowSettings,
    setWindowSettings,
    updateWindowSize: useCallback((width: number, height: number) => {
      setWindowSettings(prev => ({ ...prev, width, height }))
    }, [setWindowSettings])
  }
}

/**
 * Hook for managing minimized state
 */
export function useMinimizedState() {
  const [isMinimized, setIsMinimized] = useAtom(isMinimizedAtom)

  return useMemo(() => ({
    isMinimized,
    setIsMinimized,
    minimize: () => setIsMinimized(true),
    expand: () => setIsMinimized(false)
  }), [isMinimized, setIsMinimized])
}

/**
 * Hook for managing active tab
 */
export function useActiveTab() {
  const [activeTab, setActiveTab] = useAtom(activeTabAtom)

  return useMemo(() => ({
    activeTab,
    setActiveTab,
    isTabActive: (tabId: string) => activeTab === tabId
  }), [activeTab, setActiveTab])
}

/**
 * Hook for managing settings modal
 */
export function useSettingsModal() {
  const [showSettings, setShowSettings] = useAtom(showSettingsAtom)

  return {
    showSettings,
    setShowSettings,
    openSettings: useCallback(() => setShowSettings(true), [setShowSettings]),
    closeSettings: useCallback(() => setShowSettings(false), [setShowSettings])
  }
}

/**
 * Hook for resetting application state
 */
export function useAppReset() {
  const setIsMinimized = useSetAtom(isMinimizedAtom)
  const setActiveTab = useSetAtom(activeTabAtom)
  const setShowSettings = useSetAtom(showSettingsAtom)

  return {
    resetApp: useCallback(() => {
      setIsMinimized(false)
      setActiveTab('chat')
      setShowSettings(false)
      console.log('🔄 UI: Reset application UI state')
    }, [setIsMinimized, setActiveTab, setShowSettings])
  }
}
