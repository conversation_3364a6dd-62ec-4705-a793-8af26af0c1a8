// Unified AI API Hook
// Provides a unified interface for both OpenRouter and DeepSeek APIs

import { useCallback } from 'react'
import { useAsyncFn } from 'react-use'
import { useAiVendor, useOpenRouterConfig, useDeepSeekConfig, useAiConversation, useAiState, useChatMessages, useAiProgress } from '../store'
import { getOpenRouterClient } from '../../lib/openrouter'
import { getDeepSeekClient } from '../../lib/deepseek'
import { Message, AiMessage, AiToolCall, ToolExecutionResult, AiResponse } from '../types/ui_types'

// Helper functions for formatting tool results
function getToolResultFormat(toolName: string): string {
  const formatMap: Record<string, string> = {
    'get_figma_selection_json': 'json',
    'get_figma_selection_css': 'css',
    'get_figma_selection_tailwind_html': 'html'
  }
  return formatMap[toolName] || 'text'
}

function formatToolResult(data: unknown, toolName: string): string {
  if (typeof data === 'string') {
    return data
  }

  if (toolName === 'get_figma_selection_json') {
    return JSON.stringify(data, null, 2)
  }

  return String(data)
}





export function useUnifiedAiApi() {
  const { vendor } = useAiVendor()
  const { config: openRouterConfig } = useOpenRouterConfig()
  const { config: deepSeekConfig } = useDeepSeekConfig()
  const { conversation, addMessage: addConversationMessage, clearConversation } = useAiConversation()
  const { loading, setLoading, error, setError, clearError, streaming, setStreaming, setAbortController, abortStreaming } = useAiState()
  const { setMessages } = useChatMessages()
  const { completeAiProgress } = useAiProgress()


  // Get the appropriate client based on current vendor
  const getClient = useCallback(() => {
    if (vendor === 'deepseek') {
      return getDeepSeekClient(deepSeekConfig)
    } else {
      return getOpenRouterClient(openRouterConfig)
    }
  }, [vendor, openRouterConfig, deepSeekConfig])

  // Get current config based on vendor
  const getCurrentConfig = useCallback(() => {
    return vendor === 'deepseek' ? deepSeekConfig : openRouterConfig
  }, [vendor, openRouterConfig, deepSeekConfig])

  // Send message (non-streaming)
  const sendMessage = useCallback(async (userMessage: string) => {
    const config = getCurrentConfig()
    if (!config.apiKey) {
      throw new Error(`${vendor === 'deepseek' ? 'DeepSeek' : 'OpenRouter'} API key is required`)
    }

    setLoading(true)
    clearError()

    try {
      const client = getClient()
      const response = await client.sendMessage(userMessage, conversation)

      // Add user message to conversation
      const userAiMessage: AiMessage = {
        role: 'user',
        content: userMessage
      }
      addConversationMessage(userAiMessage)

      // Add AI response to conversation
      const aiContent = response.choices[0]?.message?.content || 'No response'
      const aiAiMessage: AiMessage = {
        role: 'assistant',
        content: aiContent
      }
      addConversationMessage(aiAiMessage)

      return response
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [vendor, getCurrentConfig, getClient, conversation, addConversationMessage, setLoading, clearError, setError])

  // Send multi-turn message (non-streaming)
  const sendMultiTurnMessage = useCallback(async (
    userMessage: string,
    maxTurns: number = 5,
    onToolCall?: (toolCall: AiToolCall, result: ToolExecutionResult) => void,
    onTurnComplete?: (turn: number, response: AiResponse) => void
  ) => {
    console.log(`🔄 [UnifiedAI] Starting multi-turn conversation with ${vendor}: "${userMessage}"`)

    const config = getCurrentConfig()
    if (!config.apiKey) {
      throw new Error(`${vendor === 'deepseek' ? 'DeepSeek' : 'OpenRouter'} API key is required`)
    }

    setLoading(true)
    clearError()

    try {
      const client = getClient()

      // Check if client supports multi-turn messaging
      if (!('sendMultiTurnMessage' in client)) {
        throw new Error(`Multi-turn messaging not supported by ${vendor} client`)
      }

      const response = await (client as any).sendMultiTurnMessage(
        userMessage,
        conversation,
        maxTurns,
        onToolCall,
        onTurnComplete
      )

      // Add user message to conversation (if not already added by multi-turn logic)
      const hasUserMessage = conversation.some(msg =>
        msg.role === 'user' && msg.content === userMessage
      )
      if (!hasUserMessage) {
        const userAiMessage: AiMessage = {
          role: 'user',
          content: userMessage
        }
        addConversationMessage(userAiMessage)
      }

      // Add final AI response to conversation
      const aiContent = response.choices[0]?.message?.content || 'No response'
      const aiAiMessage: AiMessage = {
        role: 'assistant',
        content: aiContent
      }
      addConversationMessage(aiAiMessage)

      return response
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [vendor, getCurrentConfig, getClient, conversation, addConversationMessage, setLoading, clearError, setError])

  // Send multi-turn streaming message with real-time character-by-character streaming
  const sendMultiTurnStreamingMessage = useCallback(async (userMessage: string, maxTurns: number = 5) => {
    console.log(`🔄 [UnifiedAI] Starting multi-turn streaming conversation with ${vendor}: "${userMessage}"`)

    const config = getCurrentConfig()
    if (!config.apiKey) {
      throw new Error(`${vendor === 'deepseek' ? 'DeepSeek' : 'OpenRouter'} API key is required`)
    }

    setLoading(true)
    setStreaming(true)
    clearError()

    // Create abort controller
    const controller = new AbortController()
    setAbortController(controller)

    // Add user message to conversation
    const userAiMessage: AiMessage = {
      role: 'user',
      content: userMessage
    }
    addConversationMessage(userAiMessage)

    // Add user message to chat
    const userChatMessage: Message = {
      id: Date.now().toString() + Math.random(),
      text: userMessage,
      timestamp: new Date(),
      type: 'user'
    }
    setMessages(prev => [...prev, userChatMessage])

    // Create AI message placeholder for streaming
    const aiMessageId = Date.now().toString() + Math.random() + '_ai'
    const aiChatMessage: Message = {
      id: aiMessageId,
      text: '',
      timestamp: new Date(),
      type: 'ai',
      model: config.model,
      vendor: vendor,
      isStreaming: true
    }
    setMessages(prev => [...prev, aiChatMessage])

    // Track accumulated content for real-time streaming
    let aiContent = ''
    let lastUpdateTime = 0
    const UPDATE_THROTTLE_MS = 50 // Update UI every 50ms for smooth streaming

    // Track tool execution results for display
    let toolResults = ''

    try {
      const client = getClient()

      // Check if client supports multi-turn streaming messaging
      if (!('sendMultiTurnStreamingMessage' in client)) {
        throw new Error(`Multi-turn streaming messaging not supported by ${vendor} client`)
      }

      // Use the new multi-turn streaming API which supports proper tool calling flow
      await (client as any).sendMultiTurnStreamingMessage(
        userMessage,
        conversation,
        maxTurns,
        // onChunk - real-time character-by-character streaming
        (chunk: string) => {
          aiContent += chunk
          const now = Date.now()

          // Throttle updates to ensure visible character-by-character streaming
          if (now - lastUpdateTime >= UPDATE_THROTTLE_MS) {
            lastUpdateTime = now
            // Use requestAnimationFrame to ensure smooth UI updates during streaming
            requestAnimationFrame(() => {
              const fullText = toolResults + (toolResults && aiContent ? '\n\n---\n\n' : '') + aiContent
              setMessages(prev => prev.map(msg =>
                msg.id === aiMessageId
                  ? { ...msg, text: fullText, model: config.model, vendor: vendor, isStreaming: true }
                  : msg
              ))
            })
          }
        },
        // onComplete
        () => {
          // Ensure final content is displayed and mark as complete
          const finalText = toolResults + (toolResults && aiContent ? '\n\n---\n\n' : '') + aiContent
          setMessages(prev => prev.map(msg =>
            msg.id === aiMessageId
              ? {
                  ...msg,
                  text: finalText,
                  isStreaming: false,
                  model: config.model,
                  vendor: vendor,
                  // Reset loading state to hide progress indicators
                  loadingState: msg.loadingState ? {
                    ...msg.loadingState,
                    isActive: false,
                    phase: 'completing' as const
                  } : undefined
                }
              : msg
          ))

          // Add final AI response to conversation (if there was content beyond tool calls)
          if (aiContent.trim()) {
            const aiAiMessage: AiMessage = {
              role: 'assistant',
              content: aiContent
            }
            addConversationMessage(aiAiMessage)
          }

          // Reset all loading and progress states
          setLoading(false)
          setStreaming(false)
          setAbortController(null)

          // Reset global progress indicator
          completeAiProgress()

          // Additional safety: ensure all AI messages have proper final state
          setTimeout(() => {
            setMessages(prev => prev.map(msg =>
              msg.type === 'ai' && (msg.isStreaming || msg.loadingState?.isActive)
                ? {
                    ...msg,
                    isStreaming: false,
                    loadingState: msg.loadingState ? {
                      ...msg.loadingState,
                      isActive: false,
                      phase: 'completing' as const
                    } : undefined
                  }
                : msg
            ))
          }, 100) // Small delay to ensure all updates are processed
        },
        // onError
        (err: Error) => {
          const errorMessage = err.message
          setError(errorMessage)

          // Remove streaming message and add error
          setMessages(prev => prev.filter(msg => msg.id !== aiMessageId))

          // Provide detailed error information for debugging
          let displayMessage = `❌ AI Error: ${errorMessage}`
          if (vendor === 'deepseek' && errorMessage.includes('422')) {
            displayMessage = `❌ DeepSeek API Error (422): ${errorMessage}\n\nThis may be due to:\n• Invalid tool calling configuration\n• Incorrect message ordering\n• Unsupported request format\n\nCheck browser console for detailed analysis.`
          }

          const errorChatMessage: Message = {
            id: Date.now().toString() + Math.random(),
            text: displayMessage,
            timestamp: new Date(),
            type: 'system'
          }
          setMessages(prev => [...prev, errorChatMessage])
          setLoading(false)
          setStreaming(false)
          setAbortController(null)

          // Reset global progress indicator on error
          completeAiProgress()
        },
        // abortController
        controller,
        // onToolCall - handle tool execution and display results
        (toolCall: AiToolCall, result: ToolExecutionResult) => {
          console.log(`🔧 [UnifiedAI] Tool call executed (${vendor}):`, toolCall.function.name, result)

          // Add tool execution result to display
          const toolCallText = `🔧 Executing **${toolCall.function.name}**...`
          const toolResultText = result.success
            ? `\`\`\`${getToolResultFormat(toolCall.function.name)}\n${formatToolResult(result.data, toolCall.function.name)}\n\`\`\``
            : `❌ Error: ${result.error}`

          toolResults += (toolResults ? '\n\n' : '') + toolCallText + '\n\n' + toolResultText

          // Update message with tool execution results immediately
          const currentText = toolResults + (aiContent ? '\n\n---\n\n' + aiContent : '')
          setMessages(prev => prev.map(msg =>
            msg.id === aiMessageId
              ? { ...msg, text: currentText, model: config.model, vendor: vendor, isStreaming: true }
              : msg
          ))
        },
        // onTurnComplete - handle turn completion notifications
        (turn: number, hasToolCalls: boolean) => {
          console.log(`🔄 [UnifiedAI] Turn ${turn} completed, hasToolCalls: ${hasToolCalls}`)
          // Could add turn-specific UI feedback here if needed
        }
      )

    } catch (err) {
      if (!controller.signal.aborted) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error'
        setError(errorMessage)

        // Remove streaming message and add error
        setMessages(prev => prev.filter(msg => msg.id !== aiMessageId))

        const errorChatMessage: Message = {
          id: Date.now().toString() + Math.random(),
          text: `❌ Multi-turn AI Error: ${errorMessage}`,
          timestamp: new Date(),
          type: 'system'
        }
        setMessages(prev => [...prev, errorChatMessage])
        setLoading(false)
        setStreaming(false)
        setAbortController(null)

        // Reset global progress indicator on error
        completeAiProgress()
      } else {
        // Request was aborted - preserve partial content and mark as interrupted
        console.log('🛑 [UnifiedAI] Multi-turn streaming aborted - preserving partial content')
        setMessages(prev => prev.map(msg =>
          msg.id === aiMessageId
            ? { ...msg, isStreaming: false, isInterrupted: true }
            : msg
        ))
        setLoading(false)
        setStreaming(false)
        setAbortController(null)
        completeAiProgress()
      }
    }
  }, [vendor, getCurrentConfig, getClient, conversation, addConversationMessage, setLoading, setStreaming, clearError, setError, setAbortController, setMessages, completeAiProgress])

  // Send streaming message
  const sendStreamingMessage = useCallback(async (userMessage: string) => {
    const config = getCurrentConfig()
    if (!config.apiKey) {
      throw new Error(`${vendor === 'deepseek' ? 'DeepSeek' : 'OpenRouter'} API key is required`)
    }

    setLoading(true)
    setStreaming(true)
    clearError()

    // Create abort controller
    const controller = new AbortController()
    setAbortController(controller)

    // Add user message to conversation
    const userAiMessage: AiMessage = {
      role: 'user',
      content: userMessage
    }
    addConversationMessage(userAiMessage)

    // Add user message to chat
    const userChatMessage: Message = {
      id: Date.now().toString() + Math.random(),
      text: userMessage,
      timestamp: new Date(),
      type: 'user'
    }
    setMessages(prev => [...prev, userChatMessage])

    // Create AI message placeholder
    const aiMessageId = Date.now().toString() + Math.random() + '_ai'
    const aiChatMessage: Message = {
      id: aiMessageId,
      text: '',
      timestamp: new Date(),
      type: 'ai',
      model: config.model,
      vendor: vendor,
      isStreaming: true
    }
    setMessages(prev => [...prev, aiChatMessage])

    let aiContent = ''
    let lastUpdateTime = 0
    const UPDATE_THROTTLE_MS = 50 // Update UI every 50ms for smoother streaming

    try {
      const client = getClient()

      // Send streaming request
      await client.sendStreamingMessage(
        userMessage,
        conversation,
        // onChunk - optimized for smooth streaming with throttling
        (chunk: string) => {
          aiContent += chunk
          const now = Date.now()

          // Throttle updates to ensure visible character-by-character streaming
          if (now - lastUpdateTime >= UPDATE_THROTTLE_MS) {
            lastUpdateTime = now
            // Use requestAnimationFrame to ensure smooth UI updates during streaming
            requestAnimationFrame(() => {
              setMessages(prev => prev.map(msg =>
                msg.id === aiMessageId
                  ? { ...msg, text: aiContent, model: config.model, vendor: vendor, isStreaming: true }
                  : msg
              ))
            })
          }
        },
        // onComplete
        () => {
          // Ensure final content is displayed and mark as complete
          setMessages(prev => prev.map(msg =>
            msg.id === aiMessageId
              ? {
                  ...msg,
                  text: aiContent,
                  isStreaming: false,
                  model: config.model,
                  vendor: vendor,
                  // Reset loading state to hide progress indicators
                  loadingState: msg.loadingState ? {
                    ...msg.loadingState,
                    isActive: false,
                    phase: 'completing' as const
                  } : undefined
                }
              : msg
          ))

          // Add to conversation
          const aiAiMessage: AiMessage = {
            role: 'assistant',
            content: aiContent
          }
          addConversationMessage(aiAiMessage)

          // Reset all loading and progress states
          setLoading(false)
          setStreaming(false)
          setAbortController(null)

          // Reset global progress indicator
          completeAiProgress()

          // Additional safety: ensure all AI messages have proper final state
          setTimeout(() => {
            setMessages(prev => prev.map(msg =>
              msg.type === 'ai' && (msg.isStreaming || msg.loadingState?.isActive)
                ? {
                    ...msg,
                    isStreaming: false,
                    loadingState: msg.loadingState ? {
                      ...msg.loadingState,
                      isActive: false,
                      phase: 'completing' as const
                    } : undefined
                  }
                : msg
            ))
          }, 100) // Small delay to ensure all updates are processed
        },
        // onError
        (err: Error) => {
          const errorMessage = err.message
          setError(errorMessage)

          // Remove streaming message and add error
          setMessages(prev => prev.filter(msg => msg.id !== aiMessageId))

          const errorChatMessage: Message = {
            id: Date.now().toString() + Math.random(),
            text: `❌ AI Error: ${errorMessage}`,
            timestamp: new Date(),
            type: 'system'
          }
          setMessages(prev => [...prev, errorChatMessage])
          setLoading(false)
          setStreaming(false)
          setAbortController(null)

          // Reset global progress indicator on error
          completeAiProgress()
        },
        // abortController
        controller,
        // onToolCall
        (toolCall: AiToolCall, result: ToolExecutionResult) => {
          console.log(`🔧 [UnifiedAI] Tool call executed (${vendor}):`, toolCall.function.name, result)

          // For single-turn streaming, we'll integrate tool results into the AI response
          // The tool results are already added to the conversation context
          // and will be reflected in the AI's next response
        }
      )
    } catch (err) {
      if (!controller.signal.aborted) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error'
        setError(errorMessage)

        // Remove streaming message and add error
        setMessages(prev => prev.filter(msg => msg.id !== aiMessageId))

        const errorChatMessage: Message = {
          id: Date.now().toString() + Math.random(),
          text: `❌ AI Error: ${errorMessage}`,
          timestamp: new Date(),
          type: 'system'
        }
        setMessages(prev => [...prev, errorChatMessage])
        setLoading(false)
        setStreaming(false)
        setAbortController(null)

        // Reset global progress indicator on error
        completeAiProgress()
      } else {
        // Request was aborted - preserve partial content and mark as interrupted
        console.log('🛑 [UnifiedAI] Single-turn streaming aborted - preserving partial content')
        setMessages(prev => prev.map(msg =>
          msg.id === aiMessageId
            ? { ...msg, isStreaming: false, isInterrupted: true }
            : msg
        ))
        setLoading(false)
        setStreaming(false)
        setAbortController(null)
        completeAiProgress()
      }
    }
  }, [vendor, getCurrentConfig, getClient, conversation, addConversationMessage, setLoading, setStreaming, clearError, setError, setAbortController, setMessages, completeAiProgress])

  // Test API connection using useAsyncFn
  const [testConnectionState, testConnection] = useAsyncFn(async () => {
    const config = getCurrentConfig()
    if (!config.apiKey) {
      throw new Error(`${vendor === 'deepseek' ? 'DeepSeek' : 'OpenRouter'} API key is required`)
    }

    clearError()

    try {
      const client = getClient()
      const result = await client.testConnection()

      if (!result.success) {
        setError(result.error || 'Connection test failed')
        return false
      }

      return true
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Connection test failed'
      setError(errorMessage)
      return false
    }
  }, [vendor, getCurrentConfig, getClient, clearError, setError])

  // Clear conversation history
  const clearHistory = useCallback(() => {
    clearConversation()
  }, [clearConversation])

  return {
    vendor,
    sendMessage,
    sendMultiTurnMessage,
    sendMultiTurnStreamingMessage,
    sendStreamingMessage,
    testConnection,
    testConnectionLoading: testConnectionState.loading,
    testConnectionError: testConnectionState.error,
    clearHistory,
    loading,
    error,
    clearError,
    conversation,
    config: getCurrentConfig(),
    streaming,
    abortStreaming
  }
}
