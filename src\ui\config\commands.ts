// Command system configuration

/**
 * Global command prefix configuration
 * Change this value to customize the command prefix
 * Examples:
 * - '/' for slash commands (current default)
 * - '@' for at-mention style commands
 * - '!' for exclamation commands
 * - '$' for dollar commands
 */
export const COMMAND_PREFIX = '/'

/**
 * Command configuration options
 */
export const COMMAND_CONFIG = {
  // Command prefix character
  prefix: COMMAND_PREFIX,
  
  // Whether to show suggestions automatically
  autoSuggestions: true,
  
  // Maximum number of suggestions to show
  maxSuggestions: 6,
  
  // Minimum characters after prefix to trigger suggestions
  minQueryLength: 0,
  
  // Case sensitive command matching
  caseSensitive: false
} as const

/**
 * Available command categories for organization
 */
export const COMMAND_CATEGORIES = {
  SELECTION: 'selection',
  UTILITY: 'utility',
  HELP: 'help'
} as const

export type CommandCategory = typeof COMMAND_CATEGORIES[keyof typeof COMMAND_CATEGORIES]
