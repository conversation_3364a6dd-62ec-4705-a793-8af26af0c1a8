// Minimized view component for the plugin

import React from 'react'

interface MinimizedViewProps {
  onExpand: () => void
}

export const MinimizedView: React.FC<MinimizedViewProps> = ({
  onExpand
}) => {
  return (
    <div className="w-full h-full flex justify-end items-start p-1">
      <div className="flex items-center gap-2">
        {/* Expand button */}
        <button
          onClick={onExpand}
          className="w-8 h-8 bg-white/80 hover:bg-white text-black hover:text-gray-800 rounded flex items-center justify-center transition-all duration-200 shadow-sm border border-gray-200 hover:border-gray-300"
          title="Expand Plugin"
        >
          <span className="text-lg font-bold leading-none">+</span>
        </button>
      </div>
    </div>
  )
}
