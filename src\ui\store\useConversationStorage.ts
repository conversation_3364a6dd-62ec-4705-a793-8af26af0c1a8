// Conversation Storage Hook
// Manages persistence of conversation data to Figma clientStorage

import { useCallback, useEffect } from 'react'
import { useMount, useUpdateEffect } from 'react-use'
import { useConversationManager } from './useConversationHooks'
import { getFigmaClient, STORAGE_KEYS } from '../../lib/figma'
import { Conversation, ConversationSummary } from '../types/ui_types'
import { useSetAtom } from 'jotai'
import { conversationActionsAtom } from './useConversationHooks'
import { messagesAtom } from './useChatHooks'
import { aiConversationAtom } from './useAiHooks'

export function useConversationStorage() {
  const {
    conversations,
    currentConversationId,
    currentConversation,
    setConversations,
    setCurrentConversationId,
    setCurrentConversation
  } = useConversationManager()

  const executeConversationAction = useSetAtom(conversationActionsAtom)
  const setMessages = useSetAtom(messagesAtom)
  const setAiConversation = useSetAtom(aiConversationAtom)

  // Load conversations from storage
  const loadConversationsFromStorage = useCallback(async () => {
    try {
      const figmaClient = getFigmaClient()
      const storedConversations = await figmaClient.storage.getItem(STORAGE_KEYS.CONVERSATIONS, null)

      if (storedConversations) {
        const parsedConversations = typeof storedConversations === 'string'
          ? JSON.parse(storedConversations)
          : storedConversations

        setConversations(parsedConversations)
        console.log('🔄 Storage: Loaded conversations from storage:', parsedConversations.length, 'conversations')
      }
    } catch (error) {
      console.error('❌ Storage: Failed to load conversations from storage:', error)
    }
  }, [setConversations])

  // Save conversations to storage
  const saveConversationsToStorage = useCallback(async (conversations: ConversationSummary[]) => {
    try {
      const figmaClient = getFigmaClient()
      await figmaClient.storage.setItem(STORAGE_KEYS.CONVERSATIONS, conversations)
      console.log('💾 Storage: Saved conversations to storage:', conversations.length, 'conversations')
    } catch (error) {
      console.error('❌ Storage: Failed to save conversations to storage:', error)
    }
  }, [])

  // Load current conversation ID from storage
  const loadCurrentConversationIdFromStorage = useCallback(async () => {
    try {
      const figmaClient = getFigmaClient()
      const storedId = await figmaClient.storage.getItem(STORAGE_KEYS.CURRENT_CONVERSATION_ID, null)

      if (storedId) {
        setCurrentConversationId(storedId)
        console.log('🔄 Storage: Loaded current conversation ID from storage:', storedId)
      }
    } catch (error) {
      console.error('❌ Storage: Failed to load current conversation ID from storage:', error)
    }
  }, [setCurrentConversationId])

  // Save current conversation ID to storage
  const saveCurrentConversationIdToStorage = useCallback(async (id: string | null) => {
    try {
      const figmaClient = getFigmaClient()
      if (id) {
        await figmaClient.storage.setItem(STORAGE_KEYS.CURRENT_CONVERSATION_ID, id)
        console.log('💾 Storage: Saved current conversation ID to storage:', id)
      } else {
        await figmaClient.storage.removeItem(STORAGE_KEYS.CURRENT_CONVERSATION_ID)
        console.log('💾 Storage: Cleared current conversation ID from storage')
      }
    } catch (error) {
      console.error('❌ Storage: Failed to save current conversation ID to storage:', error)
    }
  }, [])

  // Load current conversation from storage
  const loadCurrentConversationFromStorage = useCallback(async (id: string) => {
    try {
      const figmaClient = getFigmaClient()
      const storageKey = `${STORAGE_KEYS.CONVERSATION_PREFIX}${id}`
      const storedConversation = await figmaClient.storage.getItem(storageKey, null)

      if (storedConversation) {
        const parsedConversation = typeof storedConversation === 'string'
          ? JSON.parse(storedConversation)
          : storedConversation

        setCurrentConversation(parsedConversation)

        // Restore messages and AI conversation when loading a conversation
        if (parsedConversation.messages && parsedConversation.messages.length > 0) {
          setMessages(parsedConversation.messages)
          console.log('🔄 Storage: Restored', parsedConversation.messages.length, 'messages from conversation:', id)
        } else {
          setMessages([])
          console.log('🔄 Storage: No messages found in conversation, clearing messages:', id)
        }

        if (parsedConversation.aiMessages && parsedConversation.aiMessages.length > 0) {
          setAiConversation(parsedConversation.aiMessages)
          console.log('🔄 Storage: Restored', parsedConversation.aiMessages.length, 'AI messages from conversation:', id)
        } else {
          setAiConversation([])
          console.log('🔄 Storage: No AI messages found in conversation, clearing AI messages:', id)
        }

        console.log('🔄 Storage: Successfully loaded conversation from storage:', id, {
          title: parsedConversation.title,
          messageCount: parsedConversation.messages?.length || 0,
          aiMessageCount: parsedConversation.aiMessages?.length || 0
        })
      }
    } catch (error) {
      console.error('❌ Storage: Failed to load current conversation from storage:', error)
    }
  }, [setCurrentConversation])

  // Save current conversation to storage
  const saveCurrentConversationToStorage = useCallback(async (conversation: Conversation) => {
    try {
      const figmaClient = getFigmaClient()
      const storageKey = `${STORAGE_KEYS.CONVERSATION_PREFIX}${conversation.id}`
      await figmaClient.storage.setItem(storageKey, conversation)
      console.log('💾 Storage: Saved current conversation to storage:', conversation.id)
    } catch (error) {
      console.error('❌ Storage: Failed to save current conversation to storage:', error)
    }
  }, [])

  // Auto-save conversations when they change
  useUpdateEffect(() => {
    if (conversations.length > 0) {
      saveConversationsToStorage(conversations)
    }
  }, [conversations, saveConversationsToStorage])

  // Auto-save current conversation ID when it changes
  useUpdateEffect(() => {
    saveCurrentConversationIdToStorage(currentConversationId)
  }, [currentConversationId, saveCurrentConversationIdToStorage])

  // Auto-save current conversation when it changes
  useUpdateEffect(() => {
    if (currentConversation) {
      saveCurrentConversationToStorage(currentConversation)
    }
  }, [currentConversation, saveCurrentConversationToStorage])

  // Load current conversation when ID changes
  useEffect(() => {
    if (currentConversationId) {
      console.log('🔄 [ConversationStorage] Loading conversation:', currentConversationId)
      loadCurrentConversationFromStorage(currentConversationId)
    } else {
      console.log('🔄 [ConversationStorage] Clearing conversation and messages')
      setCurrentConversation(null)
      // Clear messages when switching away from a conversation
      setMessages([])
      setAiConversation([])
    }
  }, [currentConversationId, loadCurrentConversationFromStorage, setCurrentConversation, setMessages, setAiConversation])

  // Load on mount
  useMount(() => {
    loadConversationsFromStorage()
    loadCurrentConversationIdFromStorage()
  })

  return {
    conversations,
    currentConversationId,
    currentConversation,
    setConversations,
    setCurrentConversationId,
    setCurrentConversation,
    executeConversationAction,
    reload: useCallback(() => {
      loadConversationsFromStorage()
      loadCurrentConversationIdFromStorage()
    }, [loadConversationsFromStorage, loadCurrentConversationIdFromStorage])
  }
}
