// Test file to verify ChatInterface virtualization works correctly

import React, { useState, useEffect } from 'react'
import { ChatInterface } from './ChatInterface'
import { Message } from '../types/ui_types'
import VirtualizationDebug from './VirtualizationDebug'

// Mock data for testing
const createMockMessage = (id: string, type: 'user' | 'ai' | 'system', text: string): Message => ({
  id,
  type,
  text,
  timestamp: new Date(),
})

const createVariedMessages = (count: number): Message[] => {
  const messages: Message[] = []
  const messageTypes = ['user', 'ai', 'system'] as const

  // Create a large code block for testing virtualization
  const largeCodeBlock = `\`\`\`javascript
// This is a large code block to test virtualization
function largeFunction() {
  console.log("Line 1");
  console.log("Line 2");
  console.log("Line 3");
  console.log("Line 4");
  console.log("Line 5");
  console.log("Line 6");
  console.log("Line 7");
  console.log("Line 8");
  console.log("Line 9");
  console.log("Line 10");
  console.log("Line 11");
  console.log("Line 12");
  console.log("Line 13");
  console.log("Line 14");
  console.log("Line 15");
  console.log("Line 16");
  console.log("Line 17");
  console.log("Line 18");
  console.log("Line 19");
  console.log("Line 20");
  console.log("Line 21");
  console.log("Line 22");
  console.log("Line 23");
  console.log("Line 24");
  console.log("Line 25");
  console.log("Line 26");
  console.log("Line 27");
  console.log("Line 28");
  console.log("Line 29");
  console.log("Line 30");
  console.log("Line 31");
  console.log("Line 32");
  console.log("Line 33");
  console.log("Line 34");
  console.log("Line 35");
  console.log("Line 36");
  console.log("Line 37");
  console.log("Line 38");
  console.log("Line 39");
  console.log("Line 40");
  console.log("Line 41");
  console.log("Line 42");
  console.log("Line 43");
  console.log("Line 44");
  console.log("Line 45");
  console.log("Line 46");
  console.log("Line 47");
  console.log("Line 48");
  console.log("Line 49");
  console.log("Line 50");
  console.log("Line 51");
  console.log("Line 52");
  console.log("Line 53");
  console.log("Line 54");
  console.log("Line 55");
  console.log("Line 56");
  console.log("Line 57");
  console.log("Line 58");
  console.log("Line 59");
  console.log("Line 60");
  console.log("Line 61");
  console.log("Line 62");
  console.log("Line 63");
  console.log("Line 64");
  console.log("Line 65");
  console.log("Line 66");
  console.log("Line 67");
  console.log("Line 68");
  console.log("Line 69");
  console.log("Line 70");
  return "This is a large function for testing code block virtualization";
}
\`\`\`

This message contains a large code block (>50 lines) that should trigger virtualization.`

  const sampleTexts = [
    'Short message.',
    'This is a medium length message that spans multiple lines and contains some more detailed content to test the height estimation.',
    `This is a message with a small code block:

\`\`\`javascript
function example() {
  console.log("Hello world");
  return true;
}
\`\`\`

And some more text after the code block. This helps test the height estimation for complex messages.`,
    '# Heading\n\nThis message has **markdown** formatting:\n\n- List item 1\n- List item 2\n- List item 3\n\n> This is a blockquote\n\nAnd some `inline code` as well.',
    'Simple user response',
    'AI response with multiple paragraphs.\n\nThis is the second paragraph with more content.\n\nAnd a third paragraph to make it longer.',
    largeCodeBlock, // Large code block for testing virtualization
  ]

  for (let i = 0; i < count; i++) {
    const type = messageTypes[i % 3]
    const text = sampleTexts[i % sampleTexts.length]

    // Add loading state to some AI messages to test timeout functionality
    const loadingState = type === 'ai' && i % 7 === 0 ? {
      isActive: true,
      phase: 'tool_execution' as const,
      statusMessage: 'Executing tools...',
      currentTool: 'test-tool',
      toolsExecuted: 1,
      startTime: new Date(),
      timeoutMs: 5000, // Short timeout for testing
      isTimedOut: false,
    } : undefined

    const message = createMockMessage(`msg-${i}`, type, text)
    if (loadingState) {
      message.loadingState = loadingState
    }
    messages.push(message)
  }
  return messages
}

// Generate large Figma selection JSON for performance testing
const createLargeFigmaJson = (): string => {
  const selection = {
    selection: Array.from({ length: 100 }, (_, i) => ({
      id: `node_${i}`,
      name: `Component ${i}`,
      type: i % 3 === 0 ? 'FRAME' : i % 3 === 1 ? 'TEXT' : 'RECTANGLE',
      visible: true,
      locked: false,
      x: Math.random() * 1000,
      y: Math.random() * 1000,
      width: 100 + Math.random() * 200,
      height: 50 + Math.random() * 150,
      rotation: 0,
      opacity: 1,
      fills: [{
        type: 'SOLID',
        color: { r: Math.random(), g: Math.random(), b: Math.random() },
        opacity: 1
      }],
      children: i % 5 === 0 ? Array.from({ length: 10 }, (_, j) => ({
        id: `child_${i}_${j}`,
        name: `Child ${j}`,
        type: 'TEXT',
        characters: `Sample text content for child ${j}`,
        fontSize: 12 + Math.random() * 12,
        fontName: { family: 'Inter', style: 'Regular' },
        fills: [{ type: 'SOLID', color: { r: 0, g: 0, b: 0 }, opacity: 1 }],
        x: Math.random() * 100,
        y: Math.random() * 100,
        width: 50 + Math.random() * 100,
        height: 20 + Math.random() * 30,
        visible: true,
        locked: false,
        rotation: 0,
        opacity: 1
      })) : undefined,
      // Add many properties to make JSON large
      customProperties: {
        metadata: Array.from({ length: 20 }, (_, k) => ({
          key: `property_${k}`,
          value: `This is a long property value that makes the JSON larger ${k}`.repeat(5),
          nested: {
            level1: {
              level2: {
                level3: `Deep nested value ${k}`.repeat(3)
              }
            }
          }
        }))
      }
    })),
    timestamp: new Date().toISOString(),
    metadata: {
      totalNodes: 100,
      figmaVersion: '1.0.0',
      exportSettings: {
        format: 'JSON',
        includeChildren: true,
        includeMetadata: true
      }
    }
  }

  return `\`\`\`json\n${JSON.stringify(selection, null, 2)}\n\`\`\``
}

// Test component to verify virtualization and auto-scroll
export const ChatInterfaceTest: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>(createVariedMessages(20))
  const [messageCount, setMessageCount] = useState(20)

  // Add new messages periodically to test auto-scroll
  useEffect(() => {
    const interval = setInterval(() => {
      const newMessage = createMockMessage(
        `msg-${messageCount}`,
        'ai',
        `Auto-generated message ${messageCount + 1}. This tests the auto-scroll functionality.`
      )
      setMessages(prev => [...prev, newMessage])
      setMessageCount(prev => prev + 1)
    }, 3000)

    return () => clearInterval(interval)
  }, [messageCount])

  const mockProps = {
    messages,
    copiedMessageId: null,
    onCopyMessage: (text: string, messageId: string) => {
      console.log('Copy message:', messageId, text.substring(0, 50))
    },
    selectedMessageIds: new Set<string>(),
    isSelectionMode: false,
    onToggleSelection: (messageId: string) => {
      console.log('Toggle selection:', messageId)
    },
    onSelectAll: () => {
      console.log('Select all')
    },
    onClearSelection: () => {
      console.log('Clear selection')
    },
    onDeleteSelected: () => {
      console.log('Delete selected')
    },
    onCopySelected: () => {
      console.log('Copy selected')
    },
  }

  const addTestMessage = () => {
    const newMessage = createMockMessage(
      `manual-${Date.now()}`,
      'user',
      'Manually added test message to verify auto-scroll behavior.'
    )
    setMessages(prev => [...prev, newMessage])
  }

  const addLargeJsonMessage = () => {
    const largeJson = createLargeFigmaJson()
    const newMessage = createMockMessage(
      `json-${Date.now()}`,
      'system',
      largeJson
    )
    setMessages(prev => [...prev, newMessage])
  }

  const addMultipleJsonMessages = () => {
    const jsonMessages = Array.from({ length: 5 }, (_, i) => {
      const largeJson = createLargeFigmaJson()
      return createMockMessage(
        `bulk-json-${Date.now()}-${i}`,
        'system',
        largeJson
      )
    })
    setMessages(prev => [...prev, ...jsonMessages])
  }

  return (
    <div style={{ height: '700px', width: '900px', border: '1px solid #ccc', padding: '10px', position: 'relative' }}>
      <div style={{ marginBottom: '10px' }}>
        <h2>ChatInterface Virtualization Test</h2>
        <p>
          Testing virtualization with {messages.length} messages.
          New messages are added every 3 seconds to test auto-scroll.
        </p>
        <button onClick={addTestMessage} style={{ marginRight: '10px', padding: '5px 10px' }}>
          Add Test Message
        </button>
        <button onClick={addLargeJsonMessage} style={{ marginRight: '10px', padding: '5px 10px', background: '#f59e0b', color: 'white', border: 'none', borderRadius: '4px' }}>
          Add Large JSON
        </button>
        <button onClick={addMultipleJsonMessages} style={{ marginRight: '10px', padding: '5px 10px', background: '#ef4444', color: 'white', border: 'none', borderRadius: '4px' }}>
          Add 5 Large JSONs
        </button>
        <span>Messages: {messages.length}</span>
      </div>
      <div style={{ height: '600px' }}>
        <ChatInterface {...mockProps} />
      </div>
      <VirtualizationDebug totalMessages={messages.length} />
    </div>
  )
}

export default ChatInterfaceTest
