import { parseFigmaNodes } from './parser'

// Handle GET_FIGMA_SELECTION message
export async function handleGetFigmaSelection(requestId?: string): Promise<void> {
  console.log('🎯 [Plugin Handler] Starting getFigmaSelection with requestId:', requestId)

  try {
    const selection = figma.currentPage.selection
    console.log('📋 [Plugin Handler] Current selection:', {
      count: selection.length,
      types: selection.map(node => node.type),
      requestId
    })

    if (selection.length === 0) {
      console.log('❌ [Plugin Handler] No selection found, sending error response')
      const errorResponse = {
        type: 'figma_selection_response',
        data: { selection: null, message: 'No objects selected' },
        request_id: requestId,
        success: false,
        error: 'No objects selected'
      }
      console.log('📤 [Plugin Handler] Sending error response:', errorResponse)
      figma.ui.postMessage(errorResponse)
      return
    }

    console.log('🔄 [Plugin Handler] Parsing figma nodes...')
    const parsedData = await parseFigmaNodes(selection)
    console.log('✅ [Plugin Handler] Parsing completed, data size:', JSON.stringify(parsedData).length)

    const successResponse = {
      type: 'figma_selection_response',
      data: { selection: parsedData },
      request_id: requestId,
      success: true
    }
    console.log('📤 [Plugin Handler] Sending success response:', {
      type: successResponse.type,
      request_id: successResponse.request_id,
      success: successResponse.success,
      dataSize: JSON.stringify(successResponse.data).length
    })
    figma.ui.postMessage(successResponse)
  } catch (error) {
    console.error('💥 [Plugin Handler] Error parsing figma selection:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      requestId
    })

    const errorResponse = {
      type: 'figma_selection_response',
      data: { error: 'Failed to parse selection', message: String(error) },
      request_id: requestId,
      success: false,
      error: String(error)
    }
    console.log('📤 [Plugin Handler] Sending error response for exception:', errorResponse)
    figma.ui.postMessage(errorResponse)
  }
}





// Handle window size changes - unified function for all resize operations
export function handleSetWindowSize(width: number, height: number): void {
  console.log('handleSetWindowSize called with:', { width, height })

  // Validate dimensions - allow smaller sizes for minimize operation
  const validWidth = Math.max(50, Math.min(1200, width))
  const validHeight = Math.max(30, Math.min(1000, height))

  console.log('Resizing to validated dimensions:', { validWidth, validHeight })
  figma.ui.resize(validWidth, validHeight)
  console.log('Resize completed')
}

// Handle plugin close
export function handlePluginClose(): void {
  figma.closePlugin()
}

// Storage handlers
export async function handleStorageGet(key: string, requestId?: string): Promise<void> {
  console.log('💾 [Plugin Handler] Starting storage get:', { key, requestId })

  try {
    const value = await figma.clientStorage.getAsync(key)
    console.log('✅ [Plugin Handler] Storage get successful:', { key, hasValue: !!value, requestId })

    const response = {
      type: 'storage_response',
      request_id: requestId,
      success: true,
      value: value || undefined
    }
    console.log('📤 [Plugin Handler] Sending storage get response:', response)
    figma.ui.postMessage(response)
  } catch (error) {
    console.error('💥 [Plugin Handler] Storage get error:', { key, requestId, error })

    const errorResponse = {
      type: 'storage_response',
      request_id: requestId,
      success: false,
      error: String(error)
    }
    console.log('📤 [Plugin Handler] Sending storage get error response:', errorResponse)
    figma.ui.postMessage(errorResponse)
  }
}

export async function handleStorageSet(key: string, value: string, requestId?: string): Promise<void> {
  try {
    await figma.clientStorage.setAsync(key, value)
    figma.ui.postMessage({
      type: 'storage_response',
      request_id: requestId,
      success: true
    })
  } catch (error) {
    figma.ui.postMessage({
      type: 'storage_response',
      request_id: requestId,
      success: false,
      error: String(error)
    })
  }
}

export async function handleStorageDelete(key: string, requestId?: string): Promise<void> {
  try {
    await figma.clientStorage.deleteAsync(key)
    figma.ui.postMessage({
      type: 'storage_response',
      request_id: requestId,
      success: true
    })
  } catch (error) {
    figma.ui.postMessage({
      type: 'storage_response',
      request_id: requestId,
      success: false,
      error: String(error)
    })
  }
}
