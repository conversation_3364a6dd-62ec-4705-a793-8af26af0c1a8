// OpenRouter Models Utility
// Functions for loading and searching OpenRouter models

import { OpenRouterModelsResponse, OpenRouterModelData, ModelSuggestion } from './types'

let cachedModels: OpenRouterModelData[] | null = null

/**
 * Load models from the JSON file
 */
export async function loadModels(): Promise<OpenRouterModelData[]> {
  if (cachedModels) {
    return cachedModels
  }

  try {
    // Dynamic import to load the JSON file
    const modelsData = await import('./models.json')
    const rawData = modelsData.default || modelsData

    // Handle both new format (with models array) and raw OpenRouter API format (with data array)
    let models: any[] = []
    if (rawData.models && Array.isArray(rawData.models)) {
      models = rawData.models
    } else if (rawData.data && Array.isArray(rawData.data)) {
      models = rawData.data
    } else {
      console.warn('⚠️ Invalid models data structure')
      return []
    }

    // Convert to our format
    cachedModels = models.map(convertRawModelToOurFormat).filter(Boolean)
    console.log(`✅ Loaded ${cachedModels.length} OpenRouter models`)
    return cachedModels
  } catch (error) {
    console.warn('⚠️ Failed to load OpenRouter models:', error)
    return []
  }
}

/**
 * Convert raw OpenRouter API model to our format
 */
function convertRawModelToOurFormat(rawModel: any): OpenRouterModelData | null {
  try {
    const pricing = rawModel.pricing || {}
    const promptPrice = parseFloat(pricing.prompt || '0')
    const completionPrice = parseFloat(pricing.completion || '0')

    return {
      id: rawModel.id || '',
      name: rawModel.name || rawModel.id || '',
      description: rawModel.description || '',
      pricing: {
        prompt: promptPrice,
        completion: completionPrice
      },
      contextLength: rawModel.context_length || rawModel.contextLength || 0,
      maxCompletionTokens: rawModel.top_provider?.max_completion_tokens || rawModel.maxCompletionTokens
    }
  } catch (error) {
    console.warn('Failed to convert model:', rawModel.id, error)
    return null
  }
}

/**
 * Search models by query string
 */
export function searchModels(models: OpenRouterModelData[], query: string): ModelSuggestion[] {
  if (!query.trim()) {
    // Return popular models when no query
    return getPopularModels(models)
  }

  const normalizedQuery = query.toLowerCase().trim()
  const suggestions: ModelSuggestion[] = []

  for (const model of models) {
    const matchScore = calculateMatchScore(model, normalizedQuery)
    if (matchScore > 0) {
      suggestions.push({
        ...model,
        matchScore
      })
    }
  }

  // Sort by match score (descending) and then by name
  suggestions.sort((a, b) => {
    if (b.matchScore !== a.matchScore) {
      return b.matchScore - a.matchScore
    }
    return a.name.localeCompare(b.name)
  })

  // Return top 10 suggestions
  return suggestions.slice(0, 10)
}

/**
 * Calculate match score for a model against a query
 */
function calculateMatchScore(model: OpenRouterModelData, query: string): number {
  const id = model.id.toLowerCase()
  const name = model.name.toLowerCase()
  const description = model.description.toLowerCase()

  let score = 0

  // Exact ID match gets highest score
  if (id === query) {
    score += 100
  }
  // ID starts with query
  else if (id.startsWith(query)) {
    score += 80
  }
  // ID contains query
  else if (id.includes(query)) {
    score += 60
  }

  // Name matches
  if (name.includes(query)) {
    score += 40
  }

  // Description matches
  if (description.includes(query)) {
    score += 20
  }

  // Boost score for popular models
  if (isPopularModel(model.id)) {
    score += 10
  }

  return score
}

/**
 * Get popular models for default suggestions
 */
function getPopularModels(models: OpenRouterModelData[]): ModelSuggestion[] {
  const popularModelIds = [
    'openai/gpt-4o-mini',
    'openai/gpt-4o',
    'anthropic/claude-3.5-sonnet',
    'google/gemini-pro',
    'meta-llama/llama-3.1-8b-instruct',
    'anthropic/claude-3-haiku',
    'openai/gpt-3.5-turbo',
    'mistralai/mistral-7b-instruct'
  ]

  const popularModels: ModelSuggestion[] = []

  for (const modelId of popularModelIds) {
    const model = models.find(m => m.id === modelId)
    if (model) {
      popularModels.push({
        ...model,
        matchScore: 100 - popularModelIds.indexOf(modelId) // Higher score for earlier in list
      })
    }
  }

  return popularModels
}

/**
 * Check if a model is considered popular
 */
function isPopularModel(modelId: string): boolean {
  const popularPrefixes = [
    'openai/',
    'anthropic/',
    'google/',
    'meta-llama/',
    'mistralai/'
  ]

  return popularPrefixes.some(prefix => modelId.startsWith(prefix))
}

/**
 * Format model for display in autocomplete
 */
export function formatModelForDisplay(model: ModelSuggestion): string {
  const priceInfo = model.pricing.prompt > 0
    ? ` ($${model.pricing.prompt}/$${model.pricing.completion})`
    : ' (Free)'

  return `${model.name}${priceInfo}`
}

/**
 * Highlight matching text in a string
 */
export function highlightMatch(text: string, query: string): string {
  if (!query.trim()) {
    return text
  }

  const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}
