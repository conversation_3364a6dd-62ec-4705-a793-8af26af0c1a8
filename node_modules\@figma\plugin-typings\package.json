{"name": "@figma/plugin-typings", "version": "1.113.0", "description": "Typings for the Figma Plugin API", "main": "", "scripts": {"prettier": "npm run prettier-path -- '**/*.{ts,js}'", "prettier:check": "npm run prettier-path:check -- '**/*.{ts,js}'", "prettier-path": "prettier --write", "prettier-path:check": "prettier --check", "test": "tsc"}, "repository": {"type": "git", "url": "git+https://github.com/figma/plugin-typings.git"}, "keywords": ["figma", "plugin", "typings"], "author": "Figma", "license": "MIT License", "bugs": {"url": "https://github.com/figma/plugin-typings/issues"}, "homepage": "https://github.com/figma/plugin-typings#readme", "devDependencies": {"prettier": "^2.6.1"}}