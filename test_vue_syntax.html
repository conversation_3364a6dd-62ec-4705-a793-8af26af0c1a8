<!DOCTYPE html>
<html>
<head>
    <title>Vue Syntax Highlighting Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Vue Syntax Highlighting Test</h1>
    
    <div class="test-section">
        <h2>Test: Vue Single File Component</h2>
        <p>This should be highlighted with Vue syntax highlighting:</p>
        <pre><code class="language-vue">&lt;template&gt;
  &lt;div class="hello-world"&gt;
    &lt;h1 v-if="showTitle"&gt;{{ title }}&lt;/h1&gt;
    &lt;button @click="toggleTitle"&gt;Toggle Title&lt;/button&gt;
    &lt;ul&gt;
      &lt;li v-for="item in items" :key="item.id"&gt;
        {{ item.name }}
      &lt;/li&gt;
    &lt;/ul&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script&gt;
export default {
  name: 'HelloWorld',
  data() {
    return {
      title: 'Hello Vue!',
      showTitle: true,
      items: [
        { id: 1, name: 'Item 1' },
        { id: 2, name: 'Item 2' },
        { id: 3, name: 'Item 3' }
      ]
    }
  },
  methods: {
    toggleTitle() {
      this.showTitle = !this.showTitle
    }
  }
}
&lt;/script&gt;

&lt;style scoped&gt;
.hello-world {
  text-align: center;
  padding: 20px;
}

h1 {
  color: #42b883;
}

button {
  background-color: #42b883;
  color: white;
  border: none;
  padding: 10px 20px;
  cursor: pointer;
  border-radius: 4px;
}

button:hover {
  background-color: #369870;
}

ul {
  list-style-type: none;
  padding: 0;
}

li {
  margin: 5px 0;
  padding: 5px;
  background-color: #f0f0f0;
  border-radius: 3px;
}
&lt;/style&gt;</code></pre>
    </div>
    
    <div class="test-section">
        <h2>Expected Result</h2>
        <p>When this code is rendered in the Figma plugin with Vue syntax highlighting:</p>
        <ul>
            <li>✅ No JavaScript errors in console about "languageDefinition.bind is not a function"</li>
            <li>✅ No errors about "Language definition for 'vue' could not be registered"</li>
            <li>✅ Vue template syntax (v-if, v-for, @click) should be properly highlighted</li>
            <li>✅ Script section should have JavaScript syntax highlighting</li>
            <li>✅ Style section should have CSS syntax highlighting</li>
        </ul>
    </div>
</body>
</html>
