#!/bin/bash

# FigmaAgent 开发环境快速启动脚本
# Bash 脚本用于快速启动开发环境

# 默认参数
MODE="web"
INSTALL=false
BUILD=false
OPEN=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --mode)
            MODE="$2"
            shift 2
            ;;
        --install)
            INSTALL=true
            shift
            ;;
        --build)
            BUILD=true
            shift
            ;;
        --open)
            OPEN=true
            shift
            ;;
        -h|--help)
            echo "FigmaAgent 开发环境启动器"
            echo ""
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  --mode MODE     启动模式 (web|plugin|both) [默认: web]"
            echo "  --install       重新安装依赖"
            echo "  --build         先构建项目"
            echo "  --open          自动打开浏览器"
            echo "  -h, --help      显示帮助信息"
            echo ""
            echo "示例:"
            echo "  $0 --mode web --open"
            echo "  $0 --mode plugin --build"
            echo "  $0 --mode both --install"
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            echo "使用 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

echo "🎨 FigmaAgent 开发环境启动器"
echo "================================"

# 检查 Node.js
if command -v node >/dev/null 2>&1; then
    NODE_VERSION=$(node --version)
    echo "✅ Node.js: $NODE_VERSION"
else
    echo "❌ Node.js 未安装或不在 PATH 中"
    exit 1
fi

# 检查 npm
if command -v npm >/dev/null 2>&1; then
    NPM_VERSION=$(npm --version)
    echo "✅ npm: v$NPM_VERSION"
else
    echo "❌ npm 未安装或不在 PATH 中"
    exit 1
fi

# 重新安装依赖（如果需要）
if [ "$INSTALL" = true ]; then
    echo "📦 重新安装依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
    echo "✅ 依赖安装完成"
fi

# 构建（如果需要）
if [ "$BUILD" = true ]; then
    echo "🏗️ 构建项目..."
    npm run build:prod
    if [ $? -ne 0 ]; then
        echo "❌ 构建失败"
        exit 1
    fi
    echo "✅ 构建完成"
fi

# 根据模式启动
case "${MODE,,}" in
    "web")
        echo "🌐 启动 Web 开发服务器..."
        echo "访问地址: http://localhost:5173/"
        
        if [ "$OPEN" = true ]; then
            sleep 2
            if command -v xdg-open >/dev/null 2>&1; then
                xdg-open "http://localhost:5173/" >/dev/null 2>&1 &
            elif command -v open >/dev/null 2>&1; then
                open "http://localhost:5173/" >/dev/null 2>&1 &
            fi
        fi
        
        npm run dev
        ;;
    
    "plugin")
        echo "🔌 构建 Figma 插件..."
        npm run build:prod
        
        if [ $? -eq 0 ]; then
            echo "✅ 插件构建完成"
            echo "📁 输出目录: dist/"
            echo "📋 在 Figma 中导入插件:"
            echo "   1. 打开 Figma 桌面应用"
            echo "   2. Plugins → Development → Import plugin from manifest"
            echo "   3. 选择项目根目录"
        else
            echo "❌ 插件构建失败"
            exit 1
        fi
        ;;
    
    "both")
        echo "🚀 构建插件并启动 Web 服务器..."
        
        # 先构建插件
        npm run build:prod
        if [ $? -ne 0 ]; then
            echo "❌ 插件构建失败"
            exit 1
        fi
        
        echo "✅ 插件构建完成"
        echo "🌐 启动 Web 开发服务器..."
        echo "访问地址: http://localhost:5173/"
        
        if [ "$OPEN" = true ]; then
            sleep 2
            if command -v xdg-open >/dev/null 2>&1; then
                xdg-open "http://localhost:5173/" >/dev/null 2>&1 &
            elif command -v open >/dev/null 2>&1; then
                open "http://localhost:5173/" >/dev/null 2>&1 &
            fi
        fi
        
        npm run dev
        ;;
    
    *)
        echo "❌ 无效的模式: $MODE"
        echo "可用模式: web, plugin, both"
        exit 1
        ;;
esac

echo "🎉 启动完成！"
