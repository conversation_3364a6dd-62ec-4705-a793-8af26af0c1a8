{"name": "figmagent", "version": "1.0.0", "description": "Figma Plugin with AI Chat and Multi-Provider AI Integration", "main": "dist/plugin.js", "type": "module", "scripts": {"build": "npm run build:prod", "build:prod": "concurrently \"vite build --config vite.config.plugin.ts\" \"vite build --config vite.config.ui.ts\"", "build:staging": "concurrently \"vite build --config vite.config.plugin.ts --mode staging\" \"vite build --config vite.config.ui.ts --mode staging\"", "dev": "vite --config vite.config.ui.ts --mode development", "dev:open": "vite --config vite.config.ui.ts --mode development --open", "preview": "vite preview", "watch:prod": "nodemon --watch src/ --ext ts,tsx,js,jsx,css --exec \"npm run build:prod\"", "watch:staging": "nodemon --watch src/ --ext ts,tsx,js,jsx,css --exec \"npm run build:staging\"", "tsc": "tsc -p tsconfig.json", "type-check": "npm run tsc", "lint": "eslint --ext .ts,.tsx --ignore-pattern node_modules .", "lint:fix": "eslint --ext .ts,.tsx --ignore-pattern node_modules --fix .", "lint:check": "npm run lint && npm run type-check", "extract-types": "npx tsx scripts/extract_figma_types.ts", "update-models": "python scripts/update_openrouter_models.py", "clean": "rimraf dist/ node_modules/.cache/ .eslintcache", "setup": "npm install && npm run update-models", "start:web": "powershell -ExecutionPolicy Bypass -File scripts/dev-start.ps1 -Mode web", "start:plugin": "powershell -ExecutionPolicy Bypass -File scripts/dev-start.ps1 -Mode plugin", "start:both": "powershell -ExecutionPolicy Bypass -File scripts/dev-start.ps1 -Mode both", "postinstall": "python scripts/update_openrouter_models.py || echo 'Warning: Failed to update OpenRouter models. Continuing with existing data.'"}, "author": "", "license": "", "devDependencies": {"@figma/eslint-plugin-figma-plugins": "*", "@figma/plugin-typings": "*", "@tailwindcss/vite": "^4.1.7", "@types/node": "^22.15.21", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "@types/react-syntax-highlighter": "^15.5.13", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "@vitejs/plugin-react": "^4.5.0", "concurrently": "^9.1.2", "eslint": "^8.54.0", "jotai-devtools": "^0.12.0", "json5": "^2.2.3", "nodemon": "^3.1.10", "tsx": "^4.19.4", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-plugin-singlefile": "^2.2.0", "vite-plugin-wasm": "^3.4.1"}, "eslintConfig": {"extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:@figma/figma-plugins/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"project": "./tsconfig.json"}, "root": true, "rules": {"@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_", "caughtErrorsIgnorePattern": "^_"}], "no-debugger": "warn"}}, "dependencies": {"@tailwindcss/typography": "^0.5.16", "@tanstack/react-virtual": "^3.13.10", "css-to-tailwind-translator": "^1.2.8", "highlightjs-vue": "^1.0.0", "jotai": "^2.12.5", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "react-use": "^17.6.0", "remark-gfm": "^4.0.1", "tailwindcss": "^4.1.7"}}