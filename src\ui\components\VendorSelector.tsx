// Vendor Selector Component
// Allows users to switch between AI providers in the chat interface

import React, { useState, useRef, useCallback } from 'react'
import { AIVendor } from '../types/ui_types'

interface VendorSelectorProps {
  vendor: AIVendor
  onVendorChange: (vendor: AIVendor) => void
  className?: string
  isLoading?: boolean
}

const VENDOR_OPTIONS = [
  { value: 'openrouter' as AIVendor, label: 'OpenRouter', color: 'bg-blue-500' },
  { value: 'deepseek' as AIVendor, label: 'DeepSeek', color: 'bg-purple-500' }
]

export const VendorSelector: React.FC<VendorSelectorProps> = ({
  vendor,
  onVendorChange,
  className = "",
  isLoading = false
}) => {
  const [showDropdown, setShowDropdown] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  const currentOption = VENDOR_OPTIONS.find(option => option.value === vendor) || VENDOR_OPTIONS[0] // Fallback to first option

  const handleOptionSelect = useCallback((selectedVendor: AIVendor) => {
    onVendorChange(selectedVendor)
    setShowDropdown(false)
  }, [onVendorChange])

  const handleBlur = useCallback(() => {
    // Delay hiding dropdown to allow for clicks
    setTimeout(() => {
      setShowDropdown(false)
    }, 150)
  }, [])

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {/* Phi (Φ) icon for Figma AI branding */}
      <span className="text-sm font-bold text-blue-600" title="Figma AI Provider">Φ</span>

      {/* Custom dropdown */}
      <div className="relative">
        <button
          type="button"
          onClick={() => setShowDropdown(!showDropdown)}
          onBlur={handleBlur}
          disabled={isLoading}
          className="text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white flex items-center gap-1 min-w-[80px] justify-between disabled:bg-gray-100 disabled:cursor-not-allowed"
        >
          <span>{isLoading ? 'Loading...' : currentOption.label}</span>
          <svg
            className={`w-3 h-3 transition-transform ${showDropdown ? 'rotate-180' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>

        {/* Dropdown menu - positioned above button */}
        {showDropdown && (
          <div
            ref={dropdownRef}
            className="absolute bottom-full left-0 right-0 mb-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50"
          >
            {VENDOR_OPTIONS.map((option) => (
              <button
                key={option.value}
                type="button"
                onClick={() => handleOptionSelect(option.value)}
                className={`w-full px-2 py-1.5 text-xs text-left hover:bg-gray-50 transition-colors border-b border-gray-100 last:border-b-0 first:rounded-t-lg last:rounded-b-lg ${
                  option.value === vendor ? 'bg-blue-50 text-blue-600' : 'text-gray-800'
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Visual indicator */}
      <div className={`w-2 h-2 rounded-full ${currentOption.color}`} title={`Using ${currentOption.label}`} />
    </div>
  )
}
