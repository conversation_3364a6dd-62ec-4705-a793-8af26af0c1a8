// OpenRouter API Client
// Provides a clean interface for OpenRouter API interactions

import {
  OpenRouterConfig,
  AiRequest,
  AiResponse,
  AiStreamChunk,
  AiMessage,
  AiToolCall,
  ToolExecutionResult,
  // Legacy aliases for backward compatibility
  OpenRouterRequest,
  OpenRouterResponse,
  OpenRouterStreamChunk,
  OpenRouterMessage,
  OpenRouterToolCall
} from '../../ui/types/ui_types'
import { FIGMA_TOOLS, executeTool } from './tools'

export class OpenRouterClient {
  private baseUrl = 'https://openrouter.ai/api/v1'
  private config: OpenRouterConfig
  private enableTools: boolean

  constructor(config: OpenRouterConfig, enableTools: boolean = true) {
    this.config = config
    this.enableTools = enableTools
  }

  // Update configuration
  updateConfig(config: OpenRouterConfig) {
    this.config = config
  }

  // Validate API key format
  private validateApiKey(): boolean {
    return Boolean(this.config.apiKey && this.config.apiKey.length > 0)
  }

  // Create request headers
  private createHeaders(): Record<string, string> {
    return {
      'Authorization': `Bearer ${this.config.apiKey}`,
      'Content-Type': 'application/json',
      'HTTP-Referer': 'https://figma.com',
      'X-Title': 'FigmaAgent'
    }
  }

  // Convert conversation to AI format
  private formatMessages(userMessage: string, conversation: AiMessage[]): AiMessage[] {
    const messages: AiMessage[] = []

    // Add system prompt if configured
    if (this.config.systemPrompt) {
      messages.push({
        role: 'system',
        content: this.config.systemPrompt
      })
    }

    // Add conversation history (keep last 10 messages for context)
    const recentConversation = conversation.slice(-10)
    messages.push(...recentConversation)

    // Add current user message
    messages.push({
      role: 'user',
      content: userMessage
    })

    return messages
  }

  // Format conversation messages for API request (without adding new user message)
  private formatConversationMessages(conversation: AiMessage[]): AiMessage[] {
    const messages: AiMessage[] = []

    // Add system prompt if configured
    if (this.config.systemPrompt) {
      messages.push({
        role: 'system',
        content: this.config.systemPrompt
      })
    }

    // Add conversation history (keep last 10 messages for context)
    const recentConversation = conversation.slice(-10)
    messages.push(...recentConversation)

    return messages
  }

  // Send chat completion request
  async sendMessage(
    userMessage: string,
    conversation: AiMessage[] = []
  ): Promise<AiResponse> {
    if (!this.validateApiKey()) {
      throw new Error('OpenRouter API key is required')
    }

    // AI Assistant is always enabled - check for API key instead
    if (!this.validateApiKey()) {
      throw new Error('OpenRouter API key is required')
    }

    const messages = this.formatMessages(userMessage, conversation)

    const request: AiRequest = {
      model: this.config.model,
      messages,
      temperature: this.config.temperature,
      max_tokens: this.config.maxTokens,
      stream: false,
      user: 'figmagent-user',
      ...(this.enableTools && { tools: FIGMA_TOOLS, tool_choice: 'auto' })
    }

    try {
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: this.createHeaders(),
        body: JSON.stringify(request)
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(
          `OpenRouter API error: ${response.status} ${response.statusText}. ${
            errorData.error?.message || 'Unknown error'
          }`
        )
      }

      const data: AiResponse = await response.json()

      // Handle tool calls if present
      if (data.choices[0]?.message?.tool_calls) {
        await this.handleToolCalls(data.choices[0].message.tool_calls, conversation)
      }

      return data
    } catch (error) {
      if (error instanceof Error) {
        throw error
      }
      throw new Error(`Network error: ${String(error)}`)
    }
  }

  // Send multi-turn chat completion request with tool calling support
  async sendMultiTurnMessage(
    userMessage: string,
    conversation: AiMessage[] = [],
    maxTurns: number = 5,
    onToolCall?: (toolCall: AiToolCall, result: ToolExecutionResult) => void,
    onTurnComplete?: (turn: number, response: AiResponse) => void
  ): Promise<AiResponse> {
    console.log(`🔄 [OpenRouter] Starting multi-turn conversation (max ${maxTurns} turns)`)

    if (!this.validateApiKey()) {
      throw new Error('OpenRouter API key is required')
    }

    // Create a working copy of the conversation
    const workingConversation = [...conversation]
    let currentTurn = 0
    let lastResponse: AiResponse | null = null

    // Add the initial user message
    const userAiMessage: AiMessage = {
      role: 'user',
      content: userMessage
    }
    workingConversation.push(userAiMessage)

    while (currentTurn < maxTurns) {
      currentTurn++
      console.log(`🔄 [OpenRouter] Turn ${currentTurn}/${maxTurns}`)

      const messages = workingConversation

      const request: AiRequest = {
        model: this.config.model,
        messages,
        temperature: this.config.temperature,
        max_tokens: this.config.maxTokens,
        stream: false,
        user: 'figmagent-user',
        ...(this.enableTools && { tools: FIGMA_TOOLS, tool_choice: 'auto' })
      }

      try {
        const response = await fetch(`${this.baseUrl}/chat/completions`, {
          method: 'POST',
          headers: this.createHeaders(),
          body: JSON.stringify(request)
        })

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}))
          throw new Error(
            `OpenRouter API error: ${response.status} ${response.statusText}. ${
              errorData.error?.message || 'Unknown error'
            }`
          )
        }

        const data: AiResponse = await response.json()
        lastResponse = data

        // Add AI response to working conversation
        const aiMessage = data.choices[0]?.message
        if (aiMessage) {
          const aiResponseMessage: AiMessage = {
            role: 'assistant',
            content: aiMessage.content || '',
            ...(aiMessage.tool_calls && { tool_calls: aiMessage.tool_calls })
          }
          workingConversation.push(aiResponseMessage)

          // If this message has tool calls, notify about the AI's intent
          if (aiMessage.tool_calls && aiMessage.tool_calls.length > 0 && aiMessage.content) {
            console.log(`🤔 [OpenRouter] Turn ${currentTurn}: AI reasoning: "${aiMessage.content}"`)
          }
        }

        // Notify about turn completion
        if (onTurnComplete) {
          onTurnComplete(currentTurn, data)
        }

        // Handle tool calls if present
        if (data.choices[0]?.message?.tool_calls) {
          console.log(`🔧 [OpenRouter] Turn ${currentTurn}: Executing ${data.choices[0].message.tool_calls.length} tool calls`)

          const hasToolResults = await this.handleMultiTurnToolCalls(
            data.choices[0].message.tool_calls,
            workingConversation,
            onToolCall
          )

          // If we executed tools, continue to next turn for AI to analyze results
          if (hasToolResults) {
            continue
          }
        }

        // No tool calls, conversation is complete
        console.log(`✅ [OpenRouter] Multi-turn conversation completed in ${currentTurn} turns`)
        break

      } catch (error) {
        console.error(`❌ [OpenRouter] Turn ${currentTurn} failed:`, error)
        if (error instanceof Error) {
          throw error
        }
        throw new Error(`Network error: ${String(error)}`)
      }
    }

    if (currentTurn >= maxTurns) {
      console.warn(`⚠️ [OpenRouter] Multi-turn conversation reached max turns limit (${maxTurns})`)
    }

    // Update the original conversation with all messages
    conversation.splice(0, conversation.length, ...workingConversation)

    return lastResponse || {
      id: 'fallback-response',
      model: this.config.model,
      choices: [{ message: { role: 'assistant', content: 'No response' }, finish_reason: 'error' }],
      usage: { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 }
    }
  }

  // Send multi-turn streaming chat completion request with tool calling support
  async sendMultiTurnStreamingMessage(
    userMessage: string,
    conversation: AiMessage[] = [],
    maxTurns: number = 5,
    onChunk: (chunk: string) => void,
    onComplete: () => void,
    onError: (error: Error) => void,
    abortController?: AbortController,
    onToolCall?: (toolCall: AiToolCall, result: ToolExecutionResult) => void,
    onTurnComplete?: (turn: number, hasToolCalls: boolean) => void
  ): Promise<void> {
    console.log(`🔄 [OpenRouter] Starting multi-turn streaming conversation (max ${maxTurns} turns)`)

    if (!this.validateApiKey()) {
      throw new Error('OpenRouter API key is required')
    }

    // Create working conversation copy
    const workingConversation = [...conversation]

    // Add user message to working conversation
    const userAiMessage: AiMessage = {
      role: 'user',
      content: userMessage
    }
    workingConversation.push(userAiMessage)

    let currentTurn = 1

    while (currentTurn <= maxTurns) {
      if (abortController?.signal.aborted) {
        console.log('🛑 [OpenRouter] Multi-turn streaming conversation aborted')
        return
      }

      console.log(`🔄 [OpenRouter] Turn ${currentTurn}/${maxTurns}`)

      try {
        const hasToolCalls = await this.executeSingleStreamingTurn(
          workingConversation,
          onChunk,
          onError,
          abortController,
          onToolCall
        )

        // Notify about turn completion
        if (onTurnComplete) {
          onTurnComplete(currentTurn, hasToolCalls)
        }

        // If no tool calls were made, conversation is complete
        if (!hasToolCalls) {
          console.log(`✅ [OpenRouter] Multi-turn streaming conversation completed in ${currentTurn} turns`)
          break
        }

        // Continue to next turn for AI to analyze tool results
        currentTurn++
      } catch (error) {
        console.error(`❌ [OpenRouter] Turn ${currentTurn} failed:`, error)
        onError(error instanceof Error ? error : new Error(String(error)))
        return
      }
    }

    if (currentTurn > maxTurns) {
      console.warn(`⚠️ [OpenRouter] Multi-turn streaming conversation reached max turns limit (${maxTurns})`)
    }

    // Update the original conversation with all messages
    conversation.splice(0, conversation.length, ...workingConversation)
    onComplete()
  }

  // Execute a single streaming turn and return whether tool calls were made
  private async executeSingleStreamingTurn(
    conversation: AiMessage[],
    onChunk: (chunk: string) => void,
    onError: (error: Error) => void,
    abortController?: AbortController,
    onToolCall?: (toolCall: AiToolCall, result: ToolExecutionResult) => void
  ): Promise<boolean> {
    const messages = this.formatConversationMessages(conversation)

    const request: AiRequest = {
      model: this.config.model,
      messages,
      temperature: this.config.temperature,
      max_tokens: this.config.maxTokens,
      stream: true,
      user: 'figmagent-user',
      ...(this.enableTools && { tools: FIGMA_TOOLS, tool_choice: 'auto' })
    }

    console.log(`🔧 [OpenRouter] Tools enabled: ${this.enableTools}, Tools count: ${FIGMA_TOOLS.length}`)
    if (this.enableTools) {
      console.log(`🔧 [OpenRouter] Available tools:`, FIGMA_TOOLS.map(t => t.function.name))
    }

    try {
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: this.createHeaders(),
        body: JSON.stringify(request),
        signal: abortController?.signal
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(
          `OpenRouter API error: ${response.status} ${response.statusText}. ${
            errorData.error?.message || 'Unknown error'
          }`
        )
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('Failed to get response stream reader')
      }

      const decoder = new TextDecoder()
      let buffer = ''
      let accumulatedToolCalls: Record<string, Partial<OpenRouterToolCall>> = {}
      let aiContent = ''

    try {
      while (true) {
        if (abortController?.signal.aborted) {
          console.log('🛑 [OpenRouter] Streaming turn aborted')
          break
        }

        const { done, value } = await reader.read()
        if (done) break

        buffer += decoder.decode(value, { stream: true })
        const lines = buffer.split('\n')
        buffer = lines.pop() || ''

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6).trim()
            if (data === '[DONE]') {
              // Add AI response to conversation FIRST (before executing tools)
              let hasToolCalls = false
              if (aiContent.trim() || Object.keys(accumulatedToolCalls).length > 0) {
                const aiMessage: OpenRouterMessage = {
                  role: 'assistant',
                  content: aiContent,
                  ...(Object.keys(accumulatedToolCalls).length > 0 && {
                    tool_calls: Object.values(accumulatedToolCalls).map(tc => ({
                      id: tc.id!,
                      type: 'function' as const,
                      function: tc.function!
                    }))
                  })
                }
                conversation.push(aiMessage)
              }

              // Then execute any accumulated tool calls (this adds tool messages after assistant message)
              if (Object.keys(accumulatedToolCalls).length > 0) {
                hasToolCalls = await this.executeAccumulatedToolCallsForMultiTurn(
                  accumulatedToolCalls,
                  conversation,
                  onToolCall
                )
              }

              return hasToolCalls
            }

            try {
              const chunk: AiStreamChunk = JSON.parse(data)
              const delta = chunk.choices[0]?.delta

              // Handle content chunks
              if (delta?.content) {
                aiContent += delta.content
                onChunk(delta.content)
              }

              // Handle tool call chunks
              if (delta?.tool_calls) {
                this.accumulateToolCalls(delta.tool_calls, accumulatedToolCalls)
              }
            } catch (parseError) {
              console.warn('Failed to parse streaming chunk:', parseError)
            }
          }
        }
      }

      // Handle case where stream ended without [DONE]
      if (!abortController?.signal.aborted) {
        // Add AI response to conversation FIRST (before executing tools)
        let hasToolCalls = false
        if (aiContent.trim() || Object.keys(accumulatedToolCalls).length > 0) {
          const aiMessage: AiMessage = {
            role: 'assistant',
            content: aiContent,
            ...(Object.keys(accumulatedToolCalls).length > 0 && {
              tool_calls: Object.values(accumulatedToolCalls).map(tc => ({
                id: tc.id!,
                type: 'function' as const,
                function: tc.function!
              }))
            })
          }
          conversation.push(aiMessage)
        }

        // Then execute any accumulated tool calls (this adds tool messages after assistant message)
        if (Object.keys(accumulatedToolCalls).length > 0) {
          hasToolCalls = await this.executeAccumulatedToolCallsForMultiTurn(
            accumulatedToolCalls,
            conversation,
            onToolCall
          )
        }

        return hasToolCalls
      }

      return false
    } finally {
      reader.releaseLock()
    }
    } catch (error) {
      // Don't call onError if the request was aborted intentionally
      if (error instanceof Error && error.name === 'AbortError') {
        console.log('🛑 [OpenRouter] Multi-turn streaming turn was aborted')
        return false
      }
      onError(error instanceof Error ? error : new Error(String(error)))
      return false
    }
  }

  // Send streaming chat completion request (legacy single-turn version)
  async sendStreamingMessage(
    userMessage: string,
    conversation: AiMessage[] = [],
    onChunk: (chunk: string) => void,
    onComplete: () => void,
    onError: (error: Error) => void,
    abortController?: AbortController,
    onToolCall?: (toolCall: AiToolCall, result: ToolExecutionResult) => void
  ): Promise<void> {
    console.log(`🤖 [OpenRouter] Streaming ${this.config.model} (${conversation.length} msgs)`)

    if (!this.validateApiKey()) {
      console.log('❌ [OpenRouterClient] API key validation failed')
      throw new Error('OpenRouter API key is required')
    }

    // AI Assistant is always enabled - check for API key instead
    if (!this.validateApiKey()) {
      console.log('❌ [OpenRouterClient] API key validation failed')
      throw new Error('OpenRouter API key is required')
    }

    console.log('🔧 [OpenRouterClient] Formatting messages')
    const messages = this.formatMessages(userMessage, conversation)
    console.log('🔧 [OpenRouterClient] Formatted messages:', messages)

    const request: AiRequest = {
      model: this.config.model,
      messages,
      temperature: this.config.temperature,
      max_tokens: this.config.maxTokens,
      stream: true,
      user: 'figmagent-user',
      ...(this.enableTools && { tools: FIGMA_TOOLS, tool_choice: 'auto' })
    }

    try {
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: this.createHeaders(),
        body: JSON.stringify(request),
        signal: abortController?.signal
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(
          `OpenRouter API error: ${response.status} ${response.statusText}. ${
            errorData.error?.message || 'Unknown error'
          }`
        )
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('Failed to get response stream reader')
      }

      const decoder = new TextDecoder()
      let buffer = ''
      let accumulatedToolCalls: Record<string, Partial<OpenRouterToolCall>> = {}

      try {
        while (true) {
          // Check if request was aborted
          if (abortController?.signal.aborted) {
            console.log('🛑 [OpenRouterClient] Streaming request aborted')
            break
          }

          const { done, value } = await reader.read()
          if (done) break

          buffer += decoder.decode(value, { stream: true })
          const lines = buffer.split('\n')
          buffer = lines.pop() || ''

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6).trim()
              if (data === '[DONE]') {
                // Execute any accumulated tool calls before completing
                await this.executeAccumulatedToolCalls(accumulatedToolCalls, conversation, onToolCall)
                onComplete()
                return
              }

              try {
                const chunk: AiStreamChunk = JSON.parse(data)
                const delta = chunk.choices[0]?.delta

                // Handle content chunks
                if (delta?.content) {
                  onChunk(delta.content)
                }

                // Handle tool call chunks
                if (delta?.tool_calls) {
                  this.accumulateToolCalls(delta.tool_calls, accumulatedToolCalls)
                }
              } catch (parseError) {
                console.warn('Failed to parse streaming chunk:', parseError)
              }
            }
          }
        }

        // Execute any accumulated tool calls and complete if not aborted
        if (!abortController?.signal.aborted) {
          await this.executeAccumulatedToolCalls(accumulatedToolCalls, conversation, onToolCall)
          onComplete()
        }
      } finally {
        reader.releaseLock()
      }
    } catch (error) {
      // Don't call onError if the request was aborted intentionally
      if (error instanceof Error && error.name === 'AbortError') {
        console.log('🛑 [OpenRouterClient] Streaming request was aborted')
        return
      }
      onError(error instanceof Error ? error : new Error(String(error)))
    }
  }

  // Accumulate tool call chunks during streaming
  private accumulateToolCalls(
    toolCallDeltas: OpenRouterToolCall[],
    accumulated: Record<string, Partial<OpenRouterToolCall>>
  ): void {
    for (const delta of toolCallDeltas) {
      if (!accumulated[delta.id]) {
        accumulated[delta.id] = {
          id: delta.id,
          type: delta.type,
          function: {
            name: delta.function?.name || '',
            arguments: delta.function?.arguments || ''
          }
        }
      } else {
        // Accumulate function arguments
        if (delta.function?.arguments) {
          accumulated[delta.id].function!.arguments += delta.function.arguments
        }
        if (delta.function?.name) {
          accumulated[delta.id].function!.name = delta.function.name
        }
      }
    }
  }

  // Execute accumulated tool calls for multi-turn conversations
  private async executeAccumulatedToolCallsForMultiTurn(
    accumulated: Record<string, Partial<OpenRouterToolCall>>,
    conversation: OpenRouterMessage[],
    onToolCall?: (toolCall: OpenRouterToolCall, result: ToolExecutionResult) => void
  ): Promise<boolean> {
    let hasResults = false

    for (const [id, partialToolCall] of Object.entries(accumulated)) {
      if (partialToolCall.function?.name && partialToolCall.function?.arguments !== undefined) {
        const toolCall: OpenRouterToolCall = {
          id,
          type: 'function',
          function: {
            name: partialToolCall.function.name,
            arguments: partialToolCall.function.arguments
          }
        }

        try {
          // Parse arguments and execute tool
          const args = JSON.parse(toolCall.function.arguments || '{}')
          const result = await executeTool(toolCall.function.name, args)
          hasResults = true

          // Notify callback if provided
          if (onToolCall) {
            onToolCall(toolCall, result)
          }

          // Add tool result to conversation
          const toolMessage: OpenRouterMessage = {
            role: 'tool',
            content: result.success
              ? JSON.stringify(result.data, null, 2)
              : `Error: ${result.error}`,
            tool_call_id: toolCall.id
          }

          conversation.push(toolMessage)
        } catch (error) {
          console.error('Tool execution failed:', error)
          hasResults = true

          const errorResult: ToolExecutionResult = {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          }

          // Notify callback if provided
          if (onToolCall) {
            onToolCall(toolCall, errorResult)
          }

          // Add error message to conversation
          const errorMessage: OpenRouterMessage = {
            role: 'tool',
            content: `Error executing tool: ${errorResult.error}`,
            tool_call_id: toolCall.id
          }

          conversation.push(errorMessage)
        }
      }
    }

    return hasResults
  }

  // Execute accumulated tool calls (legacy single-turn version)
  private async executeAccumulatedToolCalls(
    accumulated: Record<string, Partial<OpenRouterToolCall>>,
    conversation: OpenRouterMessage[],
    onToolCall?: (toolCall: OpenRouterToolCall, result: ToolExecutionResult) => void
  ): Promise<void> {
    for (const [id, partialToolCall] of Object.entries(accumulated)) {
      if (partialToolCall.function?.name && partialToolCall.function?.arguments !== undefined) {
        const toolCall: OpenRouterToolCall = {
          id,
          type: 'function',
          function: {
            name: partialToolCall.function.name,
            arguments: partialToolCall.function.arguments
          }
        }

        try {
          // Parse arguments and execute tool
          const args = JSON.parse(toolCall.function.arguments || '{}')
          const result = await executeTool(toolCall.function.name, args)

          // Notify callback if provided
          if (onToolCall) {
            onToolCall(toolCall, result)
          }

          // Add tool result to conversation
          const toolMessage: OpenRouterMessage = {
            role: 'tool',
            content: result.success
              ? JSON.stringify(result.data, null, 2)
              : `Error: ${result.error}`,
            tool_call_id: toolCall.id
          }

          conversation.push(toolMessage)
        } catch (error) {
          console.error('Tool execution failed:', error)

          const errorResult: ToolExecutionResult = {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          }

          // Notify callback if provided
          if (onToolCall) {
            onToolCall(toolCall, errorResult)
          }

          // Add error message to conversation
          const errorMessage: OpenRouterMessage = {
            role: 'tool',
            content: `Error executing tool: ${errorResult.error}`,
            tool_call_id: toolCall.id
          }

          conversation.push(errorMessage)
        }
      }
    }
  }

  // Handle tool calls from AI response
  private async handleToolCalls(
    toolCalls: AiToolCall[],
    conversation: AiMessage[]
  ): Promise<void> {
    for (const toolCall of toolCalls) {
      if (toolCall.type === 'function') {
        try {
          // Parse arguments (they come as JSON string)
          const args = JSON.parse(toolCall.function.arguments || '{}')

          // Execute the tool
          const result = await executeTool(toolCall.function.name, args)

          // Add tool result to conversation
          const toolMessage: AiMessage = {
            role: 'tool',
            content: result.success
              ? JSON.stringify(result.data, null, 2)
              : `Error: ${result.error}`,
            tool_call_id: toolCall.id
          }

          conversation.push(toolMessage)
        } catch (error) {
          console.error('Tool execution failed:', error)

          // Add error message to conversation
          const errorMessage: OpenRouterMessage = {
            role: 'tool',
            content: `Error executing tool: ${error instanceof Error ? error.message : 'Unknown error'}`,
            tool_call_id: toolCall.id
          }

          conversation.push(errorMessage)
        }
      }
    }
  }

  // Handle tool calls for multi-turn conversations
  private async handleMultiTurnToolCalls(
    toolCalls: OpenRouterToolCall[],
    conversation: OpenRouterMessage[],
    onToolCall?: (toolCall: OpenRouterToolCall, result: ToolExecutionResult) => void
  ): Promise<boolean> {
    let hasResults = false

    for (const toolCall of toolCalls) {
      if (toolCall.type === 'function') {
        try {
          // Parse arguments (they come as JSON string)
          const args = JSON.parse(toolCall.function.arguments || '{}')

          // Execute the tool
          const result = await executeTool(toolCall.function.name, args)
          hasResults = true

          // Notify callback if provided
          if (onToolCall) {
            onToolCall(toolCall, result)
          }

          // Add tool result to conversation
          const toolMessage: OpenRouterMessage = {
            role: 'tool',
            content: result.success
              ? JSON.stringify(result.data, null, 2)
              : `Error: ${result.error}`,
            tool_call_id: toolCall.id
          }

          conversation.push(toolMessage)
        } catch (error) {
          console.error('Tool execution failed:', error)
          hasResults = true

          const errorResult: ToolExecutionResult = {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          }

          // Notify callback if provided
          if (onToolCall) {
            onToolCall(toolCall, errorResult)
          }

          // Add error result to conversation
          const errorMessage: OpenRouterMessage = {
            role: 'tool',
            content: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
            tool_call_id: toolCall.id
          }

          conversation.push(errorMessage)
        }
      }
    }

    return hasResults
  }

  // Test API connection
  async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      await this.sendMessage('Hello, this is a test message.')
      return { success: true }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  // Get available models (static list for now)
  getAvailableModels() {
    return [
      'openai/gpt-4o-mini',
      'openai/gpt-4o',
      'anthropic/claude-3.5-sonnet',
      'google/gemini-pro',
      'meta-llama/llama-3.1-8b-instruct'
    ]
  }
}

// Singleton instance
let openRouterClient: OpenRouterClient | null = null

export function getOpenRouterClient(config: OpenRouterConfig): OpenRouterClient {
  if (!openRouterClient) {
    openRouterClient = new OpenRouterClient(config)
  } else {
    openRouterClient.updateConfig(config)
  }
  return openRouterClient
}
