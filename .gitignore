# 依赖和包管理
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
*.log
*.log.*

# 构建输出
dist/
build/
out/
tmp/
llmdocs/
*.tsbuildinfo

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 缓存和临时文件
.cache
.parcel-cache
.eslintcache
.stylelintcache
*.tsbuildinfo

# 系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
*.lnk

# IDE 和编辑器
.idea/
*.swp
*.swo
*~

# 测试覆盖率
coverage/
.nyc_output/

# Python 相关 (用于脚本)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# 备份和临时文件
*.bak
*.backup
*.old
*.orig
*.tmp

# 压缩文件
*.zip
*.tar.gz
*.rar

# 调试和性能分析
debug.log
*.debug
*.cpuprofile
*.heapprofile
