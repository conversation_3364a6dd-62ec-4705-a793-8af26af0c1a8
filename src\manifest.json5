{
  // 插件基本信息
  "name": "figmagent",                    // 插件在 Figma 中显示的名称
  "id": "1508668258843197249",           // Figma 开发者控制台分配的唯一插件 ID
  "api": "1.0.0",                       // 使用的 Figma Plugin API 版本

  // 文件路径配置
  "main": "./plugin.js",                // 插件主代码文件（运行在 Figma 沙箱中）
  "ui": "./index.html",                 // UI 界面文件（运行在 iframe 中）

  // 插件功能配置
  "capabilities": [],                   // 插件能力列表（可添加 "textreview", "inspect" 等）
  "enableProposedApi": false,           // 是否启用 Figma 实验性 API
  "documentAccess": "dynamic-page",     // 文档访问权限：允许动态访问当前页面

  // 支持的编辑器
  "editorType": [
    "figma"                            // 仅支持 Figma（也可支持 "figjam"）
  ],

  // 网络访问配置
  "networkAccess": {
    "allowedDomains": [
      "https://openrouter.ai",          // 允许访问 OpenRouter AI API 服务
      "https://api.deepseek.com"
    ],
    // 网络访问原因说明
    "reasoning": "Plugin needs access to OpenRouter and DeepSeek APIs for AI assistant functionality"
  }
}