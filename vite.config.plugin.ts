import path from "path";
import { defineConfig, Plugin } from "vite";
import { viteSingleFile } from "vite-plugin-singlefile";
import JSON5 from "json5";
import { readFileSync } from "fs";

// JSON5 to JSON plugin for manifest
function json5ManifestPlugin(): Plugin {
  return {
    name: 'json5-manifest',
    generateBundle() {
      try {
        // Read and parse JSON5 manifest
        const json5Content = readFileSync(path.resolve('src/manifest.json5'), 'utf-8');
        const manifestObject = JSON5.parse(json5Content);

        // Convert to clean JSON
        const manifestJson = JSON.stringify(manifestObject, null, 2);

        // Emit as manifest.json
        this.emitFile({
          type: 'asset',
          fileName: 'manifest.json',
          source: manifestJson
        });

        console.log('✅ Generated manifest.json from manifest.json5');
      } catch (error) {
        console.error('❌ Failed to process manifest.json5:', error);
        this.error(`Failed to process manifest.json5: ${error}`);
      }
    }
  };
}

export default defineConfig(({ mode }) => ({
  plugins: [
    viteSingleFile(),
    json5ManifestPlugin(),
  ],
  build: {
    target: 'es2017',
    minify: mode === 'production',
    sourcemap: mode !== 'production' ? 'inline' : false,
    emptyOutDir: false,
    outDir: path.resolve("dist"),
    rollupOptions: {
      input: path.resolve('src/plugin/plugin.ts'),
      output: {
        entryFileNames: 'plugin.js',
        format: 'iife', // safe for use
        name: 'FigmaPlugin'
      },
    },
  },
}));
