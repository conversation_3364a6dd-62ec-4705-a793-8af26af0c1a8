import React from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'
import { Light as HljsSyntaxHighlighter } from 'react-syntax-highlighter'
import { oneLight } from 'react-syntax-highlighter/dist/esm/styles/prism'
import { atomOneLight } from 'react-syntax-highlighter/dist/esm/styles/hljs'
import InlineLoadingIndicator from './InlineLoadingIndicator'

import UnifiedCodeBlock from './UnifiedCodeBlock'
import { AiLoadingState } from '../types/ui_types'

// Import Vue language definition directly from highlightjs-vue
import { definer as vue } from 'highlightjs-vue'

// Register Vue language for highlight.js
try {
  HljsSyntaxHighlighter.registerLanguage('vue', vue)
  console.log('✅ Vue language registered successfully for syntax highlighting')
} catch (error) {
  console.warn('❌ Failed to register Vue language for syntax highlighting:', error)
}

interface MessageContentWithProgressProps {
  content: string
  loadingState?: AiLoadingState
}

const PROGRESS_INDICATOR_MARKER = '<!-- PROGRESS_INDICATOR_HERE -->'

export const MessageContentWithProgress: React.FC<MessageContentWithProgressProps> = ({
  content,
  loadingState
}) => {
  // If no inline marker or no loading state, render normally
  if (!loadingState?.hasInlineMarker || !content.includes(PROGRESS_INDICATOR_MARKER)) {
    return (
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        components={{
          code({ node, inline, className, children, ...props }: any) {
            const match = /language-(\w+)/.exec(className || '')

            if (!inline && match) {
              const language = match[1]
              const codeContent = String(children).replace(/\n$/, '')

              // Use UnifiedCodeBlock for all code blocks (including JSON)
              return (
                <UnifiedCodeBlock
                  code={codeContent}
                  language={language || 'text'}
                  showLineNumbers={Boolean(language && ['javascript', 'typescript', 'python', 'java', 'cpp', 'c', 'css', 'html', 'json', 'vue'].includes(language))}
                  variant={language === 'json' ? 'json' : 'standard'}
                />
              )
            } else {
              return (
                <code className={`${className} bg-gray-100 px-1 py-0.5 rounded text-xs`} {...props}>
                  {children}
                </code>
              )
            }
          },
          h1: ({ children }) => <h1 className="text-lg font-bold mb-2">{children}</h1>,
          h2: ({ children }) => <h2 className="text-base font-semibold mb-1.5">{children}</h2>,
          h3: ({ children }) => <h3 className="text-sm font-medium mb-1.5">{children}</h3>,
          p: ({ children }) => <p className="mb-1.5 last:mb-0 text-sm leading-relaxed">{children}</p>,
          ul: ({ children }) => <ul className="list-disc list-inside mb-1.5 space-y-0.5 text-sm">{children}</ul>,
          ol: ({ children }) => <ol className="list-decimal list-inside mb-1.5 space-y-0.5 text-sm">{children}</ol>,
          li: ({ children }) => <li className="text-sm leading-relaxed">{children}</li>,
          blockquote: ({ children }) => (
            <blockquote className="border-l-4 border-gray-300 pl-3 italic text-gray-600 text-sm mb-2">
              {children}
            </blockquote>
          ),
          table: ({ children }) => (
            <div className="overflow-x-auto mb-2">
              <table className="min-w-full border border-gray-200 text-sm">{children}</table>
            </div>
          ),
          th: ({ children }) => (
            <th className="border border-gray-200 px-2 py-1 bg-gray-50 font-medium text-left text-xs">
              {children}
            </th>
          ),
          td: ({ children }) => (
            <td className="border border-gray-200 px-2 py-1 text-sm">{children}</td>
          ),
        }}
      >
        {content}
      </ReactMarkdown>
    )
  }

  // Split content by progress indicator marker and render with inline progress indicator
  const parts = content.split(PROGRESS_INDICATOR_MARKER)

  return (
    <div>
      {parts.map((part, index) => (
        <React.Fragment key={index}>
          {/* Render the content part */}
          {part && (
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              components={{
                code({ node, inline, className, children, ...props }: any) {
                  const match = /language-(\w+)/.exec(className || '')

                  if (!inline && match) {
                    const language = match[1]
                    const codeContent = String(children).replace(/\n$/, '')

                    // Use UnifiedCodeBlock for all code blocks (including JSON)
                    return (
                      <UnifiedCodeBlock
                        code={codeContent}
                        language={language || 'text'}
                        showLineNumbers={Boolean(language && ['javascript', 'typescript', 'python', 'java', 'cpp', 'c', 'css', 'html', 'json', 'vue'].includes(language))}
                        variant={language === 'json' ? 'json' : 'standard'}
                      />
                    )
                  } else {
                    return (
                      <code className={`${className} bg-gray-100 px-1 py-0.5 rounded text-xs`} {...props}>
                        {children}
                      </code>
                    )
                  }
                },
                h1: ({ children }) => <h1 className="text-lg font-bold mb-2">{children}</h1>,
                h2: ({ children }) => <h2 className="text-base font-semibold mb-1.5">{children}</h2>,
                h3: ({ children }) => <h3 className="text-sm font-medium mb-1.5">{children}</h3>,
                p: ({ children }) => <p className="mb-1.5 last:mb-0 text-sm leading-relaxed">{children}</p>,
                ul: ({ children }) => <ul className="list-disc list-inside mb-1.5 space-y-0.5 text-sm">{children}</ul>,
                ol: ({ children }) => <ol className="list-decimal list-inside mb-1.5 space-y-0.5 text-sm">{children}</ol>,
                li: ({ children }) => <li className="text-sm leading-relaxed">{children}</li>,
                blockquote: ({ children }) => (
                  <blockquote className="border-l-4 border-gray-300 pl-3 italic text-gray-600 text-sm mb-2">
                    {children}
                  </blockquote>
                ),
                table: ({ children }) => (
                  <div className="overflow-x-auto mb-2">
                    <table className="min-w-full border border-gray-200 text-sm">{children}</table>
                  </div>
                ),
                th: ({ children }) => (
                  <th className="border border-gray-200 px-2 py-1 bg-gray-50 font-medium text-left text-xs">
                    {children}
                  </th>
                ),
                td: ({ children }) => (
                  <td className="border border-gray-200 px-2 py-1 text-sm">{children}</td>
                ),
              }}
            >
              {part}
            </ReactMarkdown>
          )}

          {/* Render progress indicator inline if this is not the last part */}
          {index < parts.length - 1 && loadingState && loadingState.isActive && (
            <InlineLoadingIndicator
              loadingState={loadingState}
              position="inline"
              className="my-2"
            />
          )}
        </React.Fragment>
      ))}
    </div>
  )
}

export default MessageContentWithProgress
