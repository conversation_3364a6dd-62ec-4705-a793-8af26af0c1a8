/**
 * Figma Node Types and Properties Extractor (TypeScript AST Version)
 *
 * This script uses TypeScript Compiler API to parse the @figma/plugin-typings
 * definition file and extract all node types and their properties accurately.
 *
 * Usage: npx tsx scripts/extract_figma_types.ts
 */

import fs from 'fs';
import path from 'path';
import ts from 'typescript';

const __dirname = path.resolve('.');

// Type definitions
interface ProgramResult {
  program: ts.Program;
  sourceFile: ts.SourceFile;
}

interface NodeProperties {
  [nodeType: string]: string[];
}

interface MixinProperties {
  [mixinName: string]: string[];
}

interface NodeMixinInheritance {
  [nodeType: string]: string[];
}

// Paths
const PLUGIN_TYPINGS_PATH = path.join(__dirname, 'node_modules/@figma/plugin-typings/plugin-api.d.ts');
const OUTPUT_PATH = path.join(__dirname, 'src/plugin/figma_types.ts');

// Create TypeScript program and source file
function createProgram(): ProgramResult {
  if (!fs.existsSync(PLUGIN_TYPINGS_PATH)) {
    throw new Error(`Plugin typings file not found at: ${PLUGIN_TYPINGS_PATH}`);
  }

  const program = ts.createProgram([PLUGIN_TYPINGS_PATH], {
    target: ts.ScriptTarget.Latest,
    module: ts.ModuleKind.CommonJS,
    allowJs: false,
    declaration: true,
    skipLibCheck: true
  });

  const sourceFile = program.getSourceFile(PLUGIN_TYPINGS_PATH);
  if (!sourceFile) {
    throw new Error('Failed to parse TypeScript source file');
  }

  return { program, sourceFile };
}

// Convert PascalCase to SCREAMING_SNAKE_CASE
function toScreamingSnakeCase(str: string): string {
  return str
    .replace(/Node$/, '') // Remove 'Node' suffix
    .replace(/([A-Z])/g, '_$1') // Add underscore before capitals
    .toUpperCase()
    .substring(1); // Remove leading underscore
}

// Extract node types from type aliases (BaseNode, SceneNode)
function extractNodeTypesFromAST(sourceFile: ts.SourceFile): string[] {
  const nodeTypes = new Set<string>();

  function visitNode(node: ts.Node): void {
    // Look for type alias declarations like "declare type BaseNode = ..."
    if (ts.isTypeAliasDeclaration(node)) {
      const typeName = node.name.text;

      if (typeName === 'BaseNode' || typeName === 'SceneNode') {
        console.log(`📋 Found ${typeName} type alias`);

        // Extract union type members
        if (ts.isUnionTypeNode(node.type)) {
          node.type.types.forEach((typeNode: ts.TypeNode) => {
            if (ts.isTypeReferenceNode(typeNode) && ts.isIdentifier(typeNode.typeName)) {
              const nodeTypeName = typeNode.typeName.text;
              if (nodeTypeName.endsWith('Node')) {
                const constantName = toScreamingSnakeCase(nodeTypeName);
                nodeTypes.add(constantName);
                console.log(`  - Found: ${nodeTypeName} → ${constantName}`);
              }
            }
          });
        }
      }
    }

    ts.forEachChild(node, visitNode);
  }

  visitNode(sourceFile);
  return Array.from(nodeTypes).sort();
}

// Extract interface properties using AST
function extractInterfacePropertiesFromAST(sourceFile: ts.SourceFile, interfaceName: string): string[] {
  const properties = new Set<string>();

  function visitNode(node: ts.Node): void {
    // Look for interface declarations
    if (ts.isInterfaceDeclaration(node) && node.name.text === interfaceName) {
      console.log(`🔧 Extracting properties from interface: ${interfaceName}`);

      // Process each member of the interface
      node.members.forEach((member: ts.TypeElement) => {
        let propertyName: string | null = null;

        // Property signature: propertyName: type
        if (ts.isPropertySignature(member) && member.name) {
          if (ts.isIdentifier(member.name)) {
            propertyName = member.name.text;
          } else if (ts.isStringLiteral(member.name)) {
            propertyName = member.name.text;
          }
        }

        // Method signature: methodName(params): returnType
        else if (ts.isMethodSignature(member) && member.name) {
          if (ts.isIdentifier(member.name)) {
            propertyName = member.name.text;
          }
        }

        // Get accessor: get propertyName(): type
        else if (ts.isGetAccessorDeclaration(member) && member.name) {
          if (ts.isIdentifier(member.name)) {
            propertyName = member.name.text;
          }
        }

        // Set accessor: set propertyName(value: type)
        else if (ts.isSetAccessorDeclaration(member) && member.name) {
          if (ts.isIdentifier(member.name)) {
            propertyName = member.name.text;
          }
        }

        if (propertyName && propertyName !== 'type' && propertyName.length > 1) {
          properties.add(propertyName);
        }
      });
    }

    ts.forEachChild(node, visitNode);
  }

  visitNode(sourceFile);
  return Array.from(properties).sort();
}

// Discover all mixin interfaces from AST
function discoverMixinInterfacesFromAST(sourceFile: ts.SourceFile): string[] {
  const mixinNames = new Set<string>();

  function visitNode(node: ts.Node): void {
    // Look for interface declarations that end with "Mixin"
    if (ts.isInterfaceDeclaration(node)) {
      const interfaceName = node.name.text;
      if (interfaceName.endsWith('Mixin')) {
        mixinNames.add(interfaceName);
        console.log(`🔍 Discovered mixin interface: ${interfaceName}`);
      }
    }

    ts.forEachChild(node, visitNode);
  }

  visitNode(sourceFile);
  return Array.from(mixinNames).sort();
}

// Extract Mixin-Mixin inheritance relationships from AST
function extractMixinInheritanceFromAST(sourceFile: ts.SourceFile, mixinNames: string[]): NodeMixinInheritance {
  const inheritance: NodeMixinInheritance = {};

  function visitNode(node: ts.Node): void {
    // Look for interface declarations that are Mixin types
    if (ts.isInterfaceDeclaration(node)) {
      const interfaceName = node.name.text;

      // Check if this is a Mixin interface we care about
      if (mixinNames.includes(interfaceName)) {
        const inheritedMixins = new Set<string>();

        // Check heritage clauses (extends/implements)
        if (node.heritageClauses) {
          node.heritageClauses.forEach((heritageClause: ts.HeritageClause) => {
            heritageClause.types.forEach((heritageType: ts.ExpressionWithTypeArguments) => {
              if (ts.isIdentifier(heritageType.expression)) {
                const inheritedName = heritageType.expression.text;
                if (inheritedName.endsWith('Mixin') && mixinNames.includes(inheritedName)) {
                  inheritedMixins.add(inheritedName);
                  console.log(`  ${interfaceName} extends ${inheritedName}`);
                }
              }
            });
          });
        }

        if (inheritedMixins.size > 0) {
          inheritance[interfaceName] = Array.from(inheritedMixins).sort();
          console.log(`🔗 ${interfaceName} inherits from: ${Array.from(inheritedMixins).join(', ')}`);
        }
      }
    }

    ts.forEachChild(node, visitNode);
  }

  console.log('🔗 Extracting Mixin-Mixin inheritance relationships...');
  visitNode(sourceFile);
  return inheritance;
}

// Recursively resolve all inherited properties for a mixin
function resolveMixinProperties(mixinName: string, mixins: MixinProperties, mixinInheritance: NodeMixinInheritance, resolved: Set<string> = new Set()): string[] {
  // Prevent infinite recursion
  if (resolved.has(mixinName)) {
    return [];
  }
  resolved.add(mixinName);

  const directProperties = mixins[mixinName] || [];
  const inheritedMixins = mixinInheritance[mixinName] || [];

  // Recursively collect properties from inherited mixins
  const inheritedProperties: string[] = [];
  inheritedMixins.forEach((inheritedMixin: string) => {
    inheritedProperties.push(...resolveMixinProperties(inheritedMixin, mixins, mixinInheritance, new Set(resolved)));
  });

  return [...new Set([...inheritedProperties, ...directProperties])].sort();
}

// Extract mixin properties using AST
function extractMixinPropertiesFromAST(sourceFile: ts.SourceFile): MixinProperties {
  console.log('🔍 Discovering mixin interfaces from AST...');
  const mixinNames: string[] = discoverMixinInterfacesFromAST(sourceFile);

  console.log(`📋 Found ${mixinNames.length} mixin interfaces:`, mixinNames);

  // First, extract direct properties for each mixin
  const directMixins: MixinProperties = {};
  mixinNames.forEach((mixinName: string) => {
    console.log(`🔧 Extracting direct properties for mixin: ${mixinName}`);
    const properties = extractInterfacePropertiesFromAST(sourceFile, mixinName);
    if (properties.length > 0) {
      directMixins[mixinName] = properties;
      console.log(`  Found ${properties.length} direct properties`);
    } else {
      console.log(`  No direct properties found`);
    }
  });

  // Extract mixin inheritance relationships
  const mixinInheritance = extractMixinInheritanceFromAST(sourceFile, mixinNames);

  // Resolve all properties including inherited ones
  const resolvedMixins: MixinProperties = {};
  mixinNames.forEach((mixinName: string) => {
    console.log(`🔄 Resolving all properties for mixin: ${mixinName}`);
    const allProperties = resolveMixinProperties(mixinName, directMixins, mixinInheritance);
    if (allProperties.length > 0) {
      resolvedMixins[mixinName] = allProperties;
      console.log(`  Total properties (including inherited): ${allProperties.length}`);
    }
  });

  return resolvedMixins;
}

// Extract Node-Mixin inheritance relationships from AST
function extractNodeMixinInheritanceFromAST(sourceFile: ts.SourceFile, nodeTypes: string[]): NodeMixinInheritance {
  const inheritance: NodeMixinInheritance = {};

  function visitNode(node: ts.Node): void {
    // Look for interface declarations that are Node types
    if (ts.isInterfaceDeclaration(node)) {
      const interfaceName = node.name.text;

      // Check if this is a Node interface we care about
      const nodeType = nodeTypes.find(type => {
        const expectedInterfaceName = type.split('_').map((part: string) =>
          part.charAt(0) + part.slice(1).toLowerCase()
        ).join('') + 'Node';
        return interfaceName === expectedInterfaceName;
      });

      if (nodeType) {
        const mixins = new Set<string>();

        // Check heritage clauses (extends/implements)
        if (node.heritageClauses) {
          node.heritageClauses.forEach((heritageClause: ts.HeritageClause) => {
            heritageClause.types.forEach((heritageType: ts.ExpressionWithTypeArguments) => {
              if (ts.isIdentifier(heritageType.expression)) {
                const inheritedName = heritageType.expression.text;
                if (inheritedName.endsWith('Mixin')) {
                  mixins.add(inheritedName);
                  console.log(`  ${interfaceName} extends ${inheritedName}`);
                }
              }
            });
          });
        }

        if (mixins.size > 0) {
          inheritance[nodeType] = Array.from(mixins).sort();
          console.log(`🔗 ${nodeType} inherits from: ${Array.from(mixins).join(', ')}`);
        } else {
          console.log(`🔗 ${nodeType} has no mixin inheritance`);
        }
      }
    }

    ts.forEachChild(node, visitNode);
  }

  console.log('🔗 Extracting Node-Mixin inheritance relationships...');
  visitNode(sourceFile);
  return inheritance;
}

// Generate TypeScript output
function generateOutput(nodeTypes: string[], nodeProperties: NodeProperties, mixins: MixinProperties, inheritance: NodeMixinInheritance): string {
  const output = `// Auto-generated Figma Node Types and Properties
// Generated on: ${new Date().toISOString()}
// Source: @figma/plugin-typings

/**
 * All Figma Node Types
 */
export const FIGMA_NODE_TYPES = [
${nodeTypes.map((type: string) => `  '${type}'`).join(',\n')}
] as const;

export type FigmaNodeType = typeof FIGMA_NODE_TYPES[number];

/**
 * Common Mixin Properties
 */
export const FIGMA_MIXINS = {
${Object.entries(mixins).map(([name, props]: [string, string[]]) =>
  `  ${name}: [\n${props.map((prop: string) => `    '${prop}'`).join(',\n')}\n  ]`
).join(',\n\n')}
} as const;

/**
 * Node-Mixin Inheritance Relationships
 */
export const FIGMA_NODE_MIXINS = {
${Object.entries(inheritance).map(([nodeType, mixinList]: [string, string[]]) =>
  `  ${nodeType}: [\n${mixinList.map((mixin: string) => `    '${mixin}'`).join(',\n')}\n  ]`
).join(',\n\n')}
} as const;

/**
 * Node-specific Properties
 * Note: These are in addition to inherited mixin properties
 */
export const FIGMA_NODE_PROPERTIES = {
${Object.entries(nodeProperties).map(([nodeType, props]: [string, string[]]) =>
  `  ${nodeType}: [\n${props.map((prop: string) => `    '${prop}'`).join(',\n')}\n  ]`
).join(',\n\n')}
} as const;

/**
 * Get all properties for a specific node type (including inherited mixins)
 */
export function get_node_properties(node_type: FigmaNodeType): string[] {
  const inherited_mixins = FIGMA_NODE_MIXINS[node_type as keyof typeof FIGMA_NODE_MIXINS] || [];
  const specific_props = FIGMA_NODE_PROPERTIES[node_type as keyof typeof FIGMA_NODE_PROPERTIES] || [];

  // Collect all properties from inherited mixins
  const mixin_props: string[] = [];
  inherited_mixins.forEach((mixinName: string) => {
    const mixinProps = FIGMA_MIXINS[mixinName as keyof typeof FIGMA_MIXINS] || [];
    mixin_props.push(...mixinProps);
  });

  return [...new Set([...mixin_props, ...specific_props])].sort();
}

/**
 * Check if a node type has a specific property
 */
export function has_node_property(node_type: FigmaNodeType, property: string): boolean {
  return get_node_properties(node_type).includes(property);
}
`;

  return output;
}

// Main execution using TypeScript AST
function main(): void {
  try {
    console.log('🔍 Creating TypeScript program...');
    const { sourceFile } = createProgram();

    console.log('📋 Extracting node types from AST...');
    const nodeTypes: string[] = extractNodeTypesFromAST(sourceFile);
    console.log(`Found ${nodeTypes.length} node types:`, nodeTypes);

    console.log('🔧 Extracting mixin properties from AST...');
    const mixins: MixinProperties = extractMixinPropertiesFromAST(sourceFile);

    console.log('� Extracting Node-Mixin inheritance relationships...');
    const inheritance: NodeMixinInheritance = extractNodeMixinInheritanceFromAST(sourceFile, nodeTypes);

    console.log('�📝 Extracting node-specific properties from AST...');
    const nodeProperties: NodeProperties = {};

    nodeTypes.forEach((nodeType: string) => {
      // Convert SCREAMING_SNAKE_CASE back to PascalCase + Node
      const interfaceName = nodeType.split('_').map((part: string) =>
        part.charAt(0) + part.slice(1).toLowerCase()
      ).join('') + 'Node';

      console.log(`  Processing ${nodeType} → ${interfaceName}`);
      const props: string[] = extractInterfacePropertiesFromAST(sourceFile, interfaceName);
      if (props.length > 0) {
        nodeProperties[nodeType] = props;
        console.log(`    Found ${props.length} properties`);
      } else {
        console.log(`    No specific properties found`);
      }
    });

    console.log('📄 Generating output file...');
    const output: string = generateOutput(nodeTypes, nodeProperties, mixins, inheritance);

    // Ensure output directory exists
    const outputDir = path.dirname(OUTPUT_PATH);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    fs.writeFileSync(OUTPUT_PATH, output, 'utf8');

    console.log('✅ Successfully generated:', OUTPUT_PATH);
    console.log(`📊 Summary: ${nodeTypes.length} node types, ${Object.keys(nodeProperties).length} with specific properties`);

    // Show mixin summary
    const mixinSummary = Object.entries(mixins).map(([name, props]: [string, string[]]) => `${name}: ${props.length}`).join(', ');
    console.log(`🔧 Mixins: ${mixinSummary}`);

  } catch (error: unknown) {
    if (error instanceof Error) {
      console.error('❌ Error:', error.message);
      console.error(error.stack);
    } else {
      console.error('❌ Unknown error:', error);
    }
    process.exit(1);
  }
}

// Run the script
main();
