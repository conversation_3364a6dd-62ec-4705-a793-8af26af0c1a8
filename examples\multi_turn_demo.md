# Multi-Turn Tool Calling Demo

This document demonstrates the new multi-turn tool calling capabilities in the Figma Agent.

## Before: Single-Turn Limitation

**Previous Behavior:**
```
User: "Analyze my Figma selection and suggest improvements"

AI: [Calls get_figma_selection_json]
Tool Result: { "figma": { "id": "123", "type": "FRAME", ... } }

AI Response: "I executed the tool to get your selection data."
❌ CONVERSATION ENDS - AI cannot analyze the results!
```

## After: Multi-Turn Capability

**New Behavior:**
```
User: "Analyze my Figma selection and suggest improvements"

Turn 1:
AI: [Calls get_figma_selection_json]
Tool Result: { "figma": { "id": "123", "type": "FRAME", "width": 300, "height": 200, ... } }

Turn 2:
AI: [Analyzes the data] "I can see you have a 300x200 frame. Let me get the CSS styles too."
AI: [Calls get_figma_selection_css]
Tool Result: { "background-color": "#ffffff", "border-radius": "8px", ... }

Turn 3:
AI: "Based on your selection analysis:
- Frame dimensions: 300x200px
- Clean white background with rounded corners
- Suggestions:
  1. Consider adding subtle shadows for depth
  2. The aspect ratio works well for card layouts
  3. Border radius could be increased to 12px for modern look"
✅ COMPLETE ANALYSIS WITH ACTIONABLE INSIGHTS!
```

## Usage Examples

### 1. Command System
```
/ai Analyze my selection and export it as CSS
```

The AI will:
1. Call `get_figma_selection_json` to understand the structure
2. Call `get_figma_selection_css` to get styling
3. Provide formatted CSS output with explanations

### 2. Chat Interface
Simply type in the chat:
```
"Get my selection data and convert it to Tailwind classes"
```

The AI will:
1. Retrieve selection data
2. Analyze the styles
3. Generate appropriate Tailwind CSS classes
4. Explain the conversion choices

### 3. Complex Workflows
```
"Help me optimize my design for mobile"
```

The AI might:
1. Get selection JSON to understand layout
2. Get CSS to analyze current styling
3. Suggest responsive improvements
4. Provide specific code recommendations

## Technical Features

### Multi-Turn Execution
- **Automatic**: AI decides when to continue or stop
- **Intelligent**: Each turn builds on previous results
- **Limited**: Max 5 turns prevents infinite loops

### Enhanced UI Feedback
- **Progress Indicators**: Shows current turn (Turn 2/5)
- **Tool Execution**: Real-time tool call results
- **Streaming**: Live updates during processing

### Error Handling
- **Graceful Degradation**: Tool failures don't stop conversation
- **Recovery**: AI can try alternative approaches
- **User Feedback**: Clear error messages and suggestions

## Configuration

### Max Turns
```typescript
// Default: 5 turns
await unifiedAiApi.sendMultiTurnStreamingMessage("Your message", 3)
```

### Callbacks
```typescript
await unifiedAiApi.sendMultiTurnMessage(
  "Your message",
  5, // max turns
  (toolCall, result) => {
    console.log(`Tool: ${toolCall.function.name}`, result)
  },
  (turn, response) => {
    console.log(`Turn ${turn} completed`)
  }
)
```

## Benefits

### For Users
- **Smarter AI**: Can perform complex multi-step analysis
- **Better Results**: More thorough and actionable responses
- **Seamless Experience**: Works automatically without extra commands

### For Developers
- **Powerful Workflows**: Enable sophisticated design automation
- **Flexible Architecture**: Easy to extend with new tools
- **Robust Error Handling**: Graceful failure recovery

## Real-World Scenarios

### Design Analysis
```
User: "Review my component design"
AI: Gets selection → Analyzes structure → Checks accessibility → Suggests improvements
```

### Code Generation
```
User: "Generate React component from my design"
AI: Gets selection → Extracts styles → Generates JSX → Provides usage examples
```

### Design System Integration
```
User: "Check if this matches our design system"
AI: Gets selection → Compares with standards → Identifies discrepancies → Suggests fixes
```

## Migration Guide

### Existing Code
No changes needed! All existing functionality continues to work.

### New Features
- Use `sendMultiTurnStreamingMessage()` for enhanced experience
- Enable in ChatTab and command system automatically
- Backward compatible with single-turn tools

## Performance

### API Usage
- Each turn consumes tokens
- Max turns limit controls costs
- Efficient tool result formatting

### User Experience
- Real-time progress feedback
- Abort capability during execution
- Smooth streaming updates

## Future Enhancements

### Planned Features
- **Parallel Tool Execution**: Run multiple tools simultaneously
- **Custom Turn Limits**: Per-tool or per-user configuration
- **Advanced Progress Tracking**: Detailed execution metrics
- **Tool Call Batching**: Optimize API usage

### Extensibility
- Plugin system for custom strategies
- Configurable conversation patterns
- Advanced error recovery mechanisms

---

**Try it now!** Open the Figma plugin, select any design element, and ask the AI to analyze it. Watch as it performs multiple tool calls to give you comprehensive insights!
