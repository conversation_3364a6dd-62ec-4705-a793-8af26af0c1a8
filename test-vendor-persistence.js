// Test script for vendor persistence
// Run this in the browser console to test vendor selection persistence

console.log('🧪 Testing vendor persistence...');

// Helper function to get all figmagent storage keys
function getFigmaAgentStorageKeys() {
  const keys = [];
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key?.startsWith('figmagent-mock-')) {
      keys.push({
        key: key.replace('figmagent-mock-', ''),
        fullKey: key,
        value: localStorage.getItem(key)
      });
    }
  }
  return keys;
}

// Test 1: Check current storage state
console.log('📋 Current FigmaAgent storage:');
const storageKeys = getFigmaAgentStorageKeys();
storageKeys.forEach(item => {
  console.log(`  ${item.key}:`, item.value);
});

// Test 2: Check if vendor key exists
const vendorKey = storageKeys.find(item => item.key === 'figmagent-ai-vendor');
if (vendorKey) {
  console.log('✅ Vendor key found:', vendorKey.value);
} else {
  console.log('❌ Vendor key not found in storage');
}

// Test 3: Manually set vendor to test persistence
function testVendorPersistence(vendor) {
  console.log(`🔄 Setting vendor to: ${vendor}`);
  localStorage.setItem('figmagent-mock-figmagent-ai-vendor', JSON.stringify(vendor));
  console.log('💾 Vendor saved to storage');
  console.log('🔄 Refresh the page to test if vendor persists');
}

// Test 4: Clear vendor storage
function clearVendorStorage() {
  console.log('🧹 Clearing vendor storage...');
  localStorage.removeItem('figmagent-mock-figmagent-ai-vendor');
  console.log('✅ Vendor storage cleared');
}

// Make functions available globally for testing
window.testVendorPersistence = testVendorPersistence;
window.clearVendorStorage = clearVendorStorage;
window.getFigmaAgentStorageKeys = getFigmaAgentStorageKeys;

console.log('🛠️ Test functions available:');
console.log('  testVendorPersistence("deepseek") - Set vendor to DeepSeek');
console.log('  testVendorPersistence("openrouter") - Set vendor to OpenRouter');
console.log('  clearVendorStorage() - Clear vendor storage');
console.log('  getFigmaAgentStorageKeys() - Get all storage keys');
