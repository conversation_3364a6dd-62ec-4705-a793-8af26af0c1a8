# Scripts Directory

This directory contains various scripts for the Figmagent project, including dependency installation, Zenoh daemon management, and Figma types extraction.

## Installation Scripts

### 🔧 `install_zenoh_plugin_remote_api.py`
Cross-platform Python script that automatically detects the zenohd installation location and installs the remote API plugin DLL to the same directory.

**Usage:**
```bash
# Via npm script (recommended)
npm run install:zenoh

# Or run directly
/d/msys64/home/<USER>/micromamba/envs/figmagent/python scripts/install_zenoh_plugin_remote_api.py
```

**Features:**
- ✅ Automatic zenohd location detection using `which`/`where` commands
- ✅ Cross-platform path handling (Unix/Windows style paths)
- ✅ Installs DLL to the same directory as zenohd executable
- ✅ Cross-platform compatibility (Windows, macOS, Linux)
- ✅ Clear error messages and status reporting
- ✅ File size verification and permission checks

**What it does:**
1. Detects zenohd executable location using system commands
2. Converts Unix-style paths to Windows paths when needed
3. Copies `deps/zenoh_plugin_remote_api.dll` → `{zenohd_directory}/zenoh_plugin_remote_api.dll`
4. Verifies source file exists and target directory is writable
5. Confirms successful copy operation and reports file size

## Zenoh Daemon Management

### 🚀 `start_zenohd.sh`
Bash script to start the Zenoh daemon with the correct configuration.

**Usage:**
```bash
# Run from anywhere (script auto-detects its location)
./scripts/start_zenohd.sh
```

**Features:**
- ✅ Auto-detects script directory for configuration file
- ✅ Checks for zenohd installation
- ✅ Validates configuration file exists
- ✅ Provides helpful installation instructions if zenohd is missing

### ⚙️ `zenoh-config.json5`
Zenoh daemon configuration file with WebSocket support on port 10000.

## Type Extraction Scripts

### `extract_figma_types.ts`

Uses **TypeScript AST parsing** to accurately extract Figma plugin typings and generates a comprehensive TypeScript file containing:

- **All Figma Node Types**: Complete list of all available node types
- **Mixin Properties**: Common properties inherited by multiple node types
- **Node-specific Properties**: Unique properties for each node type
- **Utility Functions**: Helper functions to query node properties

#### Usage

```bash
# Run the extractor
npm run extract-types

# Or run directly
npx tsx scripts/extract_figma_types.ts
```

#### Output

The script generates `src/plugin/figma_types.ts` with:

```typescript
// All node types
export const FIGMA_NODE_TYPES = [
  'DOCUMENT', 'PAGE', 'FRAME', 'TEXT', ...
] as const;

// Mixin properties (inherited by multiple node types)
export const FIGMA_MIXINS = {
  BaseNodeMixin: ['id', 'name', 'parent', ...],
  SceneNodeMixin: ['visible', 'locked', ...],
  // ... more mixins
};

// Node-specific properties
export const FIGMA_NODE_PROPERTIES = {
  FRAME: ['clone', 'children', ...],
  TEXT: ['characters', 'fontSize', ...],
  // ... all node types
};

// Utility functions (snake_case naming for backend code)
export function get_node_properties(node_type: FigmaNodeType): string[]
export function has_node_property(node_type: FigmaNodeType, property: string): boolean
```

#### Features

- **TypeScript AST Parsing**: Uses TypeScript Compiler API for 100% accurate parsing
- **No Regex Dependencies**: Eliminates regex-based text processing errors
- **Complete Coverage**: Extracts all properties from TypeScript interfaces accurately
- **Mixin Support**: Properly handles inherited properties from mixins
- **Type Safety**: Generates TypeScript types for better development experience
- **Utility Functions**: Provides helper functions for property queries
- **Robust Error Handling**: Better error reporting and debugging

#### How It Works

1. **Creates TypeScript Program**: Uses `ts.createProgram()` to parse the definition file
2. **AST Traversal**: Walks the Abstract Syntax Tree to find interface declarations
3. **Type Union Extraction**: Extracts node types from `BaseNode` and `SceneNode` type unions
4. **Interface Analysis**: Analyzes each interface to extract properties, methods, and accessors
5. **Mixin Processing**: Identifies and separates mixin properties from node-specific properties
6. **Code Generation**: Generates clean TypeScript output with proper type definitions

#### Advantages over Regex-based Parsing

- **Accuracy**: No false positives or missed properties due to complex syntax
- **Maintainability**: Easier to understand and modify the extraction logic
- **Robustness**: Handles TypeScript syntax changes and edge cases correctly
- **Completeness**: Captures all property types (properties, methods, getters, setters)

#### Maintenance

The script should be run whenever:
- The `@figma/plugin-typings` package is updated
- New Figma features introduce new node types or properties
- You want to ensure the property definitions are up-to-date

#### Error Handling

The script includes robust error handling for:
- Missing typings file
- Malformed TypeScript interfaces
- Parsing errors
- File system issues

All errors are reported with clear messages to help with debugging.
