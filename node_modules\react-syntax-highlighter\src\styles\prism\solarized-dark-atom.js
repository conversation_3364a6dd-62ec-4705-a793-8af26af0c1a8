export default {
    "code[class*=\"language-\"]": {
        "color": "#839496",
        "textShadow": "0 1px rgba(0, 0, 0, 0.3)",
        "fontFamily": "Inconsolata, Monaco, Consolas, 'Courier New', Courier, monospace",
        "direction": "ltr",
        "textAlign": "left",
        "whiteSpace": "pre",
        "wordSpacing": "normal",
        "wordBreak": "normal",
        "lineHeight": "1.5",
        "MozTabSize": "4",
        "OTabSize": "4",
        "tabSize": "4",
        "WebkitHyphens": "none",
        "MozHyphens": "none",
        "msHyphens": "none",
        "hyphens": "none"
    },
    "pre[class*=\"language-\"]": {
        "color": "#839496",
        "textShadow": "0 1px rgba(0, 0, 0, 0.3)",
        "fontFamily": "Inconsolata, Monaco, Consol<PERSON>, 'Courier New', Courier, monospace",
        "direction": "ltr",
        "textAlign": "left",
        "whiteSpace": "pre",
        "wordSpacing": "normal",
        "wordBreak": "normal",
        "lineHeight": "1.5",
        "MozTabSize": "4",
        "OTabSize": "4",
        "tabSize": "4",
        "WebkitHyphens": "none",
        "MozHyphens": "none",
        "msHyphens": "none",
        "hyphens": "none",
        "padding": "1em",
        "margin": ".5em 0",
        "overflow": "auto",
        "borderRadius": "0.3em",
        "background": "#002b36"
    },
    ":not(pre) > code[class*=\"language-\"]": {
        "background": "#002b36",
        "padding": ".1em",
        "borderRadius": ".3em"
    },
    "comment": {
        "color": "#586e75"
    },
    "prolog": {
        "color": "#586e75"
    },
    "doctype": {
        "color": "#586e75"
    },
    "cdata": {
        "color": "#586e75"
    },
    "punctuation": {
        "color": "#93a1a1"
    },
    ".namespace": {
        "Opacity": ".7"
    },
    "property": {
        "color": "#268bd2"
    },
    "keyword": {
        "color": "#268bd2"
    },
    "tag": {
        "color": "#268bd2"
    },
    "class-name": {
        "color": "#FFFFB6",
        "textDecoration": "underline"
    },
    "boolean": {
        "color": "#b58900"
    },
    "constant": {
        "color": "#b58900"
    },
    "symbol": {
        "color": "#dc322f"
    },
    "deleted": {
        "color": "#dc322f"
    },
    "number": {
        "color": "#859900"
    },
    "selector": {
        "color": "#859900"
    },
    "attr-name": {
        "color": "#859900"
    },
    "string": {
        "color": "#859900"
    },
    "char": {
        "color": "#859900"
    },
    "builtin": {
        "color": "#859900"
    },
    "inserted": {
        "color": "#859900"
    },
    "variable": {
        "color": "#268bd2"
    },
    "operator": {
        "color": "#EDEDED"
    },
    "function": {
        "color": "#268bd2"
    },
    "regex": {
        "color": "#E9C062"
    },
    "important": {
        "color": "#fd971f",
        "fontWeight": "bold"
    },
    "entity": {
        "color": "#FFFFB6",
        "cursor": "help"
    },
    "url": {
        "color": "#96CBFE"
    },
    ".language-css .token.string": {
        "color": "#87C38A"
    },
    ".style .token.string": {
        "color": "#87C38A"
    },
    "bold": {
        "fontWeight": "bold"
    },
    "italic": {
        "fontStyle": "italic"
    },
    "atrule": {
        "color": "#F9EE98"
    },
    "attr-value": {
        "color": "#F9EE98"
    }
}