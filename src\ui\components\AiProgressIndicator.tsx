import React, { useEffect, useState } from 'react'
import { useAiProgress } from '../store/hooks'

interface AiProgressIndicatorProps {
  className?: string
}

export const AiProgressIndicator: React.FC<AiProgressIndicatorProps> = ({ className = '' }) => {
  const { progress } = useAiProgress()
  const [dots, setDots] = useState('')
  const [elapsedTime, setElapsedTime] = useState(0)
  const [isTimedOut, setIsTimedOut] = useState(false)

  // Timeout handling
  useEffect(() => {
    if (!progress.isActive || progress.isTimedOut) {
      setIsTimedOut(progress.isTimedOut || false)
      return
    }

    const timeoutMs = progress.timeoutMs || 60000 // Default 60 seconds
    const timeoutId = setTimeout(() => {
      setIsTimedOut(true)
    }, timeoutMs)

    return () => clearTimeout(timeoutId)
  }, [progress.isActive, progress.startTime, progress.timeoutMs, progress.isTimedOut])

  // Animated ellipsis effect
  useEffect(() => {
    if (!progress.isActive || isTimedOut) {
      setDots('')
      return
    }

    const interval = setInterval(() => {
      setDots(prev => {
        if (prev === '...') return ''
        return prev + '.'
      })
    }, 500)

    return () => clearInterval(interval)
  }, [progress.isActive, isTimedOut])

  // Elapsed time tracking
  useEffect(() => {
    if (!progress.isActive || !progress.startTime) {
      setElapsedTime(0)
      return
    }

    const interval = setInterval(() => {
      // Ensure startTime is a Date object (handle JSON deserialization)
      const startTime = progress.startTime instanceof Date
        ? progress.startTime
        : new Date(progress.startTime)

      const elapsed = Math.floor((Date.now() - startTime.getTime()) / 1000)
      setElapsedTime(elapsed)
    }, 1000)

    return () => clearInterval(interval)
  }, [progress.isActive, progress.startTime])

  if (!progress.isActive && !isTimedOut) {
    return null
  }

  const getPhaseIcon = () => {
    if (isTimedOut || progress.phase === 'timeout') {
      return '⏰'
    }

    switch (progress.phase) {
      case 'initializing':
        return '🚀'
      case 'thinking':
        return '🤔'
      case 'tool_execution':
        return '🔧'
      case 'analyzing':
        return '📊'
      case 'completing':
        return '✨'
      default:
        return '⚡'
    }
  }

  const getStatusMessage = () => {
    if (isTimedOut || progress.phase === 'timeout') {
      return 'Operation timed out'
    }
    return progress.statusMessage
  }

  const getProgressPercentage = () => {
    // Show indeterminate progress since turn counts are unpredictable
    return progress.isActive ? 50 : 0
  }

  const formatElapsedTime = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}m ${remainingSeconds}s`
  }

  return (
    <div className={`ai-progress-indicator ${isTimedOut ? 'timeout' : ''} ${className}`}>
      {/* Main Progress Bar - Indeterminate Animation */}
      <div className="progress-container">
        <div className="progress-bar">
          <div className="progress-fill indeterminate" />
        </div>
      </div>

      {/* Status Message */}
      <div className="status-message">
        <span className="phase-icon">{getPhaseIcon()}</span>
        <span className="status-text">
          {getStatusMessage()}{!isTimedOut ? dots : ''}
        </span>

        {/* Tool Execution Badge */}
        {progress.currentTool && (
          <span className="tool-badge">
            {progress.currentTool}
          </span>
        )}
      </div>

      {/* Additional Info */}
      <div className="progress-details">
        {progress.toolsExecuted > 0 && (
          <span className="tools-count">
            {progress.toolsExecuted} tool{progress.toolsExecuted !== 1 ? 's' : ''} executed
          </span>
        )}

        {elapsedTime > 0 && (
          <span className="elapsed-time">
            {formatElapsedTime(elapsedTime)}
          </span>
        )}
      </div>

      <style jsx>{`
        .ai-progress-indicator {
          background: rgba(0, 0, 0, 0.05);
          border: 1px solid rgba(0, 0, 0, 0.1);
          border-radius: 8px;
          padding: 12px;
          margin: 8px 0;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          animation: fadeIn 0.3s ease-in-out;
        }

        .ai-progress-indicator.timeout {
          background: rgba(239, 68, 68, 0.05);
          border-color: rgba(239, 68, 68, 0.2);
        }

        .timeout .progress-fill {
          background: linear-gradient(90deg, #dc2626, #ef4444);
          animation: none;
          width: 100%;
        }

        .timeout .phase-icon {
          animation: none;
        }

        .timeout .status-text {
          color: #dc2626;
        }

        .progress-container {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;
        }

        .progress-bar {
          flex: 1;
          height: 4px;
          background: rgba(0, 0, 0, 0.1);
          border-radius: 2px;
          overflow: hidden;
        }

        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, #007AFF, #5AC8FA);
          border-radius: 2px;
        }

        .progress-fill.indeterminate {
          width: 30%;
          animation: indeterminate 2s ease-in-out infinite;
        }

        .status-message {
          display: flex;
          align-items: center;
          gap: 6px;
          margin-bottom: 6px;
        }

        .phase-icon {
          font-size: 14px;
          animation: pulse 2s infinite;
        }

        .status-text {
          font-size: 13px;
          color: #333;
          font-weight: 500;
          flex: 1;
        }

        .tool-badge {
          font-size: 10px;
          background: #007AFF;
          color: white;
          padding: 2px 6px;
          border-radius: 10px;
          font-weight: 600;
        }

        .progress-details {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 11px;
          color: #666;
        }

        .tools-count,
        .elapsed-time {
          background: rgba(0, 0, 0, 0.05);
          padding: 2px 6px;
          border-radius: 8px;
        }

        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(-4px); }
          to { opacity: 1; transform: translateY(0); }
        }

        @keyframes indeterminate {
          0% { transform: translateX(-100%); }
          50% { transform: translateX(0%); }
          100% { transform: translateX(300%); }
        }

        @keyframes pulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.1); }
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
          .ai-progress-indicator {
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(255, 255, 255, 0.1);
          }

          .progress-bar {
            background: rgba(255, 255, 255, 0.1);
          }

          .turn-counter {
            background: rgba(255, 255, 255, 0.1);
            color: #ccc;
          }

          .status-text {
            color: #fff;
          }

          .progress-details {
            color: #ccc;
          }

          .tools-count,
          .elapsed-time {
            background: rgba(255, 255, 255, 0.1);
          }
        }
      `}</style>
    </div>
  )
}

export default AiProgressIndicator
