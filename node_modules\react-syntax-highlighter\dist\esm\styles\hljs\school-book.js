export default {
  "hljs": {
    "display": "block",
    "overflowX": "auto",
    "padding": "15px 0.5em 0.5em 30px",
    "fontSize": "11px",
    "lineHeight": "16px",
    "background": "#f6f6ae url(./school-book.png)",
    "borderTop": "solid 2px #d2e8b9",
    "borderBottom": "solid 1px #d2e8b9",
    "color": "#3e5915"
  },
  "hljs-keyword": {
    "color": "#005599",
    "fontWeight": "bold"
  },
  "hljs-selector-tag": {
    "color": "#005599",
    "fontWeight": "bold"
  },
  "hljs-literal": {
    "color": "#005599",
    "fontWeight": "bold"
  },
  "hljs-subst": {
    "color": "#3e5915"
  },
  "hljs-string": {
    "color": "#2c009f"
  },
  "hljs-title": {
    "color": "#2c009f",
    "fontWeight": "bold"
  },
  "hljs-section": {
    "color": "#2c009f",
    "fontWeight": "bold"
  },
  "hljs-type": {
    "color": "#2c009f",
    "fontWeight": "bold"
  },
  "hljs-symbol": {
    "color": "#2c009f"
  },
  "hljs-bullet": {
    "color": "#2c009f"
  },
  "hljs-attribute": {
    "color": "#2c009f"
  },
  "hljs-built_in": {
    "color": "#2c009f"
  },
  "hljs-builtin-name": {
    "color": "#2c009f"
  },
  "hljs-addition": {
    "color": "#2c009f"
  },
  "hljs-variable": {
    "color": "#2c009f"
  },
  "hljs-template-tag": {
    "color": "#2c009f"
  },
  "hljs-template-variable": {
    "color": "#2c009f"
  },
  "hljs-link": {
    "color": "#2c009f"
  },
  "hljs-comment": {
    "color": "#e60415"
  },
  "hljs-quote": {
    "color": "#e60415"
  },
  "hljs-deletion": {
    "color": "#e60415"
  },
  "hljs-meta": {
    "color": "#e60415"
  },
  "hljs-doctag": {
    "fontWeight": "bold"
  },
  "hljs-name": {
    "fontWeight": "bold"
  },
  "hljs-selector-id": {
    "fontWeight": "bold"
  },
  "hljs-strong": {
    "fontWeight": "bold"
  },
  "hljs-emphasis": {
    "fontStyle": "italic"
  }
};