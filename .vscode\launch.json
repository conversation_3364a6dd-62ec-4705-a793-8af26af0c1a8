{"version": "0.2.0", "configurations": [{"name": "Launch Web Dev Server", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/vite", "args": ["--config", "vite.config.ui.ts", "--mode", "development"], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "env": {"NODE_ENV": "development"}}, {"name": "Build Plugin (Production)", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/concurrently", "args": ["\"vite build --config vite.config.plugin.ts\"", "\"vite build --config vite.config.ui.ts\""], "cwd": "${workspaceFolder}", "console": "integratedTerminal"}, {"name": "Build Plugin (Staging)", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/concurrently", "args": ["\"vite build --config vite.config.plugin.ts --mode staging\"", "\"vite build --config vite.config.ui.ts --mode staging\""], "cwd": "${workspaceFolder}", "console": "integratedTerminal"}, {"name": "Type Check", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/tsc", "args": ["-p", "tsconfig.json"], "cwd": "${workspaceFolder}", "console": "integratedTerminal"}, {"name": "ESLint Check", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/eslint", "args": ["--ext", ".ts,.tsx", "--ignore-pattern", "node_modules", "."], "cwd": "${workspaceFolder}", "console": "integratedTerminal"}, {"name": "Update OpenRouter Models", "type": "python", "request": "launch", "program": "${workspaceFolder}/scripts/update_openrouter_models.py", "cwd": "${workspaceFolder}", "console": "integratedTerminal"}]}