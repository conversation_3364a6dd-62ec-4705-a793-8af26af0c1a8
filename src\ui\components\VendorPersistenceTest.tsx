// Vendor Persistence Test Component
// Temporary component for testing vendor selection persistence

import React, { useEffect, useState } from 'react'
import { useVendorStorage } from '../store'
import { AIVendor } from '../types/ui_types'

export const VendorPersistenceTest: React.FC = () => {
  const vendorStorage = useVendorStorage()
  const [testResults, setTestResults] = useState<string[]>([])

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`])
  }

  useEffect(() => {
    addTestResult(`Initial vendor: ${vendorStorage.vendor}`)
    addTestResult(`Loading state: ${vendorStorage.loadingVendor}`)
    if (vendorStorage.loadError) {
      addTestResult(`Load error: ${vendorStorage.loadError}`)
    }

    // Check storage immediately
    const stored = localStorage.getItem('figmagent-mock-figmagent-ai-vendor')
    if (stored) {
      addTestResult(`Storage contains: ${stored}`)
    } else {
      addTestResult(`No storage value found`)
    }
  }, [vendorStorage.vendor, vendorStorage.loadingVendor, vendorStorage.loadError])

  const testVendorChange = async (newVendor: AIVendor) => {
    addTestResult(`Testing vendor change to: ${newVendor}`)
    try {
      addTestResult(`Before change - Current vendor: ${vendorStorage.vendor}`)
      await vendorStorage.saveVendor(newVendor)
      addTestResult(`After change - Current vendor: ${vendorStorage.vendor}`)
      addTestResult(`✅ Successfully changed vendor to: ${newVendor}`)

      // Check storage immediately after save
      setTimeout(() => {
        const stored = localStorage.getItem('figmagent-mock-figmagent-ai-vendor')
        addTestResult(`Storage after save: ${stored}`)
      }, 100)
    } catch (error) {
      addTestResult(`❌ Failed to change vendor: ${error}`)
    }
  }

  const clearStorage = () => {
    addTestResult('Clearing localStorage...')
    localStorage.removeItem('figmagent-mock-figmagent-ai-vendor')
    addTestResult('✅ Storage cleared - refresh page to test')
  }

  const checkStorage = () => {
    const stored = localStorage.getItem('figmagent-mock-figmagent-ai-vendor')
    addTestResult(`Storage value: ${stored || 'null'}`)
    if (stored) {
      try {
        const parsed = JSON.parse(stored)
        addTestResult(`Parsed value: ${parsed}`)
      } catch (error) {
        addTestResult(`Parse error: ${error}`)
      }
    }
  }

  const reloadStorage = async () => {
    addTestResult('Manually reloading vendor storage...')
    try {
      await vendorStorage.reload()
      addTestResult(`After reload - Current vendor: ${vendorStorage.vendor}`)
    } catch (error) {
      addTestResult(`❌ Failed to reload: ${error}`)
    }
  }

  return (
    <div className="p-4 border border-gray-300 rounded-lg bg-gray-50">
      <h3 className="text-lg font-bold mb-3">🧪 Vendor Persistence Test</h3>

      <div className="mb-4">
        <p><strong>Current Vendor:</strong> {vendorStorage.vendor}</p>
        <p><strong>Loading:</strong> {vendorStorage.loadingVendor ? 'Yes' : 'No'}</p>
        <p><strong>Error:</strong> {vendorStorage.loadError || 'None'}</p>
      </div>

      <div className="flex gap-2 mb-4">
        <button
          onClick={() => testVendorChange('openrouter')}
          className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Set OpenRouter
        </button>
        <button
          onClick={() => testVendorChange('deepseek')}
          className="px-3 py-1 bg-purple-500 text-white rounded hover:bg-purple-600"
        >
          Set DeepSeek
        </button>
        <button
          onClick={checkStorage}
          className="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600"
        >
          Check Storage
        </button>
        <button
          onClick={clearStorage}
          className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600"
        >
          Clear Storage
        </button>
        <button
          onClick={reloadStorage}
          className="px-3 py-1 bg-yellow-500 text-white rounded hover:bg-yellow-600"
        >
          Reload Storage
        </button>
      </div>

      <div className="bg-white p-3 rounded border max-h-40 overflow-y-auto">
        <h4 className="font-semibold mb-2">Test Results:</h4>
        {testResults.length === 0 ? (
          <p className="text-gray-500">No test results yet...</p>
        ) : (
          <ul className="text-sm space-y-1">
            {testResults.map((result, index) => (
              <li key={index} className="font-mono">{result}</li>
            ))}
          </ul>
        )}
      </div>

      <div className="mt-3 text-sm text-gray-600">
        <p><strong>Instructions:</strong></p>
        <ol className="list-decimal list-inside space-y-1">
          <li>Click "Set DeepSeek" or "Set OpenRouter" to change vendor</li>
          <li>Check that the vendor changes in the UI</li>
          <li>Refresh the page (F5)</li>
          <li>Verify that the vendor selection persists after refresh</li>
        </ol>
      </div>
    </div>
  )
}
