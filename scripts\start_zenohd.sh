#!/bin/bash

# FigmAgent - <PERSON>oh Daemon Startup Script
# This script helps you start the Zenoh daemon with the correct configuration

echo "🚀 Starting FigmAgent Zenoh Daemon..."
echo ""

# Check if zenohd is installed
if ! command -v zenohd &> /dev/null; then
    echo "❌ zenohd is not installed or not in PATH"
    echo ""
    echo "Please install Zenoh first:"
    echo ""
    echo "Ubuntu/Debian:"
    echo "  echo \"deb [trusted=yes] https://download.eclipse.org/zenoh/debian-repo/ /\" | sudo tee -a /etc/apt/sources.list.d/zenoh.list > /dev/null"
    echo "  sudo apt update"
    echo "  sudo apt install zenohd zenoh-plugin-remote-api"
    echo ""
    echo "macOS:"
    echo "  brew tap eclipse-zenoh/homebrew-zenoh"
    echo "  brew install zenoh zenoh-plugin-remote-api"
    echo ""
    exit 1
fi

# Get script directory and config file path
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/zenoh-config.json5"

# Check if config file exists
if [ ! -f "$CONFIG_FILE" ]; then
    echo "❌ Configuration file 'zenoh-config.json5' not found at: $CONFIG_FILE"
    echo "Please make sure the configuration file exists in the scripts directory"
    exit 1
fi

echo "✅ Found zenohd installation"
echo "✅ Found configuration file"
echo ""
echo "🔧 Starting Zenoh daemon with WebSocket support on port 10000..."
echo "📡 Chat messages will use topic: chat/messages"
echo ""
echo "Press Ctrl+C to stop the daemon"
echo ""

# Start zenohd with the configuration
zenohd --config "$CONFIG_FILE"
