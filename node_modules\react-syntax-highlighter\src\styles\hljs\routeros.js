export default {
    "hljs": {
        "display": "block",
        "overflowX": "auto",
        "padding": "0.5em",
        "background": "#F0F0F0",
        "color": "#444"
    },
    "hljs-subst": {
        "color": "#444"
    },
    "hljs-comment": {
        "color": "#888888"
    },
    "hljs-keyword": {
        "fontWeight": "bold"
    },
    "hljs-selector-tag": {
        "fontWeight": "bold"
    },
    "hljs-meta-keyword": {
        "fontWeight": "bold"
    },
    "hljs-doctag": {
        "fontWeight": "bold"
    },
    "hljs-name": {
        "fontWeight": "bold"
    },
    "hljs-attribute": {
        "color": "#0E9A00"
    },
    "hljs-function": {
        "color": "#99069A"
    },
    "hljs-builtin-name": {
        "color": "#99069A"
    },
    "hljs-type": {
        "color": "#880000"
    },
    "hljs-string": {
        "color": "#880000"
    },
    "hljs-number": {
        "color": "#880000"
    },
    "hljs-selector-id": {
        "color": "#880000"
    },
    "hljs-selector-class": {
        "color": "#880000"
    },
    "hljs-quote": {
        "color": "#880000"
    },
    "hljs-template-tag": {
        "color": "#880000"
    },
    "hljs-deletion": {
        "color": "#880000"
    },
    "hljs-title": {
        "color": "#880000",
        "fontWeight": "bold"
    },
    "hljs-section": {
        "color": "#880000",
        "fontWeight": "bold"
    },
    "hljs-regexp": {
        "color": "#BC6060"
    },
    "hljs-symbol": {
        "color": "#BC6060"
    },
    "hljs-variable": {
        "color": "#BC6060"
    },
    "hljs-template-variable": {
        "color": "#BC6060"
    },
    "hljs-link": {
        "color": "#BC6060"
    },
    "hljs-selector-attr": {
        "color": "#BC6060"
    },
    "hljs-selector-pseudo": {
        "color": "#BC6060"
    },
    "hljs-literal": {
        "color": "#78A960"
    },
    "hljs-built_in": {
        "color": "#0C9A9A"
    },
    "hljs-bullet": {
        "color": "#0C9A9A"
    },
    "hljs-code": {
        "color": "#0C9A9A"
    },
    "hljs-addition": {
        "color": "#0C9A9A"
    },
    "hljs-meta": {
        "color": "#1f7199"
    },
    "hljs-meta-string": {
        "color": "#4d99bf"
    },
    "hljs-emphasis": {
        "fontStyle": "italic"
    },
    "hljs-strong": {
        "fontWeight": "bold"
    }
}