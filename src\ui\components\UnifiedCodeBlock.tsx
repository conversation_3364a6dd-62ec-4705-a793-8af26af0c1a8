import React, { useMemo, useCallback, useState, useRef, useEffect } from 'react'
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter'
import { oneLight } from 'react-syntax-highlighter/dist/esm/styles/prism'
import { useVirtualizer } from '@tanstack/react-virtual'
import { useCodeBlockSettings } from '../store'

interface UnifiedCodeBlockProps {
  code: string
  language: string
  showLineNumbers?: boolean
  maxHeight?: number
  variant?: 'standard' | 'json'
}

interface CodeStats {
  totalLines: number
  totalSize: number
  isLarge: boolean
  shouldUseVirtualScrolling: boolean
}

export const UnifiedCodeBlock: React.FC<UnifiedCodeBlockProps> = ({
  code,
  language,
  showLineNumbers,
  maxHeight: propMaxHeight,
  variant = 'standard'
}) => {
  const [copySuccess, setCopySuccess] = useState(false)

  const { codeBlockSettings } = useCodeBlockSettings()

  const maxHeight = propMaxHeight ?? codeBlockSettings.maxHeight
  const shouldShowLineNumbers = showLineNumbers ?? codeBlockSettings.showLineNumbers

  const codeStats = useMemo((): CodeStats => {
    const lines = code.split('\n')
    const totalLines = lines.length
    const totalSize = code.length
    const isLarge = totalLines > 50 || totalSize > 50000
    // Use virtual scrolling for very large code blocks (>100 lines or >100KB)
    const shouldUseVirtualScrolling = totalLines > 100 || totalSize > 100000

    return { totalLines, totalSize, isLarge, shouldUseVirtualScrolling }
  }, [code])

  const formatSize = useCallback((bytes: number): string => {
    if (bytes < 1024) return `${bytes} B`
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`
  }, [])

  const handleCopy = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(code)
      setCopySuccess(true)
      setTimeout(() => setCopySuccess(false), 2000)
    } catch (err) {
      console.error('Failed to copy code:', err)
    }
  }, [code])

  const getHeaderContent = () => {
    if (variant === 'json' && codeStats.isLarge) {
      return (
        <div className="flex flex-col gap-0.5">
          <span className="font-semibold text-xs text-gray-800 uppercase">JSON Data</span>
          <span className="text-xs text-gray-600">
            {codeStats.totalLines} lines • {formatSize(codeStats.totalSize)}
          </span>
        </div>
      )
    }

    return (
      <div className="flex items-center gap-2">
        <span className="font-semibold uppercase">{language}</span>
        <span className="opacity-70">{codeStats.totalLines} lines</span>
      </div>
    )
  }

  // Virtual scrolling component for large code blocks
  const VirtualCodeContent = () => {
    const containerRef = useRef<HTMLDivElement>(null)
    const codeLines = useMemo(() => code.split('\n'), [code])

    const virtualizer = useVirtualizer({
      count: codeLines.length,
      getScrollElement: () => containerRef.current,
      estimateSize: () => 20, // Estimated line height in pixels
      overscan: 10, // Render extra lines for smooth scrolling
    })

    return (
      <div
        ref={containerRef}
        className="overflow-auto"
        style={{
          maxHeight: maxHeight,
          backgroundColor: '#ffffff',
          fontSize: '13px',
          lineHeight: '1.5',
          fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace',
          padding: '0.75rem',
          // Custom scrollbar styles
          scrollbarWidth: 'thin',
          scrollbarColor: '#cbd5e1 #f1f5f9'
        }}
      >
        <div
          style={{
            height: virtualizer.getTotalSize(),
            width: '100%',
            position: 'relative',
          }}
        >
          {virtualizer.getVirtualItems().map((virtualItem) => {
            const line = codeLines[virtualItem.index]
            const lineNumber = virtualItem.index + 1

            return (
              <div
                key={virtualItem.index}
                data-index={virtualItem.index}
                ref={virtualizer.measureElement}
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  transform: `translateY(${virtualItem.start}px)`,
                }}
                className="flex"
              >
                {shouldShowLineNumbers && (
                  <span className="text-gray-400 text-right pr-3 select-none" style={{ minWidth: '3em' }}>
                    {lineNumber}
                  </span>
                )}
                <span className="flex-1 whitespace-pre">{line}</span>
              </div>
            )
          })}
        </div>
      </div>
    )
  }

  const CopyButton = () => (
    <button
      onClick={handleCopy}
      className="w-6 h-6 flex items-center justify-center bg-white border border-gray-300 rounded hover:bg-gray-50 transition-colors"
      title={copySuccess ? 'Copied!' : 'Copy code'}
    >
      {copySuccess ? (
        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M20 6L9 17L4 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      ) : (
        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect x="9" y="9" width="13" height="13" rx="2" ry="2" stroke="currentColor" strokeWidth="2" fill="none"/>
          <path d="M5 15H4a2 2 0 01-2-2V4a2 2 0 012-2h9a2 2 0 012 2v1" stroke="currentColor" strokeWidth="2" fill="none"/>
        </svg>
      )}
    </button>
  )

  return (
    <div className="my-2 border border-gray-200 rounded-md overflow-hidden">
      <div className="flex justify-between items-center px-3 py-1.5 bg-gray-50 border-b border-gray-200 text-xs text-gray-600">
        {getHeaderContent()}
        <CopyButton />
      </div>
      <div className="relative">
        {codeStats.shouldUseVirtualScrolling ? (
          <VirtualCodeContent />
        ) : (
          <SyntaxHighlighter
            language={language || 'text'}
            style={oneLight}
            showLineNumbers={shouldShowLineNumbers}
            customStyle={{
              margin: 0,
              borderRadius: 0,
              fontSize: '13px',
              lineHeight: '1.5',
              maxHeight: maxHeight,
              overflow: 'auto',
              backgroundColor: '#ffffff',
              border: 'none',
              padding: '0.75rem', // Compact padding for better space efficiency
              // Custom scrollbar styles for better UX
              scrollbarWidth: 'thin',
              scrollbarColor: '#cbd5e1 #f1f5f9'
            }}
            codeTagProps={{
              style: {
                backgroundColor: 'transparent',
                color: 'inherit'
              }
            }}
            preTag="div"
          >
            {code}
          </SyntaxHighlighter>
        )}
      </div>
    </div>
  )
}

export default UnifiedCodeBlock