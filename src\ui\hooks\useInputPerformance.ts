// Hook for monitoring input performance and responsiveness

import { useRef, useCallback, useEffect } from 'react'

interface InputPerformanceMetrics {
  inputLatency: number
  processingTime: number
  lastUpdate: Date
  averageLatency: number
  maxLatency: number
}

interface UseInputPerformanceOptions {
  enabled?: boolean
  onMetricsUpdate?: (metrics: InputPerformanceMetrics) => void
  sampleSize?: number
}

export function useInputPerformance(options: UseInputPerformanceOptions = {}) {
  const {
    enabled = process.env.NODE_ENV === 'development',
    onMetricsUpdate,
    sampleSize = 10
  } = options

  const inputStartTime = useRef<number>(0)
  const processingStartTime = useRef<number>(0)
  const latencySamples = useRef<number[]>([])
  const metricsRef = useRef<InputPerformanceMetrics>({
    inputLatency: 0,
    processingTime: 0,
    lastUpdate: new Date(),
    averageLatency: 0,
    maxLatency: 0
  })

  // Start measuring input latency
  const startInputMeasurement = useCallback(() => {
    if (!enabled) return
    inputStartTime.current = performance.now()
  }, [enabled])

  // End input measurement and start processing measurement
  const startProcessingMeasurement = useCallback(() => {
    if (!enabled) return
    
    const now = performance.now()
    const inputLatency = now - inputStartTime.current
    
    // Update latency samples
    latencySamples.current.push(inputLatency)
    if (latencySamples.current.length > sampleSize) {
      latencySamples.current.shift()
    }

    // Calculate average and max latency
    const averageLatency = latencySamples.current.reduce((sum, val) => sum + val, 0) / latencySamples.current.length
    const maxLatency = Math.max(...latencySamples.current)

    // Update metrics
    metricsRef.current = {
      ...metricsRef.current,
      inputLatency,
      averageLatency,
      maxLatency,
      lastUpdate: new Date()
    }

    processingStartTime.current = now
  }, [enabled, sampleSize])

  // End processing measurement
  const endProcessingMeasurement = useCallback(() => {
    if (!enabled) return
    
    const now = performance.now()
    const processingTime = now - processingStartTime.current
    
    // Update metrics
    metricsRef.current = {
      ...metricsRef.current,
      processingTime,
      lastUpdate: new Date()
    }

    // Notify callback if provided
    if (onMetricsUpdate) {
      onMetricsUpdate(metricsRef.current)
    }

    // Log performance warnings in development
    if (process.env.NODE_ENV === 'development') {
      if (metricsRef.current.inputLatency > 16) {
        console.warn(`🐌 [InputPerformance] High input latency: ${metricsRef.current.inputLatency.toFixed(2)}ms`)
      }
      if (metricsRef.current.processingTime > 50) {
        console.warn(`🐌 [InputPerformance] Slow processing: ${metricsRef.current.processingTime.toFixed(2)}ms`)
      }
    }
  }, [enabled, onMetricsUpdate])

  // Get current metrics
  const getMetrics = useCallback(() => {
    return { ...metricsRef.current }
  }, [])

  // Reset metrics
  const resetMetrics = useCallback(() => {
    latencySamples.current = []
    metricsRef.current = {
      inputLatency: 0,
      processingTime: 0,
      lastUpdate: new Date(),
      averageLatency: 0,
      maxLatency: 0
    }
  }, [])

  // Log metrics periodically in development
  useEffect(() => {
    if (!enabled || process.env.NODE_ENV !== 'development') return

    const interval = setInterval(() => {
      const metrics = getMetrics()
      if (metrics.averageLatency > 0) {
        console.log(`📊 [InputPerformance] Avg: ${metrics.averageLatency.toFixed(2)}ms, Max: ${metrics.maxLatency.toFixed(2)}ms`)
      }
    }, 10000) // Log every 10 seconds

    return () => clearInterval(interval)
  }, [enabled, getMetrics])

  return {
    startInputMeasurement,
    startProcessingMeasurement,
    endProcessingMeasurement,
    getMetrics,
    resetMetrics,
    enabled
  }
}

export type { InputPerformanceMetrics, UseInputPerformanceOptions }
