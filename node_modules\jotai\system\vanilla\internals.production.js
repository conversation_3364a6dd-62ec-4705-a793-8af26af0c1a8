System.register([],function($){"use strict";return{execute:function(){const M=(t,n)=>t.unstable_is?t.unstable_is(n):n===t,z=t=>"init"in t,W=t=>!!t.write,O=t=>"v"in t||"e"in t,h=t=>{if("e"in t)throw t.e;return t.v},m=new WeakMap,B=t=>{var n;return v(t)&&!!((n=m.get(t))!=null&&n[0])},J=t=>{const n=m.get(t);n!=null&&n[0]&&(n[0]=!1,n[1].forEach(a=>a()))},K=(t,n)=>{let a=m.get(t);if(!a){a=[!0,new Set],m.set(t,a);const d=()=>{a[0]=!1};t.then(d,d)}a[1].add(n)},v=t=>typeof(t==null?void 0:t.then)=="function",D=(t,n,a)=>{a.p.has(t)||(a.p.add(t),n.then(()=>{a.p.delete(t)},()=>{a.p.delete(t)}))},S=(t,n,a)=>{const d=a(t),A="v"in d,N=d.v;if(v(n))for(const u of d.d.keys())D(t,n,a(u));d.v=n,delete d.e,(!A||!Object.is(N,d.v))&&(++d.n,v(N)&&J(N))},H=(t,n,a)=>{var d;const A=new Set;for(const N of((d=a.get(t))==null?void 0:d.t)||[])a.has(N)&&A.add(N);for(const N of n.p)A.add(N);return A},q=()=>{const t=new Set,n=()=>{t.forEach(a=>a())};return n.add=a=>(t.add(a),()=>{t.delete(a)}),n},V=()=>{const t={},n=new WeakMap,a=d=>{var A,N;(A=n.get(t))==null||A.forEach(u=>u(d)),(N=n.get(d))==null||N.forEach(u=>u())};return a.add=(d,A)=>{const N=d||t,u=(n.has(N)?n:n.set(N,new Set)).get(N);return u.add(A),()=>{u==null||u.delete(A),u.size||n.delete(N)}},a},G=t=>(t.c||(t.c=V()),t.m||(t.m=V()),t.u||(t.u=V()),t.f||(t.f=q()),t),Q=Symbol();$({INTERNAL_buildStoreRev1:(t=new WeakMap,n=new WeakMap,a=new WeakMap,d=new Set,A=new Set,N=new Set,u={},U=(c,...R)=>c.read(...R),X=(c,...R)=>c.write(...R),x=(c,R)=>{var g;return(g=c.unstable_onInit)==null?void 0:g.call(c,R)},Y=(c,R)=>{var g;return(g=c.onMount)==null?void 0:g.call(c,R)},...L)=>{const c=L[0]||(e=>{let s=t.get(e);return s||(s={d:new Map,p:new Set,n:0},t.set(e,s),x==null||x(e,j)),s}),R=L[1]||(()=>{const e=[],s=o=>{try{o()}catch(l){e.push(l)}};do{u.f&&s(u.f);const o=new Set,l=o.add.bind(o);d.forEach(r=>{var i;return(i=n.get(r))==null?void 0:i.l.forEach(l)}),d.clear(),N.forEach(l),N.clear(),A.forEach(l),A.clear(),o.forEach(s),d.size&&g()}while(d.size||N.size||A.size);if(e.length)throw new AggregateError(e)}),g=L[2]||(()=>{const e=[],s=new WeakSet,o=new WeakSet,l=Array.from(d);for(;l.length;){const r=l[l.length-1],i=c(r);if(o.has(r)){l.pop();continue}if(s.has(r)){a.get(r)===i.n&&e.push([r,i]),o.add(r),l.pop();continue}s.add(r);for(const E of H(r,i,n))s.has(E)||l.push(E)}for(let r=e.length-1;r>=0;--r){const[i,E]=e[r];let I=!1;for(const _ of E.d.keys())if(_!==i&&d.has(_)){I=!0;break}I&&(T(i),p(i)),a.delete(i)}}),T=L[3]||(e=>{var s;const o=c(e);if(O(o)&&(n.has(e)&&a.get(e)!==o.n||Array.from(o.d).every(([f,k])=>T(f).n===k)))return o;o.d.clear();let l=!0;const r=()=>{n.has(e)&&(p(e),g(),R())},i=f=>{var k;if(M(e,f)){const Z=c(f);if(!O(Z))if(z(f))S(f,f.init,c);else throw new Error("no atom init");return h(Z)}const F=T(f);try{return h(F)}finally{o.d.set(f,F.n),B(o.v)&&D(e,o.v,F),(k=n.get(f))==null||k.t.add(e),l||r()}};let E,I;const _={get signal(){return E||(E=new AbortController),E.signal},get setSelf(){return!I&&W(e)&&(I=(...f)=>{if(!l)try{return y(e,...f)}finally{g(),R()}}),I}},b=o.n;try{const f=U(e,i,_);return S(e,f,c),v(f)&&(K(f,()=>E==null?void 0:E.abort()),f.then(r,r)),o}catch(f){return delete o.v,o.e=f,++o.n,o}finally{l=!1,b!==o.n&&a.get(e)===b&&(a.set(e,o.n),d.add(e),(s=u.c)==null||s.call(u,e))}}),C=L[4]||(e=>{const s=[e];for(;s.length;){const o=s.pop(),l=c(o);for(const r of H(o,l,n)){const i=c(r);a.set(r,i.n),s.push(r)}}}),y=L[5]||((e,...s)=>{let o=!0;const l=i=>h(T(i)),r=(i,...E)=>{var I;const _=c(i);try{if(M(e,i)){if(!z(i))throw new Error("atom not writable");const b=_.n,f=E[0];S(i,f,c),p(i),b!==_.n&&(d.add(i),(I=u.c)==null||I.call(u,i),C(i));return}else return y(i,...E)}finally{o||(g(),R())}};try{return X(e,l,r,...s)}finally{o=!1}}),p=L[6]||(e=>{var s;const o=c(e),l=n.get(e);if(l&&!B(o.v)){for(const[r,i]of o.d)if(!l.d.has(r)){const E=c(r);w(r).t.add(e),l.d.add(r),i!==E.n&&(d.add(r),(s=u.c)==null||s.call(u,r),C(r))}for(const r of l.d||[])if(!o.d.has(r)){l.d.delete(r);const i=P(r);i==null||i.t.delete(e)}}}),w=L[7]||(e=>{var s;const o=c(e);let l=n.get(e);if(!l){T(e);for(const r of o.d.keys())w(r).t.add(e);if(l={l:new Set,d:new Set(o.d.keys()),t:new Set},n.set(e,l),(s=u.m)==null||s.call(u,e),W(e)){const r=()=>{let i=!0;const E=(...I)=>{try{return y(e,...I)}finally{i||(g(),R())}};try{const I=Y(e,E);I&&(l.u=()=>{i=!0;try{I()}finally{i=!1}})}finally{i=!1}};A.add(r)}}return l}),P=L[8]||(e=>{var s;const o=c(e);let l=n.get(e);if(l&&!l.l.size&&!Array.from(l.t).some(r=>{var i;return(i=n.get(r))==null?void 0:i.d.has(e)})){l.u&&N.add(l.u),l=void 0,n.delete(e),(s=u.u)==null||s.call(u,e);for(const r of o.d.keys()){const i=P(r);i==null||i.t.delete(e)}return}return l}),ee=[t,n,a,d,A,N,u,U,X,x,Y,c,R,g,T,C,y,p,w,P],j={get:e=>h(T(e)),set:(e,...s)=>{try{return y(e,...s)}finally{g(),R()}},sub:(e,s)=>{const o=w(e).l;return o.add(s),R(),()=>{o.delete(s),P(e),R()}}};return Object.defineProperty(j,Q,{value:ee}),j},INTERNAL_getBuildingBlocksRev1:t=>t[Q],INTERNAL_initializeStoreHooks:G,INTERNAL_isSelfAtom:M,INTERNAL_hasInitialValue:z,INTERNAL_isActuallyWritableAtom:W,INTERNAL_isAtomStateInitialized:O,INTERNAL_returnAtomValue:h,INTERNAL_promiseStateMap:m,INTERNAL_isPendingPromise:B,INTERNAL_abortPromise:J,INTERNAL_registerAbortHandler:K,INTERNAL_isPromiseLike:v,INTERNAL_addPendingPromiseToDependency:D,INTERNAL_setAtomStateValueOrPromise:S,INTERNAL_getMountedOrPendingDependents:H})}}});
