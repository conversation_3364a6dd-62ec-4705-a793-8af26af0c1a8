'use client';
System.register(["react","jotai/react","jotai/vanilla/utils","jotai/vanilla"],function(k){"use strict";var r,i,c,l,m,f,A;return{setters:[function(e){r=e.useCallback,i=e.useMemo},function(e){c=e.useSetAtom,l=e.useAtom,m=e.useStore},function(e){f=e.RESET},function(e){A=e.atom}],execute:function(){k({useAtomCallback:R,useHydrateAtoms:d,useReducerAtom:y,useResetAtom:e});function e(n,t){const s=c(n,t);return r(()=>s(f),[s])}function y(n,t,s){const[u,o]=l(n,s),a=r(C=>{o(E=>t(E,C))},[o,t]);return[u,a]}function R(n,t){const s=i(()=>A(null,(u,o,...a)=>n(u,o,...a)),[n]);return c(s,t)}const S=new WeakMap;function d(n,t){const s=m(t),u=b(s);for(const[o,a]of n)(!u.has(o)||t!=null&&t.dangerouslyForceHydrate)&&(u.add(o),s.set(o,a))}const b=n=>{let t=S.get(n);return t||(t=new WeakSet,S.set(n,t)),t}}}});
