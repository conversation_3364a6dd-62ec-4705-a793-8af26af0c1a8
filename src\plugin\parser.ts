import { hasNodeProperty, FigmaNodeType } from './figma_types';

interface JNode {
    figma: Record<string, unknown>
}

// Helper function to ensure data is serializable for postMessage
function makeSerializable(obj: unknown): unknown {
    try {
        // Use JSON.parse(JSON.stringify()) to remove non-serializable values
        return JSON.parse(JSON.stringify(obj));
    } catch (error) {
        console.warn('Failed to serialize object:', error);
        return null;
    }
}

// Properties to extract from Figma nodes
const parseProps: string[] = [
    'id', // important fields
    'type', // important fields

    // shared node properties
    'absoluteBoundingBox',
    'absoluteRenderBounds',
    'absoluteTransform',
    'blendMode',
    'bottomLeftRadius',
    'bottomRightRadius',
    'boundVariables',
    'characters',
    'clipsContent',
    'componentPropertyDefinitions',
    'componentPropertyReferences',
    'constraints',
    'cornerRadius',
    'cornerSmoothing',
    'counterAxisAlignContent',
    'counterAxisAlignItems',
    'counterAxisSizingMode',
    'counterAxisSpacing',
    'dashPattern',
    'description',
    'descriptionMarkdown',
    'detachedInfo',
    'documentationLinks',
    'effectStyleId',
    'effects',
    'expanded',
    'explicitVariableModes',
    'fillGeometry',
    'fillStyleId',
    'fills',
    'fontName',
    'fontSize',
    'fontWeight',
    'gridStyleId',
    'guides',
    'handleMirroring',
    'hangingList',
    'hangingPunctuation',
    'hasMissingFont',
    'height',
    'hyperlink',
    'isAsset',
    'isMask',
    'itemReverseZIndex',
    'itemSpacing',
    'key',
    'layoutAlign',
    'layoutGrids',
    'layoutGrow',
    'layoutMode',
    'layoutPositioning',
    'layoutSizingHorizontal',
    'layoutSizingVertical',
    'layoutWrap',
    'leadingTrim',
    'letterSpacing',
    'lineHeight',
    'listSpacing',
    'locked',
    'maskType',
    'maxHeight',
    'maxWidth',
    'minHeight',
    'minWidth',
    'name',
    'numberOfFixedChildren',
    'opacity',
    'openTypeFeatures',
    'overflowDirection',
    'overlayBackground',
    'overlayBackgroundInteraction',
    'overlayPositionType',
    'paddingBottom',
    'paddingLeft',
    'paddingRight',
    'paddingTop',
    'paragraphIndent',
    'paragraphSpacing',
    'primaryAxisAlignItems',
    'primaryAxisSizingMode',
    'reactions',
    'relativeTransform',
    'remote',
    'removed',
    'resolvedVariableModes',
    'rotation',
    'strokeAlign',
    'strokeBottomWeight',
    'strokeCap',
    'strokeGeometry',
    'strokeJoin',
    'strokeLeftWeight',
    'strokeMiterLimit',
    'strokeRightWeight',
    'strokeStyleId',
    'strokeTopWeight',
    'strokeWeight',
    'strokes',
    'strokesIncludedInLayout',
    'targetAspectRatio',
    'textCase',
    'textDecoration',
    'textDecorationColor',
    'textDecorationOffset',
    'textDecorationSkipInk',
    'textDecorationStyle',
    'textDecorationThickness',
    'topLeftRadius',
    'topRightRadius',
    'vectorNetwork',
    'vectorPaths',
    'visible',
    'width',
    'x',
    'y',
];



/**
 * Parse Figma nodes and extract their properties
 */
export const parseFigmaNodes = async (fNodes: readonly SceneNode[]): Promise<JNode[]> => {
    const results: JNode[] = [];

    for (const fNode of fNodes) {
        console.log("type", fNode.type);
        const result = {
            figma: {} as Record<string, unknown>
        };

        // Temporary storage for properties and values
        const tempProps: Record<string, unknown> = {
            "type": fNode.type
        };

        for (const key of parseProps) {
            console.log(fNode.type);
            if (key in fNode && hasNodeProperty(fNode.type as FigmaNodeType, key)) {
                if (key === 'componentPropertyDefinitions' && fNode.parent?.type === "COMPONENT_SET") {
                    continue;
                }
                const value = (fNode as unknown as Record<string, unknown>)[key];
                tempProps[key] = makeSerializable(value);
            }
        }

        // Get CSS and ensure it's serializable
        try {
            const cssData = await fNode.getCSSAsync();
            // Convert CSS object to a serializable format
            tempProps['css'] = JSON.parse(JSON.stringify(cssData));
        } catch (error) {
            console.warn('Failed to get CSS for node:', fNode.id, error);
            tempProps['css'] = {};
        }

        // Get plugin data
        tempProps['pluginData'] = {}
        fNode.getPluginDataKeys().forEach((key: string) => {
            (tempProps['pluginData'] as Record<string, string>)[key] = fNode.getPluginData(key)
        });

        // Add parent node information
        if (fNode.parent) {
            tempProps['parent'] = makeSerializable({
                'id': fNode.parent?.id
            });
        }

        // Process child nodes
        if ('children' in fNode) {
            tempProps['children'] = await parseFigmaNodes(fNode.children);
        }

        // Sort properties alphabetically by key and add to result.figma
        Object.keys(tempProps).sort().forEach(key => {
            result.figma[key] = tempProps[key];
        });

        results.push(result as JNode);
    }

    // Ensure the entire result is serializable before returning
    return makeSerializable(results) as JNode[];
}
