System.register(["jotai/vanilla/internals"],function(f){"use strict";var a;return{setters:[function(i){a=i.INTERNAL_buildStoreRev1}],execute:function(){f({INTERNAL_overrideCreateStore:N,atom:l,createStore:c,getDefaultStore:d});let i=0;function l(t,o){const r=`atom${++i}`,e={toString(){return r}};return typeof t=="function"?e.read=t:(e.init=t,e.read=s,e.write=S),o&&(e.write=o),e}function s(t){return t(this)}function S(t,o,r){return o(this,typeof r=="function"?r(t(this)):r)}let n;function N(t){n=t(n)}function c(){return n?n():a()}let u;function d(){return u||(u=c()),u}}}});
