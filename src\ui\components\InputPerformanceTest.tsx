// Test component for verifying input performance improvements

import React, { useState, useRef, useCallback } from 'react'
import { useInputPerformance } from '../hooks/useInputPerformance'
import { useInputScheduler } from '../utils/input_scheduler'
import { Message } from '../types/ui_types'
import { ChatInterface } from './ChatInterface'

// Generate test messages for performance testing
const generateTestMessages = (count: number): Message[] => {
  const messages: Message[] = []
  
  for (let i = 0; i < count; i++) {
    const messageType = i % 3 === 0 ? 'user' : i % 3 === 1 ? 'ai' : 'system'
    const hasLargeContent = i % 10 === 0
    
    let text = `Test message ${i + 1}. This is a ${messageType} message for performance testing.`
    
    if (hasLargeContent) {
      // Add large JSON content to some messages
      text += '\n\n```json\n' + JSON.stringify({
        selection: Array.from({ length: 100 }, (_, idx) => ({
          id: `node-${idx}`,
          type: 'FRAME',
          name: `Frame ${idx}`,
          x: Math.random() * 1000,
          y: Math.random() * 1000,
          width: Math.random() * 500,
          height: Math.random() * 500,
          fills: [{ type: 'SOLID', color: { r: Math.random(), g: Math.random(), b: Math.random() } }]
        })),
        timestamp: new Date().toISOString(),
        metadata: {
          version: '1.0.0',
          source: 'figma-plugin',
          performance_test: true
        }
      }, null, 2) + '\n```'
    }
    
    messages.push({
      id: `test-msg-${i}`,
      text,
      timestamp: new Date(Date.now() - (count - i) * 1000),
      type: messageType as 'user' | 'ai' | 'system',
      ...(messageType === 'ai' && {
        vendor: 'test',
        model: 'performance-test-model'
      })
    })
  }
  
  return messages
}

interface InputPerformanceTestProps {
  messageCount?: number
  enableProfiling?: boolean
}

export const InputPerformanceTest: React.FC<InputPerformanceTestProps> = ({
  messageCount = 100,
  enableProfiling = true
}) => {
  const [messages, setMessages] = useState<Message[]>(() => generateTestMessages(messageCount))
  const [inputValue, setInputValue] = useState('')
  const [testResults, setTestResults] = useState<{
    averageLatency: number
    maxLatency: number
    totalEvents: number
  } | null>(null)
  
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  
  // Performance monitoring
  const inputPerformance = useInputPerformance({
    enabled: enableProfiling,
    onMetricsUpdate: (metrics) => {
      setTestResults({
        averageLatency: metrics.averageLatency,
        maxLatency: metrics.maxLatency,
        totalEvents: metrics.inputLatency > 0 ? 1 : 0
      })
    }
  })
  
  // Scheduler for testing
  const scheduler = useInputScheduler()
  
  // Test input handler with performance measurement
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    inputPerformance.startInputMeasurement()
    
    const newValue = e.target.value
    setInputValue(newValue)
    
    inputPerformance.startProcessingMeasurement()
    
    // Simulate heavy processing that might block input
    if (newValue.length > 10) {
      scheduler.scheduleBackgroundTask(() => {
        // Simulate expensive operation
        const start = performance.now()
        while (performance.now() - start < 5) {
          // Busy wait to simulate work
        }
        inputPerformance.endProcessingMeasurement()
      })
    } else {
      inputPerformance.endProcessingMeasurement()
    }
  }, [inputPerformance, scheduler])
  
  // Add more test messages
  const addMessages = useCallback((count: number) => {
    const newMessages = generateTestMessages(count).map((msg, idx) => ({
      ...msg,
      id: `additional-${messages.length + idx}`,
      timestamp: new Date()
    }))
    setMessages(prev => [...prev, ...newMessages])
  }, [messages.length])
  
  // Clear all messages
  const clearMessages = useCallback(() => {
    setMessages([])
    inputPerformance.resetMetrics()
  }, [inputPerformance])
  
  // Stress test - rapid input simulation
  const runStressTest = useCallback(() => {
    let counter = 0
    const interval = setInterval(() => {
      if (inputRef.current) {
        const testValue = `Stress test input ${counter++}`
        inputRef.current.value = testValue
        inputRef.current.dispatchEvent(new Event('input', { bubbles: true }))
        
        if (counter >= 50) {
          clearInterval(interval)
        }
      }
    }, 10) // Very rapid input
  }, [])
  
  return (
    <div className="input-performance-test p-4 space-y-4">
      <div className="test-controls bg-gray-100 p-4 rounded-lg">
        <h3 className="text-lg font-semibold mb-4">Input Performance Test</h3>
        
        <div className="flex gap-2 mb-4">
          <button
            onClick={() => addMessages(50)}
            className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Add 50 Messages
          </button>
          <button
            onClick={() => addMessages(100)}
            className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Add 100 Messages
          </button>
          <button
            onClick={clearMessages}
            className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600"
          >
            Clear Messages
          </button>
          <button
            onClick={runStressTest}
            className="px-3 py-1 bg-orange-500 text-white rounded hover:bg-orange-600"
          >
            Stress Test
          </button>
        </div>
        
        <div className="test-input mb-4">
          <label className="block text-sm font-medium mb-2">
            Test Input (type to measure latency):
          </label>
          <input
            ref={inputRef}
            type="text"
            value={inputValue}
            onChange={handleInputChange}
            placeholder="Type here to test input responsiveness..."
            className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500"
          />
        </div>
        
        {testResults && (
          <div className="test-results bg-white p-3 rounded border">
            <h4 className="font-medium mb-2">Performance Results:</h4>
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Avg Latency:</span>
                <span className={`ml-2 font-mono ${testResults.averageLatency > 16 ? 'text-red-600' : 'text-green-600'}`}>
                  {testResults.averageLatency.toFixed(2)}ms
                </span>
              </div>
              <div>
                <span className="text-gray-600">Max Latency:</span>
                <span className={`ml-2 font-mono ${testResults.maxLatency > 33 ? 'text-red-600' : 'text-green-600'}`}>
                  {testResults.maxLatency.toFixed(2)}ms
                </span>
              </div>
              <div>
                <span className="text-gray-600">Messages:</span>
                <span className="ml-2 font-mono">{messages.length}</span>
              </div>
            </div>
          </div>
        )}
      </div>
      
      <div className="chat-container h-96 border border-gray-300 rounded-lg">
        <ChatInterface
          messages={messages}
          copiedMessageId={null}
          onCopyMessage={() => {}}
          messagesEndRef={messagesEndRef}
        />
      </div>
    </div>
  )
}

export default InputPerformanceTest
