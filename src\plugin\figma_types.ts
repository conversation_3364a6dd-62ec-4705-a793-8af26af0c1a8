// Auto-generated Figma Node Types and Properties
// Generated on: 2025-06-05T12:47:17.490Z
// Source: @figma/plugin-typings

/**
 * All Figma Node Types
 */
export const FIGMA_NODE_TYPES = [
  'BOOLEAN_OPERATION',
  'CODE_BLOCK',
  'COMPONENT',
  'COMPONENT_SET',
  'CONNECTOR',
  'DOCUMENT',
  'ELLIPSE',
  'EMBED',
  'FRAME',
  'GRO<PERSON>',
  'HIGHLIGHT',
  'INSTANCE',
  'INTERACTIVE_SLIDE_ELEMENT',
  'LINE',
  'LINK_UNFURL',
  'MEDIA',
  'PAGE',
  'POLYGON',
  'RECTANGLE',
  'SCENE',
  'SECTION',
  'SHAPE_WITH_TEXT',
  'SLICE',
  'SLIDE',
  'SLIDE_GRID',
  'SLIDE_ROW',
  'STAMP',
  'STAR',
  'STICKY',
  'TABLE',
  'TEXT',
  'TEXT_PATH',
  'TRANSFORM_GROUP',
  'VECTOR',
  'WASHI_TAPE',
  'WIDGET'
] as const;

export type FigmaNodeType = typeof FIGMA_NODE_TYPES[number];

/**
 * Common Mixin Properties
 */
export const FIGMA_MIXINS = {
  AnnotationsMixin: [
    'annotations'
  ],

  AspectRatioLockMixin: [
    'lockAspectRatio',
    'targetAspectRatio',
    'unlockAspectRatio'
  ],

  AutoLayoutChildrenMixin: [
    'layoutAlign',
    'layoutGrow',
    'layoutPositioning'
  ],

  AutoLayoutMixin: [
    'counterAxisAlignContent',
    'counterAxisAlignItems',
    'counterAxisSizingMode',
    'counterAxisSpacing',
    'horizontalPadding',
    'itemReverseZIndex',
    'itemSpacing',
    'layoutMode',
    'layoutWrap',
    'paddingBottom',
    'paddingLeft',
    'paddingRight',
    'paddingTop',
    'primaryAxisAlignItems',
    'primaryAxisSizingMode',
    'strokesIncludedInLayout',
    'verticalPadding'
  ],

  BaseFrameMixin: [
    'absoluteBoundingBox',
    'absoluteRenderBounds',
    'absoluteTransform',
    'addDevResourceAsync',
    'annotations',
    'appendChild',
    'attachedConnectors',
    'backgroundStyleId',
    'backgrounds',
    'blendMode',
    'bottomLeftRadius',
    'bottomRightRadius',
    'boundVariables',
    'children',
    'clearExplicitVariableModeForCollection',
    'clipsContent',
    'componentPropertyReferences',
    'constrainProportions',
    'constraints',
    'cornerRadius',
    'cornerSmoothing',
    'counterAxisAlignContent',
    'counterAxisAlignItems',
    'counterAxisSizingMode',
    'counterAxisSpacing',
    'dashPattern',
    'deleteDevResourceAsync',
    'detachedInfo',
    'devStatus',
    'editDevResourceAsync',
    'effectStyleId',
    'effects',
    'expanded',
    'explicitVariableModes',
    'exportAsync',
    'exportSettings',
    'fillGeometry',
    'fillStyleId',
    'fills',
    'findAll',
    'findAllWithCriteria',
    'findChild',
    'findChildren',
    'findOne',
    'findWidgetNodesByWidgetId',
    'getCSSAsync',
    'getDevResourcesAsync',
    'getPluginData',
    'getPluginDataKeys',
    'getRelaunchData',
    'getSharedPluginData',
    'getSharedPluginDataKeys',
    'getTopLevelFrame',
    'gridStyleId',
    'guides',
    'height',
    'horizontalPadding',
    'id',
    'inferredAutoLayout',
    'inferredVariables',
    'insertChild',
    'isAsset',
    'isMask',
    'itemReverseZIndex',
    'itemSpacing',
    'layoutAlign',
    'layoutGrids',
    'layoutGrow',
    'layoutMode',
    'layoutPositioning',
    'layoutSizingHorizontal',
    'layoutSizingVertical',
    'layoutWrap',
    'lockAspectRatio',
    'locked',
    'maskType',
    'maxHeight',
    'maxWidth',
    'minHeight',
    'minWidth',
    'name',
    'opacity',
    'outlineStroke',
    'paddingBottom',
    'paddingLeft',
    'paddingRight',
    'paddingTop',
    'parent',
    'primaryAxisAlignItems',
    'primaryAxisSizingMode',
    'relativeTransform',
    'remove',
    'removed',
    'rescale',
    'resize',
    'resizeWithoutConstraints',
    'resolvedVariableModes',
    'rotation',
    'setBoundVariable',
    'setDevResourcePreviewAsync',
    'setEffectStyleIdAsync',
    'setExplicitVariableModeForCollection',
    'setFillStyleIdAsync',
    'setGridStyleIdAsync',
    'setPluginData',
    'setRelaunchData',
    'setSharedPluginData',
    'setStrokeStyleIdAsync',
    'strokeAlign',
    'strokeBottomWeight',
    'strokeCap',
    'strokeGeometry',
    'strokeJoin',
    'strokeLeftWeight',
    'strokeMiterLimit',
    'strokeRightWeight',
    'strokeStyleId',
    'strokeTopWeight',
    'strokeWeight',
    'strokes',
    'strokesIncludedInLayout',
    'stuckNodes',
    'targetAspectRatio',
    'toString',
    'topLeftRadius',
    'topRightRadius',
    'unlockAspectRatio',
    'verticalPadding',
    'visible',
    'width'
  ],

  BaseNodeMixin: [
    'addDevResourceAsync',
    'deleteDevResourceAsync',
    'editDevResourceAsync',
    'getCSSAsync',
    'getDevResourcesAsync',
    'getPluginData',
    'getPluginDataKeys',
    'getRelaunchData',
    'getSharedPluginData',
    'getSharedPluginDataKeys',
    'getTopLevelFrame',
    'id',
    'isAsset',
    'name',
    'parent',
    'remove',
    'removed',
    'setDevResourcePreviewAsync',
    'setPluginData',
    'setRelaunchData',
    'setSharedPluginData',
    'toString'
  ],

  BaseNonResizableTextMixin: [
    'characters',
    'deleteCharacters',
    'fontName',
    'fontSize',
    'fontWeight',
    'getRangeAllFontNames',
    'getRangeBoundVariable',
    'getRangeFillStyleId',
    'getRangeFills',
    'getRangeFontName',
    'getRangeFontSize',
    'getRangeFontWeight',
    'getRangeHyperlink',
    'getRangeLetterSpacing',
    'getRangeOpenTypeFeatures',
    'getRangeTextCase',
    'getRangeTextStyleId',
    'getStyledTextSegments',
    'hasMissingFont',
    'hyperlink',
    'insertCharacters',
    'letterSpacing',
    'openTypeFeatures',
    'setRangeBoundVariable',
    'setRangeFillStyleId',
    'setRangeFillStyleIdAsync',
    'setRangeFills',
    'setRangeFontName',
    'setRangeFontSize',
    'setRangeHyperlink',
    'setRangeLetterSpacing',
    'setRangeTextCase',
    'setRangeTextStyleId',
    'setRangeTextStyleIdAsync',
    'textCase'
  ],

  BaseStyleMixin: [
    'consumers',
    'description',
    'descriptionMarkdown',
    'documentationLinks',
    'getPluginData',
    'getPluginDataKeys',
    'getPublishStatusAsync',
    'getSharedPluginData',
    'getSharedPluginDataKeys',
    'getStyleConsumersAsync',
    'id',
    'key',
    'name',
    'remote',
    'remove',
    'setPluginData',
    'setSharedPluginData'
  ],

  BlendMixin: [
    'blendMode',
    'effectStyleId',
    'effects',
    'isMask',
    'maskType',
    'opacity',
    'setEffectStyleIdAsync'
  ],

  ChildrenMixin: [
    'appendChild',
    'children',
    'findAll',
    'findAllWithCriteria',
    'findChild',
    'findChildren',
    'findOne',
    'findWidgetNodesByWidgetId',
    'insertChild'
  ],

  ComponentPropertiesMixin: [
    'addComponentProperty',
    'componentPropertyDefinitions',
    'deleteComponentProperty',
    'editComponentProperty'
  ],

  ConstraintMixin: [
    'constraints'
  ],

  ContainerMixin: [
    'expanded'
  ],

  CornerMixin: [
    'cornerRadius',
    'cornerSmoothing'
  ],

  DefaultFrameMixin: [
    'absoluteBoundingBox',
    'absoluteRenderBounds',
    'absoluteTransform',
    'addDevResourceAsync',
    'annotations',
    'appendChild',
    'attachedConnectors',
    'backgroundStyleId',
    'backgrounds',
    'blendMode',
    'bottomLeftRadius',
    'bottomRightRadius',
    'boundVariables',
    'children',
    'clearExplicitVariableModeForCollection',
    'clipsContent',
    'componentPropertyReferences',
    'constrainProportions',
    'constraints',
    'cornerRadius',
    'cornerSmoothing',
    'counterAxisAlignContent',
    'counterAxisAlignItems',
    'counterAxisSizingMode',
    'counterAxisSpacing',
    'dashPattern',
    'deleteDevResourceAsync',
    'detachedInfo',
    'devStatus',
    'editDevResourceAsync',
    'effectStyleId',
    'effects',
    'expanded',
    'explicitVariableModes',
    'exportAsync',
    'exportSettings',
    'fillGeometry',
    'fillStyleId',
    'fills',
    'findAll',
    'findAllWithCriteria',
    'findChild',
    'findChildren',
    'findOne',
    'findWidgetNodesByWidgetId',
    'getCSSAsync',
    'getDevResourcesAsync',
    'getPluginData',
    'getPluginDataKeys',
    'getRelaunchData',
    'getSharedPluginData',
    'getSharedPluginDataKeys',
    'getTopLevelFrame',
    'gridStyleId',
    'guides',
    'height',
    'horizontalPadding',
    'id',
    'inferredAutoLayout',
    'inferredVariables',
    'insertChild',
    'isAsset',
    'isMask',
    'itemReverseZIndex',
    'itemSpacing',
    'layoutAlign',
    'layoutGrids',
    'layoutGrow',
    'layoutMode',
    'layoutPositioning',
    'layoutSizingHorizontal',
    'layoutSizingVertical',
    'layoutWrap',
    'lockAspectRatio',
    'locked',
    'maskType',
    'maxHeight',
    'maxWidth',
    'minHeight',
    'minWidth',
    'name',
    'numberOfFixedChildren',
    'opacity',
    'outlineStroke',
    'overflowDirection',
    'overlayBackground',
    'overlayBackgroundInteraction',
    'overlayPositionType',
    'paddingBottom',
    'paddingLeft',
    'paddingRight',
    'paddingTop',
    'parent',
    'primaryAxisAlignItems',
    'primaryAxisSizingMode',
    'reactions',
    'relativeTransform',
    'remove',
    'removed',
    'rescale',
    'resize',
    'resizeWithoutConstraints',
    'resolvedVariableModes',
    'rotation',
    'setBoundVariable',
    'setDevResourcePreviewAsync',
    'setEffectStyleIdAsync',
    'setExplicitVariableModeForCollection',
    'setFillStyleIdAsync',
    'setGridStyleIdAsync',
    'setPluginData',
    'setReactionsAsync',
    'setRelaunchData',
    'setSharedPluginData',
    'setStrokeStyleIdAsync',
    'strokeAlign',
    'strokeBottomWeight',
    'strokeCap',
    'strokeGeometry',
    'strokeJoin',
    'strokeLeftWeight',
    'strokeMiterLimit',
    'strokeRightWeight',
    'strokeStyleId',
    'strokeTopWeight',
    'strokeWeight',
    'strokes',
    'strokesIncludedInLayout',
    'stuckNodes',
    'targetAspectRatio',
    'toString',
    'topLeftRadius',
    'topRightRadius',
    'unlockAspectRatio',
    'verticalPadding',
    'visible',
    'width'
  ],

  DefaultShapeMixin: [
    'absoluteBoundingBox',
    'absoluteRenderBounds',
    'absoluteTransform',
    'addDevResourceAsync',
    'attachedConnectors',
    'blendMode',
    'boundVariables',
    'clearExplicitVariableModeForCollection',
    'componentPropertyReferences',
    'constrainProportions',
    'dashPattern',
    'deleteDevResourceAsync',
    'editDevResourceAsync',
    'effectStyleId',
    'effects',
    'explicitVariableModes',
    'exportAsync',
    'exportSettings',
    'fillGeometry',
    'fillStyleId',
    'fills',
    'getCSSAsync',
    'getDevResourcesAsync',
    'getPluginData',
    'getPluginDataKeys',
    'getRelaunchData',
    'getSharedPluginData',
    'getSharedPluginDataKeys',
    'getTopLevelFrame',
    'height',
    'id',
    'inferredVariables',
    'isAsset',
    'isMask',
    'layoutAlign',
    'layoutGrow',
    'layoutPositioning',
    'layoutSizingHorizontal',
    'layoutSizingVertical',
    'locked',
    'maskType',
    'maxHeight',
    'maxWidth',
    'minHeight',
    'minWidth',
    'name',
    'opacity',
    'outlineStroke',
    'parent',
    'reactions',
    'relativeTransform',
    'remove',
    'removed',
    'rescale',
    'resize',
    'resizeWithoutConstraints',
    'resolvedVariableModes',
    'rotation',
    'setBoundVariable',
    'setDevResourcePreviewAsync',
    'setEffectStyleIdAsync',
    'setExplicitVariableModeForCollection',
    'setFillStyleIdAsync',
    'setPluginData',
    'setReactionsAsync',
    'setRelaunchData',
    'setSharedPluginData',
    'setStrokeStyleIdAsync',
    'strokeAlign',
    'strokeCap',
    'strokeGeometry',
    'strokeJoin',
    'strokeMiterLimit',
    'strokeStyleId',
    'strokeWeight',
    'strokes',
    'stuckNodes',
    'toString',
    'visible',
    'width'
  ],

  DeprecatedBackgroundMixin: [
    'backgroundStyleId',
    'backgrounds'
  ],

  DevResourcesMixin: [
    'addDevResourceAsync',
    'deleteDevResourceAsync',
    'editDevResourceAsync',
    'getDevResourcesAsync',
    'setDevResourcePreviewAsync'
  ],

  DevStatusMixin: [
    'devStatus'
  ],

  DimensionAndPositionMixin: [
    'absoluteBoundingBox',
    'absoluteTransform',
    'height',
    'maxHeight',
    'maxWidth',
    'minHeight',
    'minWidth',
    'relativeTransform',
    'width'
  ],

  ExplicitVariableModesMixin: [
    'clearExplicitVariableModeForCollection',
    'explicitVariableModes',
    'setExplicitVariableModeForCollection'
  ],

  ExportMixin: [
    'exportAsync',
    'exportSettings'
  ],

  FramePrototypingMixin: [
    'numberOfFixedChildren',
    'overflowDirection',
    'overlayBackground',
    'overlayBackgroundInteraction',
    'overlayPositionType'
  ],

  GeometryMixin: [
    'dashPattern',
    'fillGeometry',
    'fillStyleId',
    'fills',
    'outlineStroke',
    'setFillStyleIdAsync',
    'setStrokeStyleIdAsync',
    'strokeAlign',
    'strokeCap',
    'strokeGeometry',
    'strokeJoin',
    'strokeMiterLimit',
    'strokeStyleId',
    'strokeWeight',
    'strokes'
  ],

  IndividualStrokesMixin: [
    'strokeBottomWeight',
    'strokeLeftWeight',
    'strokeRightWeight',
    'strokeTopWeight'
  ],

  LayoutMixin: [
    'absoluteBoundingBox',
    'absoluteRenderBounds',
    'absoluteTransform',
    'constrainProportions',
    'height',
    'layoutAlign',
    'layoutGrow',
    'layoutPositioning',
    'layoutSizingHorizontal',
    'layoutSizingVertical',
    'maxHeight',
    'maxWidth',
    'minHeight',
    'minWidth',
    'relativeTransform',
    'rescale',
    'resize',
    'resizeWithoutConstraints',
    'rotation',
    'width'
  ],

  MeasurementsMixin: [
    'addMeasurement',
    'deleteMeasurement',
    'editMeasurement',
    'getMeasurements',
    'getMeasurementsForNode'
  ],

  MinimalBlendMixin: [
    'blendMode',
    'opacity'
  ],

  MinimalFillsMixin: [
    'fillStyleId',
    'fills',
    'setFillStyleIdAsync'
  ],

  MinimalStrokesMixin: [
    'dashPattern',
    'setStrokeStyleIdAsync',
    'strokeAlign',
    'strokeGeometry',
    'strokeJoin',
    'strokeStyleId',
    'strokeWeight',
    'strokes'
  ],

  NonResizableTextMixin: [
    'characters',
    'deleteCharacters',
    'fontName',
    'fontSize',
    'fontWeight',
    'getRangeAllFontNames',
    'getRangeBoundVariable',
    'getRangeFillStyleId',
    'getRangeFills',
    'getRangeFontName',
    'getRangeFontSize',
    'getRangeFontWeight',
    'getRangeHyperlink',
    'getRangeIndentation',
    'getRangeLetterSpacing',
    'getRangeLineHeight',
    'getRangeListOptions',
    'getRangeListSpacing',
    'getRangeOpenTypeFeatures',
    'getRangeParagraphIndent',
    'getRangeParagraphSpacing',
    'getRangeTextCase',
    'getRangeTextDecoration',
    'getRangeTextDecorationColor',
    'getRangeTextDecorationOffset',
    'getRangeTextDecorationSkipInk',
    'getRangeTextDecorationStyle',
    'getRangeTextDecorationThickness',
    'getRangeTextStyleId',
    'getStyledTextSegments',
    'hangingList',
    'hangingPunctuation',
    'hasMissingFont',
    'hyperlink',
    'insertCharacters',
    'leadingTrim',
    'letterSpacing',
    'lineHeight',
    'listSpacing',
    'openTypeFeatures',
    'paragraphIndent',
    'paragraphSpacing',
    'setRangeBoundVariable',
    'setRangeFillStyleId',
    'setRangeFillStyleIdAsync',
    'setRangeFills',
    'setRangeFontName',
    'setRangeFontSize',
    'setRangeHyperlink',
    'setRangeIndentation',
    'setRangeLetterSpacing',
    'setRangeLineHeight',
    'setRangeListOptions',
    'setRangeListSpacing',
    'setRangeParagraphIndent',
    'setRangeParagraphSpacing',
    'setRangeTextCase',
    'setRangeTextDecoration',
    'setRangeTextDecorationColor',
    'setRangeTextDecorationOffset',
    'setRangeTextDecorationSkipInk',
    'setRangeTextDecorationStyle',
    'setRangeTextDecorationThickness',
    'setRangeTextStyleId',
    'setRangeTextStyleIdAsync',
    'textCase',
    'textDecoration',
    'textDecorationColor',
    'textDecorationOffset',
    'textDecorationSkipInk',
    'textDecorationStyle',
    'textDecorationThickness'
  ],

  NonResizableTextPathMixin: [
    'characters',
    'deleteCharacters',
    'fontName',
    'fontSize',
    'fontWeight',
    'getRangeAllFontNames',
    'getRangeBoundVariable',
    'getRangeFillStyleId',
    'getRangeFills',
    'getRangeFontName',
    'getRangeFontSize',
    'getRangeFontWeight',
    'getRangeHyperlink',
    'getRangeLetterSpacing',
    'getRangeOpenTypeFeatures',
    'getRangeTextCase',
    'getRangeTextStyleId',
    'getStyledTextSegments',
    'hasMissingFont',
    'hyperlink',
    'insertCharacters',
    'letterSpacing',
    'openTypeFeatures',
    'setRangeBoundVariable',
    'setRangeFillStyleId',
    'setRangeFillStyleIdAsync',
    'setRangeFills',
    'setRangeFontName',
    'setRangeFontSize',
    'setRangeHyperlink',
    'setRangeLetterSpacing',
    'setRangeTextCase',
    'setRangeTextStyleId',
    'setRangeTextStyleIdAsync',
    'textCase'
  ],

  OpaqueNodeMixin: [
    'absoluteBoundingBox',
    'absoluteTransform',
    'addDevResourceAsync',
    'attachedConnectors',
    'boundVariables',
    'clearExplicitVariableModeForCollection',
    'componentPropertyReferences',
    'deleteDevResourceAsync',
    'editDevResourceAsync',
    'explicitVariableModes',
    'exportAsync',
    'exportSettings',
    'getCSSAsync',
    'getDevResourcesAsync',
    'getPluginData',
    'getPluginDataKeys',
    'getRelaunchData',
    'getSharedPluginData',
    'getSharedPluginDataKeys',
    'getTopLevelFrame',
    'height',
    'id',
    'inferredVariables',
    'isAsset',
    'locked',
    'maxHeight',
    'maxWidth',
    'minHeight',
    'minWidth',
    'name',
    'parent',
    'relativeTransform',
    'remove',
    'removed',
    'resolvedVariableModes',
    'setBoundVariable',
    'setDevResourcePreviewAsync',
    'setExplicitVariableModeForCollection',
    'setPluginData',
    'setRelaunchData',
    'setSharedPluginData',
    'stuckNodes',
    'toString',
    'visible',
    'width'
  ],

  PluginDataMixin: [
    'getPluginData',
    'getPluginDataKeys',
    'getSharedPluginData',
    'getSharedPluginDataKeys',
    'setPluginData',
    'setSharedPluginData'
  ],

  PublishableMixin: [
    'description',
    'descriptionMarkdown',
    'documentationLinks',
    'getPublishStatusAsync',
    'key',
    'remote'
  ],

  ReactionMixin: [
    'reactions',
    'setReactionsAsync'
  ],

  RectangleCornerMixin: [
    'bottomLeftRadius',
    'bottomRightRadius',
    'topLeftRadius',
    'topRightRadius'
  ],

  SceneNodeMixin: [
    'attachedConnectors',
    'boundVariables',
    'clearExplicitVariableModeForCollection',
    'componentPropertyReferences',
    'explicitVariableModes',
    'inferredVariables',
    'locked',
    'resolvedVariableModes',
    'setBoundVariable',
    'setExplicitVariableModeForCollection',
    'stuckNodes',
    'visible'
  ],

  StickableMixin: [
    'stuckTo'
  ],

  VariantMixin: [
    'variantProperties'
  ],

  VectorLikeMixin: [
    'handleMirroring',
    'setVectorNetworkAsync',
    'vectorNetwork',
    'vectorPaths'
  ]
} as const;

/**
 * Node-Mixin Inheritance Relationships
 */
export const FIGMA_NODE_MIXINS = {
  DOCUMENT: [
    'BaseNodeMixin'
  ],

  PAGE: [
    'BaseNodeMixin',
    'ChildrenMixin',
    'ExplicitVariableModesMixin',
    'ExportMixin',
    'MeasurementsMixin'
  ],

  FRAME: [
    'DefaultFrameMixin'
  ],

  GROUP: [
    'AspectRatioLockMixin',
    'BaseNodeMixin',
    'BlendMixin',
    'ChildrenMixin',
    'ContainerMixin',
    'DeprecatedBackgroundMixin',
    'ExportMixin',
    'LayoutMixin',
    'ReactionMixin',
    'SceneNodeMixin'
  ],

  TRANSFORM_GROUP: [
    'AspectRatioLockMixin',
    'BaseNodeMixin',
    'BlendMixin',
    'ChildrenMixin',
    'ContainerMixin',
    'DeprecatedBackgroundMixin',
    'ExportMixin',
    'LayoutMixin',
    'ReactionMixin',
    'SceneNodeMixin'
  ],

  SLICE: [
    'BaseNodeMixin',
    'ExportMixin',
    'LayoutMixin',
    'SceneNodeMixin'
  ],

  RECTANGLE: [
    'AnnotationsMixin',
    'AspectRatioLockMixin',
    'ConstraintMixin',
    'CornerMixin',
    'DefaultShapeMixin',
    'IndividualStrokesMixin',
    'RectangleCornerMixin'
  ],

  LINE: [
    'AnnotationsMixin',
    'ConstraintMixin',
    'DefaultShapeMixin'
  ],

  ELLIPSE: [
    'AnnotationsMixin',
    'AspectRatioLockMixin',
    'ConstraintMixin',
    'CornerMixin',
    'DefaultShapeMixin'
  ],

  POLYGON: [
    'AnnotationsMixin',
    'AspectRatioLockMixin',
    'ConstraintMixin',
    'CornerMixin',
    'DefaultShapeMixin'
  ],

  STAR: [
    'AnnotationsMixin',
    'AspectRatioLockMixin',
    'ConstraintMixin',
    'CornerMixin',
    'DefaultShapeMixin'
  ],

  VECTOR: [
    'AnnotationsMixin',
    'AspectRatioLockMixin',
    'ConstraintMixin',
    'CornerMixin',
    'DefaultShapeMixin',
    'VectorLikeMixin'
  ],

  TEXT: [
    'AnnotationsMixin',
    'AspectRatioLockMixin',
    'ConstraintMixin',
    'DefaultShapeMixin',
    'NonResizableTextMixin'
  ],

  TEXT_PATH: [
    'AnnotationsMixin',
    'AspectRatioLockMixin',
    'ConstraintMixin',
    'DefaultShapeMixin',
    'NonResizableTextPathMixin'
  ],

  COMPONENT_SET: [
    'BaseFrameMixin',
    'ComponentPropertiesMixin',
    'PublishableMixin'
  ],

  COMPONENT: [
    'ComponentPropertiesMixin',
    'DefaultFrameMixin',
    'PublishableMixin',
    'VariantMixin'
  ],

  INSTANCE: [
    'DefaultFrameMixin',
    'VariantMixin'
  ],

  BOOLEAN_OPERATION: [
    'AspectRatioLockMixin',
    'ChildrenMixin',
    'ContainerMixin',
    'CornerMixin',
    'DefaultShapeMixin'
  ],

  STICKY: [
    'MinimalBlendMixin',
    'MinimalFillsMixin',
    'OpaqueNodeMixin'
  ],

  STAMP: [
    'AspectRatioLockMixin',
    'ConstraintMixin',
    'DefaultShapeMixin',
    'StickableMixin'
  ],

  TABLE: [
    'MinimalBlendMixin',
    'MinimalFillsMixin',
    'OpaqueNodeMixin'
  ],

  HIGHLIGHT: [
    'AspectRatioLockMixin',
    'ConstraintMixin',
    'CornerMixin',
    'DefaultShapeMixin',
    'StickableMixin',
    'VectorLikeMixin'
  ],

  WASHI_TAPE: [
    'AspectRatioLockMixin',
    'DefaultShapeMixin',
    'StickableMixin'
  ],

  SHAPE_WITH_TEXT: [
    'MinimalBlendMixin',
    'MinimalFillsMixin',
    'MinimalStrokesMixin',
    'OpaqueNodeMixin'
  ],

  CODE_BLOCK: [
    'MinimalBlendMixin',
    'OpaqueNodeMixin'
  ],

  CONNECTOR: [
    'MinimalBlendMixin',
    'MinimalStrokesMixin',
    'OpaqueNodeMixin'
  ],

  WIDGET: [
    'OpaqueNodeMixin',
    'StickableMixin'
  ],

  EMBED: [
    'OpaqueNodeMixin'
  ],

  LINK_UNFURL: [
    'OpaqueNodeMixin'
  ],

  MEDIA: [
    'OpaqueNodeMixin'
  ],

  SECTION: [
    'AspectRatioLockMixin',
    'ChildrenMixin',
    'DevStatusMixin',
    'MinimalFillsMixin',
    'OpaqueNodeMixin'
  ],

  SLIDE: [
    'BaseFrameMixin'
  ],

  SLIDE_ROW: [
    'ChildrenMixin',
    'OpaqueNodeMixin'
  ],

  SLIDE_GRID: [
    'ChildrenMixin',
    'OpaqueNodeMixin'
  ],

  INTERACTIVE_SLIDE_ELEMENT: [
    'OpaqueNodeMixin'
  ]
} as const;

/**
 * Node-specific Properties
 * Note: These are in addition to inherited mixin properties
 */
export const FIGMA_NODE_PROPERTIES = {
  BOOLEAN_OPERATION: [
    'booleanOperation',
    'clone'
  ],

  CODE_BLOCK: [
    'clone',
    'code',
    'codeLanguage'
  ],

  COMPONENT: [
    'clone',
    'createInstance',
    'getInstancesAsync',
    'instances'
  ],

  COMPONENT_SET: [
    'clone',
    'defaultVariant',
    'variantGroupProperties'
  ],

  CONNECTOR: [
    'clone',
    'connectorEnd',
    'connectorEndStrokeCap',
    'connectorLineType',
    'connectorStart',
    'connectorStartStrokeCap',
    'cornerRadius',
    'rotation',
    'text',
    'textBackground'
  ],

  DOCUMENT: [
    'appendChild',
    'children',
    'documentColorProfile',
    'findAll',
    'findAllWithCriteria',
    'findChild',
    'findChildren',
    'findOne',
    'findWidgetNodesByWidgetId',
    'insertChild'
  ],

  ELLIPSE: [
    'arcData',
    'clone'
  ],

  EMBED: [
    'clone',
    'embedData'
  ],

  FRAME: [
    'clone'
  ],

  GROUP: [
    'clone'
  ],

  HIGHLIGHT: [
    'clone'
  ],

  INSTANCE: [
    'clone',
    'componentProperties',
    'detachInstance',
    'exposedInstances',
    'getMainComponentAsync',
    'isExposedInstance',
    'mainComponent',
    'overrides',
    'resetOverrides',
    'scaleFactor',
    'setProperties',
    'swapComponent'
  ],

  INTERACTIVE_SLIDE_ELEMENT: [
    'clone',
    'interactiveSlideElementType'
  ],

  LINE: [
    'clone'
  ],

  LINK_UNFURL: [
    'clone',
    'linkUnfurlData'
  ],

  MEDIA: [
    'clone',
    'mediaData',
    'resize',
    'resizeWithoutConstraints'
  ],

  PAGE: [
    'backgrounds',
    'clone',
    'flowStartingPoints',
    'focusedSlide',
    'guides',
    'isPageDivider',
    'loadAsync',
    'off',
    'on',
    'once',
    'prototypeBackgrounds',
    'prototypeStartNode',
    'selectedTextRange',
    'selection'
  ],

  POLYGON: [
    'clone',
    'pointCount'
  ],

  RECTANGLE: [
    'clone'
  ],

  SECTION: [
    'clone',
    'resizeWithoutConstraints',
    'sectionContentsHidden'
  ],

  SHAPE_WITH_TEXT: [
    'clone',
    'cornerRadius',
    'rescale',
    'resize',
    'rotation',
    'shapeType',
    'text'
  ],

  SLICE: [
    'clone'
  ],

  SLIDE: [
    'clone',
    'getSlideTransition',
    'isSkippedSlide',
    'setSlideTransition'
  ],

  SLIDE_GRID: [
    'clone'
  ],

  SLIDE_ROW: [
    'clone'
  ],

  STAMP: [
    'clone',
    'getAuthorAsync'
  ],

  STAR: [
    'clone',
    'innerRadius',
    'pointCount'
  ],

  STICKY: [
    'authorName',
    'authorVisible',
    'clone',
    'isWideWidth',
    'text'
  ],

  TABLE: [
    'cellAt',
    'clone',
    'insertColumn',
    'insertRow',
    'moveColumn',
    'moveRow',
    'numColumns',
    'numRows',
    'removeColumn',
    'removeRow',
    'resizeColumn',
    'resizeRow'
  ],

  TEXT: [
    'autoRename',
    'clone',
    'maxLines',
    'setTextStyleIdAsync',
    'textAlignHorizontal',
    'textAlignVertical',
    'textAutoResize',
    'textStyleId',
    'textTruncation'
  ],

  TEXT_PATH: [
    'autoRename',
    'clone',
    'handleMirroring',
    'setTextStyleIdAsync',
    'textAlignHorizontal',
    'textAlignVertical',
    'textStyleId',
    'vectorNetwork',
    'vectorPaths'
  ],

  TRANSFORM_GROUP: [
    'clone'
  ],

  VECTOR: [
    'clone'
  ],

  WASHI_TAPE: [
    'clone'
  ],

  WIDGET: [
    'clone',
    'cloneWidget',
    'setWidgetSyncedState',
    'widgetId',
    'widgetSyncedState'
  ]
} as const;

/**
 * Get all properties for a specific node type (including inherited mixins)
 */
export function getNodeProperties(nodeType: FigmaNodeType): string[] {
  const inheritedMixins = FIGMA_NODE_MIXINS[nodeType as keyof typeof FIGMA_NODE_MIXINS] || [];
  const specificProps = FIGMA_NODE_PROPERTIES[nodeType as keyof typeof FIGMA_NODE_PROPERTIES] || [];

  // Collect all properties from inherited mixins
  const mixinProps: string[] = [];
  inheritedMixins.forEach((mixinName: string) => {
    const mixinProperties = FIGMA_MIXINS[mixinName as keyof typeof FIGMA_MIXINS] || [];
    mixinProps.push(...mixinProperties);
  });

  return [...new Set([...mixinProps, ...specificProps])].sort();
}

/**
 * Check if a node type has a specific property
 */
export function hasNodeProperty(nodeType: FigmaNodeType, property: string): boolean {
  return getNodeProperties(nodeType).includes(property);
}
