# Vue 语法高亮测试

请在 Figma 插件中发送包含以下 Vue 代码的消息来测试语法高亮：

```vue
<template>
  <div class="hello">
    <h1 v-if="showTitle">{{ message }}</h1>
    <button @click="toggleTitle">Toggle</button>
    <ul>
      <li v-for="item in items" :key="item.id">
        {{ item.name }}
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'HelloWorld',
  data() {
    return {
      message: 'Hello Vue!',
      showTitle: true,
      items: [
        { id: 1, name: 'Item 1' },
        { id: 2, name: 'Item 2' }
      ]
    }
  },
  methods: {
    toggleTitle() {
      this.showTitle = !this.showTitle
    }
  }
}
</script>

<style scoped>
.hello {
  text-align: center;
}

h1 {
  color: #42b883;
}

button {
  background: #42b883;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
}
</style>
```

## 期望的结果

- Vue 指令 (v-if, v-for, @click) 应该有不同的颜色高亮
- 模板插值 ({{ }}) 应该被高亮显示
- HTML 标签应该有适当的颜色
- JavaScript 部分应该有语法高亮
- CSS 部分应该有样式高亮
- 不应该出现全黑的代码块
- 控制台应该显示 "✅ Vue language registered successfully for syntax highlighting"
