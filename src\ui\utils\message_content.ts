// Utility functions for handling message content with command tokens

import { MessageContent, CommandToken, Command, COMMAND_PREFIX, AVAILABLE_COMMANDS } from '../types/ui_types'

// Create empty message content
export function createEmptyMessageContent(): MessageContent {
  return {
    text: '',
    commandTokens: []
  }
}

// Convert message content to display text with command highlighting
export function messageContentToDisplayText(content: MessageContent): string {
  return content.text
}

// Convert plain text to message content (for backward compatibility)
export function textToMessageContent(text: string): MessageContent {
  return {
    text,
    commandTokens: []
  }
}

// Get the plain text from message content
export function getPlainText(content: MessageContent): string {
  return content.text
}

// Insert a command token at the current cursor position (autocomplete behavior)
export function insertCommandToken(
  content: MessageContent,
  command: Command,
  cursorPos: number
): MessageContent {
  const timestamp = new Date().toISOString().split('T')[1].split('.')[0]
  console.log(`🔧 [${timestamp}] insertCommandToken called`)
  console.log(`   📝 Input text: "${content.text}"`)
  console.log(`   🏷️  Input tokens: ${content.commandTokens.length}`, content.commandTokens)
  console.log(`   💡 Command to insert: "${command.name}"`)
  console.log(`   📍 Cursor position: ${cursorPos}`)

  // Find the command prefix and partial command text before cursor position
  let prefixPos = -1
  const partialCommandEnd = cursorPos

  console.log(`   🔍 Looking for command prefix backwards from position ${cursorPos}`)

  // Look backwards from cursor to find the command prefix
  for (let i = cursorPos - 1; i >= 0; i--) {
    const char = content.text[i]
    console.log(`     Position ${i}: "${char}"`)

    if (char === COMMAND_PREFIX) {
      console.log(`     Found command prefix at position ${i}`)
      // Check if this is a valid command prefix (at start or after whitespace)
      if (i === 0 || /\s/.test(content.text[i - 1])) {
        prefixPos = i
        console.log(`     ✅ Valid command prefix at position ${i}`)
        break
      } else {
        console.log(`     ❌ Invalid command prefix (not at start or after whitespace)`)
      }
    }
    if (/\s/.test(char)) {
      console.log(`     Hit whitespace at position ${i}, stopping search`)
      // Hit whitespace before finding prefix, no command to replace
      break
    }
  }

  console.log(`   🎯 Final prefixPos: ${prefixPos}`)
  console.log(`   🎯 partialCommandEnd: ${partialCommandEnd}`)

  if (prefixPos === -1) {
    console.log(`   🚫 No command prefix found, inserting at cursor position`)
    // No command prefix found, just insert the command at cursor position
    const before = content.text.substring(0, cursorPos)
    const after = content.text.substring(cursorPos)
    const newText = before + command.name + ' ' + after
    console.log(`   📝 New text: "${newText}"`)

    // Create new command token
    const newToken: CommandToken = {
      id: `cmd_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      command: command.name,
      start: cursorPos,
      end: cursorPos + command.name.length
    }
    console.log(`   🏷️  New token:`, newToken)

    // Update existing command tokens positions for tokens after insertion
    const updatedTokens = content.commandTokens.map(token => {
      if (token.start >= cursorPos) {
        const offset = command.name.length + 1
        return {
          ...token,
          start: token.start + offset,
          end: token.end + offset
        }
      }
      return token
    })
    console.log(`   🏷️  Updated tokens:`, updatedTokens)

    const result = {
      text: newText,
      commandTokens: [...updatedTokens, newToken]
    }
    console.log(`   ✅ Returning result (no prefix):`, result)
    return result
  }

  // Found command prefix - replace the partial command with the full command
  // This implements proper autocomplete behavior
  console.log(`   ✅ Found command prefix, implementing autocomplete replacement`)
  const partialCommand = content.text.substring(prefixPos, partialCommandEnd)
  console.log(`   📝 Partial command to replace: "${partialCommand}"`)

  const before = content.text.substring(0, prefixPos)
  const after = content.text.substring(partialCommandEnd)
  const newText = before + command.name + ' ' + after
  console.log(`   📝 Before: "${before}"`)
  console.log(`   📝 After: "${after}"`)
  console.log(`   📝 New text: "${newText}"`)

  // Create new command token
  const newToken: CommandToken = {
    id: `cmd_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    command: command.name,
    start: prefixPos,
    end: prefixPos + command.name.length
  }
  console.log(`   🏷️  New token:`, newToken)

  // Remove any existing tokens that overlap with the replacement area
  // and update positions for tokens after the replacement
  const replacementLength = partialCommandEnd - prefixPos
  const newCommandLength = command.name.length
  const lengthDiff = newCommandLength + 1 - replacementLength // +1 for space
  console.log(`   📏 Replacement length: ${replacementLength}`)
  console.log(`   📏 New command length: ${newCommandLength}`)
  console.log(`   📏 Length diff: ${lengthDiff}`)

  const updatedTokens = content.commandTokens
    .filter(token => {
      // Remove tokens that overlap with the replacement area
      const overlaps = token.start < partialCommandEnd && token.end > prefixPos
      console.log(`     🔍 Token "${token.command}" [${token.start}-${token.end}] overlaps: ${overlaps}`)
      return !overlaps
    })
    .map(token => {
      if (token.start >= partialCommandEnd) {
        console.log(`     🔄 Adjusting token "${token.command}" position by ${lengthDiff}`)
        // Adjust positions for tokens after the replacement
        return {
          ...token,
          start: token.start + lengthDiff,
          end: token.end + lengthDiff
        }
      }
      return token
    })
  console.log(`   🏷️  Updated tokens:`, updatedTokens)

  const result = {
    text: newText,
    commandTokens: [...updatedTokens, newToken]
  }
  console.log(`   ✅ Returning result (autocomplete):`, result)
  return result
}

// Remove command token at cursor position (for backspace handling)
export function removeCommandTokenAtCursor(
  content: MessageContent,
  cursorPos: number
): { content: MessageContent; deletedToken: CommandToken | null } {
  // Find command token that contains or is adjacent to cursor position
  const tokenToDelete = content.commandTokens.find(token =>
    cursorPos >= token.start && cursorPos <= token.end + 1
  )

  if (!tokenToDelete) {
    return { content, deletedToken: null }
  }

  // Remove the command text from the message
  const before = content.text.substring(0, tokenToDelete.start)
  const after = content.text.substring(tokenToDelete.end)
  const newText = before + after

  // Update remaining command tokens positions
  const updatedTokens = content.commandTokens
    .filter(token => token.id !== tokenToDelete.id)
    .map(token => {
      if (token.start > tokenToDelete.end) {
        const offset = tokenToDelete.end - tokenToDelete.start
        return {
          ...token,
          start: token.start - offset,
          end: token.end - offset
        }
      }
      return token
    })

  return {
    content: {
      text: newText,
      commandTokens: updatedTokens
    },
    deletedToken: tokenToDelete
  }
}

// Optimized update message content with new text (handles typing)
export function updateMessageContentText(
  content: MessageContent,
  newText: string,
  cursorPos: number
): MessageContent {
  // Early return if no actual change
  if (content.text === newText) {
    return content
  }

  const textDiff = newText.length - content.text.length

  // If text is shorter, some content was deleted
  if (textDiff < 0) {
    // Handle deletion - remove any command tokens that are now invalid
    const validTokens = content.commandTokens.filter(token => {
      // Quick bounds check first
      if (token.end > newText.length) return false
      const tokenText = newText.substring(token.start, token.end)
      return tokenText === token.command
    })

    return {
      text: newText,
      commandTokens: validTokens
    }
  }

  // If text is longer, content was added
  if (textDiff > 0) {
    // Update command token positions for insertions
    let updatedTokens = content.commandTokens.map(token => {
      if (token.start >= cursorPos - textDiff) {
        return {
          ...token,
          start: token.start + textDiff,
          end: token.end + textDiff
        }
      }
      return token
    })

    // Only check for new command tokens if the text contains command prefix
    if (newText.includes(COMMAND_PREFIX)) {
      updatedTokens = detectAndCreateCommandTokens(newText, updatedTokens)
    }

    return {
      text: newText,
      commandTokens: updatedTokens
    }
  }

  // No change in length - only check for new commands if text contains command prefix
  const updatedTokens = newText.includes(COMMAND_PREFIX)
    ? detectAndCreateCommandTokens(newText, content.commandTokens)
    : content.commandTokens

  return {
    text: newText,
    commandTokens: updatedTokens
  }
}

// Detect and create command tokens in text
function detectAndCreateCommandTokens(text: string, existingTokens: CommandToken[]): CommandToken[] {
  const tokens = [...existingTokens]
  const commandRegex = new RegExp(`\\${COMMAND_PREFIX}([a-zA-Z0-9-]+)`, 'g')
  let match

  // Find all potential commands in the text
  while ((match = commandRegex.exec(text)) !== null) {
    const commandStart = match.index
    const commandEnd = commandStart + match[0].length
    const commandText = match[0]

    // Check if this position already has a command token
    const existingToken = tokens.find(token =>
      token.start === commandStart && token.end === commandEnd
    )

    if (!existingToken) {
      // Check if this is a valid command
      const isValidCommand = AVAILABLE_COMMANDS.some(cmd => cmd.name === commandText)

      if (isValidCommand) {
        // Create new command token
        const newToken: CommandToken = {
          id: `cmd_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          command: commandText,
          start: commandStart,
          end: commandEnd
        }
        tokens.push(newToken)
      }
    }
  }

  // Remove tokens that no longer match the text
  return tokens.filter(token => {
    const tokenText = text.substring(token.start, token.end)
    return tokenText === token.command
  })
}

// Check if cursor is in a position where suggestions should be shown
export function shouldShowSuggestions(content: MessageContent, cursorPos: number): boolean {
  // Only show suggestions when we're typing after a command prefix
  let prefixPos = -1
  for (let i = cursorPos - 1; i >= 0; i--) {
    if (content.text[i] === COMMAND_PREFIX) {
      if (i === 0 || /\s/.test(content.text[i - 1])) {
        prefixPos = i
        break
      }
    }
    if (/\s/.test(content.text[i])) {
      break
    }
  }

  if (prefixPos === -1) return false

  // Check if we're not inside an existing command token
  const isInCommandToken = content.commandTokens.some(token =>
    cursorPos >= token.start && cursorPos <= token.end
  )

  return !isInCommandToken
}

// Get suggestion query from current cursor position
export function getSuggestionQuery(content: MessageContent, cursorPos: number): string {
  let prefixPos = -1
  for (let i = cursorPos - 1; i >= 0; i--) {
    if (content.text[i] === COMMAND_PREFIX) {
      if (i === 0 || /\s/.test(content.text[i - 1])) {
        prefixPos = i
        break
      }
    }
    if (/\s/.test(content.text[i])) {
      break
    }
  }

  if (prefixPos === -1) return ''

  return content.text.substring(prefixPos + 1, cursorPos).toLowerCase()
}
