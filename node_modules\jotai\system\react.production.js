'use client';
System.register(["react","jotai/vanilla","jotai/vanilla/internals"],function(V){"use strict";var y,p,C,E,v,R,j,m,x,w,D,d;return{setters:[function(n){y=n.createContext,p=n.useContext,C=n.useRef,E=n.createElement,v=n.default,R=n.useReducer,j=n.useEffect,m=n.useDebugValue,x=n.useCallback},function(n){w=n.getDefaultStore,D=n.createStore},function(n){d=n.INTERNAL_registerAbortHandler}],execute:function(){V({Provider:k,useAtom:H,useAtomValue:P,useSetAtom:T,useStore:A});const n=y(void 0);function A(t){const r=p(n);return(t==null?void 0:t.store)||r||w()}function k({children:t,store:r}){const s=C(void 0);return!r&&!s.current&&(s.current=D()),E(n.Provider,{value:r||s.current},t)}const b=t=>typeof(t==null?void 0:t.then)=="function",S=t=>{t.status||(t.status="pending",t.then(r=>{t.status="fulfilled",t.value=r},r=>{t.status="rejected",t.reason=r}))},_=v.use||(t=>{if(t.status==="pending")throw t;if(t.status==="fulfilled")return t.value;throw t.status==="rejected"?t.reason:(S(t),t)}),h=new WeakMap,N=(t,r)=>{let s=h.get(t);return s||(s=new Promise((c,u)=>{let f=t;const l=e=>o=>{f===e&&c(o)},g=e=>o=>{f===e&&u(o)},a=()=>{try{const e=r();b(e)?(h.set(e,s),f=e,e.then(l(e),g(e)),d(e,a)):c(e)}catch(e){u(e)}};t.then(l(t),g(t)),d(t,a)}),h.set(t,s)),s};function P(t,r){const{delay:s,unstable_promiseStatus:c=!v.use}=r||{},u=A(r),[[f,l,g],a]=R(o=>{const i=u.get(t);return Object.is(o[0],i)&&o[1]===u&&o[2]===t?o:[i,u,t]},void 0,()=>[u.get(t),u,t]);let e=f;if((l!==u||g!==t)&&(a(),e=u.get(t)),j(()=>{const o=u.sub(t,()=>{if(c)try{const i=u.get(t);b(i)&&S(N(i,()=>u.get(t)))}catch(i){}if(typeof s=="number"){setTimeout(a,s);return}a()});return a(),o},[u,t,s,c]),m(e),b(e)){const o=N(e,()=>u.get(t));return c&&S(o),_(o)}return e}function T(t,r){const s=A(r);return x((...c)=>s.set(t,...c),[s,t])}function H(t,r){return[P(t,r),T(t,r)]}}}});
