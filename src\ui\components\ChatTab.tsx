import { useRef, useCallback, useState, useEffect } from 'react'
import {
  useMount,
  useUnmount,
  useUpdateEffect,
  useCopyToClipboard,
  useTimeoutFn,
  useDebounce
} from 'react-use'
import { ChatInterface } from './ChatInterface.tsx'

import { useCommandSystem } from '../utils/command_system'
import { Message, Command, MessageContent } from '../types/ui_types'
import { COMMAND_PREFIX } from '../config/commands'
import {
  getPlainText,
  insertCommandToken,
  shouldShowSuggestions,
  createEmptyMessageContent,
  updateMessageContentText
} from '../utils/message_content'
import { useChatMessages, useCommandSuggestions, useCopiedMessage, useMessageContent, useConversationManager, useVendorStorage, useOpenRouterStorage, useDeepSeekStorage, useConversationStorage, useTerminologyStorage, useAiConversation } from '../store'
import { useUnified<PERSON>i<PERSON><PERSON> } from '../hooks/useUnifiedAiApi'
import { ModelAutocomplete } from './ModelAutocomplete'
import { VendorSelector } from './VendorSelector'
import { ConversationManager } from './ConversationManager'

// Utility function to parse conversation title into timestamp and description
function parseConversationTitle(title: string): { timestamp: string; description: string } {
  // Check if title matches the format "YY-MM-DD HH:MM:SS - Description"
  const timestampMatch = title.match(/^(\d{2}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) - (.+)$/)

  if (timestampMatch) {
    return {
      timestamp: timestampMatch[1],
      description: timestampMatch[2]
    }
  }

  // Fallback for titles that don't match the expected format
  return {
    timestamp: '',
    description: title
  }
}

interface ChatTabProps {
  // Future props for customization
}

// Performance configuration
const MAX_MESSAGES = 500 // Limit total messages for performance
const CLEANUP_THRESHOLD = 600 // Start cleanup when exceeding this

export function ChatTab(_props: ChatTabProps) {
  // Use Jotai hooks for state management
  const { messages, setMessages, clearMessages: _clearChatMessages } = useChatMessages()

  // Conversation management (moved up to be available in useEffect)
  const conversationManager = useConversationManager()

  // Add welcome message only when no conversation is active and no messages exist
  useEffect(() => {
    if (messages.length === 0 && !conversationManager.currentConversationId) {
      const welcomeMessage: Message = {
        id: 'welcome-' + Date.now(),
        text: 'Welcome to FigmaAgent! 🎨 Start chatting or use commands like `/help` to get started.',
        timestamp: new Date(),
        type: 'system'
      }
      setMessages(prev => [...prev, welcomeMessage])
      console.log('📝 [ChatTab] Added welcome message - no active conversation')
    }
  }, [messages.length, setMessages, conversationManager.currentConversationId])










  const { messageContent: messageContentValue, setMessageContent } = useMessageContent()
  const {
    showSuggestions,
    suggestions: commandSuggestions,
    selectedIndex,
    setShowSuggestions,
    setSuggestions,
    setSelectedIndex,
    hideSuggestions
  } = useCommandSuggestions()
  const {
    copiedMessageId,
    setCopiedMessageId,
    clearCopiedMessage
  } = useCopiedMessage()

  // Unified AI functionality
  const unifiedAiApi = useUnifiedAiApi()
  const vendorStorage = useVendorStorage()
  const openRouterStorage = useOpenRouterStorage()
  const deepSeekStorage = useDeepSeekStorage()
  const { aiConversation, setAiConversation } = useAiConversation()

  // Model input state - use the config directly instead of local state
  const [isModelUpdating, setIsModelUpdating] = useState(false)

  // Title editing state
  const [isEditingTitle, setIsEditingTitle] = useState(false)
  const [editingTitle, setEditingTitle] = useState('')

  // Debounced model update to avoid too many API calls
  useDebounce(
    async () => {
      // This will be handled by the ModelAutocomplete component's onChange
      // No need for debouncing here since it's handled in the component
    },
    500, // 500ms delay
    []
  )

  // Use react-use hooks for copy functionality
  const [copyState, copyToClipboardHook] = useCopyToClipboard()
  const [, , resetClearTimeout] = useTimeoutFn(() => clearCopiedMessage(), 2000)

  // Local refs (not global state)
  const editableRef = useRef<HTMLDivElement>(null) as React.RefObject<HTMLDivElement>

  // Message cleanup function for performance
  const cleanupMessages = useCallback((newMessages: Message[]) => {
    if (newMessages.length > CLEANUP_THRESHOLD) {
      // Keep only the most recent MAX_MESSAGES
      const cleaned = newMessages.slice(-MAX_MESSAGES)
      return cleaned
    }
    return newMessages
  }, [])

  // Enhanced setMessages with automatic cleanup (using Jotai)
  const setMessagesWithCleanup = useCallback((updater: React.SetStateAction<Message[]>) => {
    setMessages(prev => {
      const newMessages = typeof updater === 'function' ? updater(prev) : updater
      return cleanupMessages(newMessages)
    })
  }, [cleanupMessages, setMessages])

  // Initialize command system
  const commandSystem = useCommandSystem({
    setMessages: setMessagesWithCleanup,
    unifiedAiApi: unifiedAiApi
  })

  // Extract state from command system
  const {
    executeCommand,
    getSuggestions
  } = commandSystem

  // Auto-scroll is now handled entirely by ChatInterface component
  // No need for duplicate scroll logic here

  // Update current conversation when messages change
  useUpdateEffect(() => {
    if (messages.length > 0 && conversationManager.currentConversation) {
      conversationManager.updateCurrentConversation()
    }
  }, [messages])

  // Sync contentEditable DOM with messageContentValue state (optimized)
  const prevMessageText = useRef(messageContentValue.text)
  useUpdateEffect(() => {
    // Only update DOM if text actually changed and it's not from user input
    if (editableRef.current &&
        editableRef.current.textContent !== messageContentValue.text &&
        prevMessageText.current !== messageContentValue.text) {

      // Store current cursor position
      const selection = window.getSelection()
      let cursorPos = 0
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0)
        const preCaretRange = range.cloneRange()
        preCaretRange.selectNodeContents(editableRef.current)
        preCaretRange.setEnd(range.endContainer, range.endOffset)
        cursorPos = preCaretRange.toString().length
      }

      // Update DOM content
      editableRef.current.textContent = messageContentValue.text

      // Restore cursor position
      if (selection && messageContentValue.text.length > 0) {
        const safePos = Math.min(cursorPos, messageContentValue.text.length)
        const range = document.createRange()
        const textNode = editableRef.current.firstChild
        if (textNode && textNode.nodeType === Node.TEXT_NODE) {
          range.setStart(textNode, safePos)
          range.setEnd(textNode, safePos)
          selection.removeAllRanges()
          selection.addRange(range)
        }
      }
    }
    prevMessageText.current = messageContentValue.text
  }, [messageContentValue.text])

  // Note: Figma message handling is now done through figma/client.ts in command_system.ts

  // Cleanup on unmount
  useUnmount(() => {
    // Cleanup logic if needed
  })



  // Copy to clipboard function using react-use
  const copyToClipboard = useCallback((text: string, messageId: string) => {
    copyToClipboardHook(text)

    if (copyState.error) {
      console.error('Failed to copy text: ', copyState.error)
      alert('Failed to copy text. Please copy manually.')
    } else {
      setCopiedMessageId(messageId)
      resetClearTimeout()
    }
  }, [copyToClipboardHook, copyState.error, setCopiedMessageId, resetClearTimeout])

  // Multi-select functionality
  const [selectedMessageIds, setSelectedMessageIds] = useState<Set<string>>(new Set())
  const [isSelectionMode, setIsSelectionMode] = useState(false)

  // Auto-scroll control
  const [forceScrollToBottom, setForceScrollToBottom] = useState(false)

  // Reset force scroll flag after a short delay
  useEffect(() => {
    if (forceScrollToBottom) {
      const timer = setTimeout(() => {
        setForceScrollToBottom(false)
      }, 100) // Reset after 100ms
      return () => clearTimeout(timer)
    }
  }, [forceScrollToBottom])

  // Toggle message selection
  const toggleMessageSelection = useCallback((messageId: string) => {
    setSelectedMessageIds(prev => {
      const newSet = new Set(prev)
      if (newSet.has(messageId)) {
        newSet.delete(messageId)
      } else {
        newSet.add(messageId)
      }
      return newSet
    })
  }, [])

  // Toggle select all messages (select all if none/some selected, deselect all if all selected)
  const selectAllMessages = useCallback(() => {
    const allMessageIds = new Set(messages.map(msg => msg.id))
    const allSelected = selectedMessageIds.size === messages.length && messages.length > 0

    if (allSelected) {
      // If all are selected, deselect all
      setSelectedMessageIds(new Set())
    } else {
      // If none or some are selected, select all
      setSelectedMessageIds(allMessageIds)
    }
  }, [messages, selectedMessageIds])

  // Clear selection
  const clearSelection = useCallback(() => {
    setSelectedMessageIds(new Set())
    setIsSelectionMode(false)
  }, [])

  // Delete selected messages
  const deleteSelectedMessages = useCallback(() => {
    if (selectedMessageIds.size === 0) return

    setMessagesWithCleanup(prev => prev.filter(msg => !selectedMessageIds.has(msg.id)))
    clearSelection()
  }, [selectedMessageIds, setMessagesWithCleanup, clearSelection])

  // Copy selected messages to clipboard
  const copySelectedMessages = useCallback(() => {
    if (selectedMessageIds.size === 0) return

    const selectedMessages = messages.filter(msg => selectedMessageIds.has(msg.id))

    // Sort messages by timestamp to maintain chronological order
    selectedMessages.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())

    // Format messages with timestamps and separators
    const formattedText = selectedMessages.map(msg => {
      const timestamp = new Date(msg.timestamp).toLocaleString()
      const sender = msg.type === 'user' ? 'You' :
                    msg.type === 'ai' ? `${msg.vendor || 'AI'}:${msg.model || 'unknown'}` :
                    'System'
      return `[${timestamp}] ${sender}:\n${msg.text}`
    }).join('\n\n---\n\n')

    copyToClipboardHook(formattedText)

    if (copyState.error) {
      console.error('Failed to copy selected messages: ', copyState.error)
      alert('Failed to copy messages. Please copy manually.')
    } else {
      // Show success feedback briefly
      setCopiedMessageId('selected-messages')
      setTimeout(() => setCopiedMessageId(null), 2000)
    }
  }, [selectedMessageIds, messages, copyToClipboardHook, copyState.error, setCopiedMessageId])

  // Resend message functionality
  const handleResendMessage = useCallback(async (aiMessageId: string) => {
    // Find the AI message
    const aiMessageIndex = messages.findIndex(msg => msg.id === aiMessageId)
    if (aiMessageIndex === -1) {
      console.error('❌ AI message not found for resend:', aiMessageId)
      return
    }

    // Find the user message that prompted this AI response
    let userMessage: Message | null = null
    for (let i = aiMessageIndex - 1; i >= 0; i--) {
      if (messages[i].type === 'user') {
        userMessage = messages[i]
        break
      }
    }

    if (!userMessage) {
      console.error('❌ No user message found to resend for AI message:', aiMessageId)
      return
    }

    console.log('🔄 Resending user message:', userMessage.text)

    // Don't resend if AI is currently streaming
    if (unifiedAiApi.streaming) {
      console.log('⏸️ Cannot resend while AI is streaming')
      return
    }

    // STEP 1: Immediately delete both the AI response and the original user message from chat
    console.log('🗑️ Removing AI message and original user message before resend:', aiMessageId, userMessage.id)
    setMessagesWithCleanup(prev => prev.filter(msg => msg.id !== aiMessageId && msg.id !== userMessage.id))

    // STEP 2: Clean up AI conversation state by removing both the user and assistant messages
    // This ensures the AI conversation state stays in sync with the chat messages
    console.log('🧹 Cleaning up AI conversation state for resend')
    setAiConversation(prev => {
      // Remove the last assistant message and its corresponding user message from the conversation
      const filteredConversation = [...prev]

      // Remove the last assistant message
      for (let i = filteredConversation.length - 1; i >= 0; i--) {
        if (filteredConversation[i].role === 'assistant') {
          filteredConversation.splice(i, 1)
          console.log('🗑️ Removed assistant message from AI conversation at index:', i)
          break
        }
      }

      // Remove the last user message (which should now be the last message)
      for (let i = filteredConversation.length - 1; i >= 0; i--) {
        if (filteredConversation[i].role === 'user') {
          filteredConversation.splice(i, 1)
          console.log('🗑️ Removed user message from AI conversation at index:', i)
          break
        }
      }

      return filteredConversation
    })

    // STEP 3: Trigger force scroll for resent messages
    setForceScrollToBottom(true)

    try {
      // Check if it's a command (contains command tokens or starts with @)
      const messageText = userMessage.text.trim()
      const isDirectCommand = messageText.startsWith(COMMAND_PREFIX)

      if (isDirectCommand) {
        // Execute command
        await executeCommand(messageText)
        return
      }

      // Send to AI provider - require API key configuration
      if (unifiedAiApi.config.apiKey) {
        try {
          await unifiedAiApi.sendMultiTurnStreamingMessage(messageText)
        } catch (error) {
          console.error('❌ [AI] Resend failed:', error instanceof Error ? error.message : String(error))
          // Add error message to chat
          const errorMessage: Message = {
            id: Date.now().toString() + Math.random(),
            text: `❌ AI Resend Error: ${error instanceof Error ? error.message : 'Unknown error'}. Please check your API key configuration in Settings.`,
            timestamp: new Date(),
            type: 'system'
          }
          setMessagesWithCleanup(prev => [...prev, errorMessage])
        }
      } else {
        // Add user message first
        const userResendMessage: Message = {
          id: Date.now().toString() + Math.random(),
          text: messageText,
          timestamp: new Date(),
          type: 'user'
        }
        setMessagesWithCleanup(prev => [...prev, userResendMessage])

        // Show helpful configuration message
        const configMessage: Message = {
          id: Date.now().toString() + Math.random(),
          text: `⚙️ Please configure your ${vendorStorage.vendor === 'deepseek' ? 'DeepSeek' : 'OpenRouter'} API key in Settings to start chatting with the Agent.`,
          timestamp: new Date(),
          type: 'system'
        }
        setMessagesWithCleanup(prev => [...prev, configMessage])
      }
    } catch (error) {
      console.error('💥 [ChatTab] Failed to resend message:', error)
    }
  }, [messages, unifiedAiApi, setForceScrollToBottom, executeCommand, setMessagesWithCleanup, vendorStorage.vendor, setAiConversation])

  // Enter selection mode when long-pressing or right-clicking a message
  const enterSelectionMode = useCallback(() => {
    setIsSelectionMode(true)
  }, [])



  // Message handling functions
  const sendMessage = async () => {
    // Don't send if AI is currently streaming
    if (unifiedAiApi.streaming) {
      return
    }

    const messageText = getPlainText(messageContentValue).trim()

    if (!messageText) {
      return
    }



    // Clear input immediately for better UX
    clearInputBox()

    // Trigger force scroll for user messages
    setForceScrollToBottom(true)

    try {
      // Check if it's a command (contains command tokens or starts with @)
      const hasCommandTokens = messageContentValue.commandTokens.length > 0
      const isDirectCommand = messageText.startsWith(COMMAND_PREFIX)

      if (hasCommandTokens || isDirectCommand) {
        // Execute commands from command tokens
        if (hasCommandTokens) {
          for (const token of messageContentValue.commandTokens) {
            await executeCommand(token.command)
          }
        }
        // Execute direct command if it's not already in tokens
        if (isDirectCommand && !hasCommandTokens) {
          await executeCommand(messageText)
        }
        return
      }

      // Send to AI provider - require API key configuration
      if (unifiedAiApi.config.apiKey) {
        try {
          await unifiedAiApi.sendMultiTurnStreamingMessage(messageText)
        } catch (error) {
          console.error('❌ [AI] Failed:', error instanceof Error ? error.message : String(error))
          // Add error message to chat instead of falling back to WebSocket
          const errorMessage: Message = {
            id: Date.now().toString() + Math.random(),
            text: `❌ AI Error: ${error instanceof Error ? error.message : 'Unknown error'}. Please check your API key configuration in Settings.`,
            timestamp: new Date(),
            type: 'system'
          }
          setMessagesWithCleanup(prev => [...prev, errorMessage])
        }
      } else {
        // Add user message first
        const userMessage: Message = {
          id: Date.now().toString() + Math.random(),
          text: messageText,
          timestamp: new Date(),
          type: 'user'
        }
        setMessagesWithCleanup(prev => [...prev, userMessage])

        // Show helpful configuration message instead of WebSocket fallback
        const configMessage: Message = {
          id: Date.now().toString() + Math.random(),
          text: `⚙️ Please configure your ${vendorStorage.vendor === 'deepseek' ? 'DeepSeek' : 'OpenRouter'} API key in Settings to start chatting with the Agent.`,
          timestamp: new Date(),
          type: 'system'
        }
        setMessagesWithCleanup(prev => [...prev, configMessage])
      }
    } catch (error) {
      console.error('💥 [ChatTab] Failed to send message:', error)
    }
  }

  const clearInputBox = () => {
    setMessageContent(createEmptyMessageContent())
    setShowSuggestions(false)
    setSuggestions([])
    setSelectedIndex(0)
    if (editableRef.current) {
      editableRef.current.innerHTML = ''
      editableRef.current.focus()

      setTimeout(() => {
        if (editableRef.current) {
          const selection = window.getSelection()
          if (selection) {
            const range = document.createRange()
            range.selectNodeContents(editableRef.current)
            range.collapse(false)
            selection.removeAllRanges()
            selection.addRange(range)
          }
        }
      }, 0)
    }
  }

  // Cached cursor position to avoid repeated DOM queries
  const getCursorPosition = useCallback(() => {
    const selection = window.getSelection()
    if (!selection || selection.rangeCount === 0) return messageContentValue.text.length

    const range = selection.getRangeAt(0)
    const preCaretRange = range.cloneRange()
    if (editableRef.current) {
      preCaretRange.selectNodeContents(editableRef.current)
      preCaretRange.setEnd(range.endContainer, range.endOffset)
      return preCaretRange.toString().length
    }
    return messageContentValue.text.length
  }, [messageContentValue.text])

  // Debounced suggestion processing to reduce computational overhead
  const [debouncedMessageContent, setDebouncedMessageContent] = useState(messageContentValue)
  useDebounce(() => {
    setDebouncedMessageContent(messageContentValue)
  }, 100, [messageContentValue]) // Reduced debounce time for better responsiveness

  // Throttled input processing to prevent excessive updates
  const [lastInputTime, setLastInputTime] = useState(0)
  const INPUT_THROTTLE_MS = 16 // ~60fps throttling

  // Optimized message content change handler with immediate UI update and debounced processing
  const handleMessageContentChange = useCallback((newContent: MessageContent) => {
    // Immediate UI update for responsiveness
    setMessageContent(newContent)

    // Early exit if no command prefix to avoid unnecessary processing
    if (!newContent.text.includes(COMMAND_PREFIX)) {
      if (showSuggestions) {
        hideSuggestions()
      }
      return
    }

    // Use cached cursor position
    const cursorPos = getCursorPosition()

    // Check if we should show suggestions (immediate for better UX)
    if (shouldShowSuggestions(newContent, cursorPos)) {
      const currentSuggestions = getSuggestions(newContent.text, cursorPos)
      setSuggestions(currentSuggestions)
      setShowSuggestions(true)
      setSelectedIndex(0)
    } else {
      hideSuggestions()
    }
  }, [setMessageContent, showSuggestions, hideSuggestions, setSuggestions, setShowSuggestions, setSelectedIndex, getCursorPosition])

  // Suggestion handling
  const handleSuggestionClick = (suggestion: Command) => {
    // Get current cursor position in text content (not DOM offset)
    let cursorPos = messageContentValue.text.length // Default to end
    if (editableRef.current) {
      const selection = window.getSelection()
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0)
        const preCaretRange = range.cloneRange()
        preCaretRange.selectNodeContents(editableRef.current)
        preCaretRange.setEnd(range.endContainer, range.endOffset)
        cursorPos = preCaretRange.toString().length
      }
    }

    // Use the insertCommandToken utility function
    const newContent = insertCommandToken(messageContentValue, suggestion, cursorPos)

    // Use the message content change handler to ensure proper display updates
    handleMessageContentChange(newContent)

    // Focus the input and position cursor after the inserted command
    if (editableRef.current) {
      setTimeout(() => {
        if (!editableRef.current) {
          return
        }

        editableRef.current.focus()

        const insertedToken = newContent.commandTokens.find(token =>
          token.command === suggestion.name
        )
        if (!insertedToken) {
          return
        }

        const targetPosition = insertedToken.end + 1

        const selection = window.getSelection()
        if (!selection) {
          return
        }

        let currentPos = 0
        let targetNode: Node | null = null
        let targetOffset = 0
        const walker = document.createTreeWalker(
          editableRef.current,
          NodeFilter.SHOW_TEXT,
          null
        )

        let node
        while ((node = walker.nextNode())) {
          const nodeLength = node.textContent?.length || 0
          if (currentPos + nodeLength >= targetPosition) {
            targetNode = node
            targetOffset = targetPosition - currentPos
            break
          }
          currentPos += nodeLength
        }

        if (!targetNode) {
          return
        }

        const safeOffset = Math.min(targetOffset, targetNode.textContent?.length || 0)
        const range = document.createRange()
        range.setStart(targetNode, safeOffset)
        range.setEnd(targetNode, safeOffset)
        selection.removeAllRanges()
        selection.addRange(range)
      }, 100)
    }

    hideSuggestions()
  }

  const handleSuggestionMouseEnter = (index: number) => {
    setSelectedIndex(index)
  }

  // Model input change handler (immediate UI update, debounced save)
  const handleModelInputChange = useCallback(async (newModel: string) => {
    const currentConfig = unifiedAiApi.vendor === 'deepseek' ? deepSeekStorage.config : openRouterStorage.config
    const updateConfig = unifiedAiApi.vendor === 'deepseek' ? deepSeekStorage.updateConfig : openRouterStorage.updateConfig

    // Allow empty values so users can clear the field completely
    const trimmedModel = newModel.trim()
    if (trimmedModel !== currentConfig.model) {
      setIsModelUpdating(true)
      try {
        await updateConfig({ model: trimmedModel })
      } catch (error) {
        console.error(`❌ [ChatTab] Failed to update ${unifiedAiApi.vendor} model configuration:`, error)
      } finally {
        setIsModelUpdating(false)
      }
    }
  }, [unifiedAiApi.vendor, openRouterStorage, deepSeekStorage])

  return (
    <div className="flex flex-col h-full p-3 sm:p-4">
      {/* Conversation Management Header */}
      <div className="flex items-center justify-between mb-3 gap-3 flex-shrink-0">
        <div className="flex items-center gap-2 flex-1 min-w-0 max-w-[calc(100%-100px)]">
          {isEditingTitle ? (
            <input
              type="text"
              value={editingTitle}
              onChange={(e) => setEditingTitle(e.target.value)}
              onBlur={() => {
                if (editingTitle.trim()) {
                  conversationManager.updateConversationTitle(editingTitle.trim())
                }
                setIsEditingTitle(false)
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  if (editingTitle.trim()) {
                    conversationManager.updateConversationTitle(editingTitle.trim())
                  }
                  setIsEditingTitle(false)
                } else if (e.key === 'Escape') {
                  setIsEditingTitle(false)
                  setEditingTitle(conversationManager.currentConversation?.title || '')
                }
              }}
              className="text-sm font-medium text-gray-700 bg-transparent border-b border-gray-300 focus:outline-none focus:border-blue-500 flex-1 min-w-0 max-w-full"
              autoFocus
            />
          ) : (
            <div
              className="cursor-pointer hover:opacity-80 transition-opacity flex-1 min-w-0 max-w-full"
              onClick={() => {
                setEditingTitle(conversationManager.currentConversation?.title || '')
                setIsEditingTitle(true)
              }}
              title={`${conversationManager.currentConversation?.title || 'New Conversation'} (Click to edit)`}
            >
              {(() => {
                const { timestamp, description } = parseConversationTitle(conversationManager.currentConversation?.title || 'New Conversation')
                return (
                  <div className="space-y-0.5">
                    {timestamp && (
                      <div className="text-xs text-gray-400 truncate max-w-full overflow-hidden text-ellipsis whitespace-nowrap">
                        {timestamp}
                      </div>
                    )}
                    <h2 className="text-sm font-medium text-gray-700 truncate max-w-full overflow-hidden text-ellipsis whitespace-nowrap">
                      {description}
                    </h2>
                  </div>
                )
              })()}
            </div>
          )}
        </div>
        <div className="flex items-center gap-1 flex-shrink-0">

          {/* Select Messages button - icon only */}
          {!isSelectionMode && messages.length > 0 && (
            <button
              onClick={enterSelectionMode}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              title="Select messages to copy or delete"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 11H15M9 15H15M17 21H7C5.89543 21 5 20.1046 5 19V5C5 3.89543 5.89543 3 7 3H12.5858C12.851 3 13.1054 3.10536 13.2929 3.29289L19.7071 9.70711C19.8946 9.89464 20 10.149 20 10.4142V19C20 20.1046 19.1046 21 18 21H17Z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7H10" />
              </svg>
            </button>
          )}
          <button
            onClick={conversationManager.toggleManager}
            className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            title="Manage conversations"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.013 8.013 0 01-7-4L5 20l.94-5.94A8.013 8.013 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
            </svg>
          </button>
          <button
            onClick={() => conversationManager.createNewConversation()}
            className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            title="New conversation"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
          </button>
          <button
            onClick={() => {
              // Stop streaming if active before clearing
              if (unifiedAiApi.streaming) {
                unifiedAiApi.abortStreaming()
              }
              conversationManager.clearCurrentConversation()
            }}
            className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            title="Clear conversation"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
        </div>
      </div>



      <ChatInterface
        messages={messages}
        copiedMessageId={copiedMessageId}
        onCopyMessage={copyToClipboard}
        selectedMessageIds={selectedMessageIds}
        isSelectionMode={isSelectionMode}
        onToggleSelection={toggleMessageSelection}
        onSelectAll={selectAllMessages}
        onClearSelection={clearSelection}
        onDeleteSelected={deleteSelectedMessages}
        onCopySelected={copySelectedMessages}
        forceScrollToBottom={forceScrollToBottom}
        onResendMessage={handleResendMessage}
      />

      {/* Message Input with Custom Controls */}
      <div className="space-y-3 flex-shrink-0">
        {/* Input Box with Suggestion Overlay */}
        <div className="relative">
          {/* Suggestion Box - Positioned above input */}
          {showSuggestions && commandSuggestions.length > 0 && (
            <div className="absolute bottom-full left-0 right-0 mb-2 z-50 bg-white border border-gray-200 rounded-lg shadow-2xl font-sans max-h-48 overflow-hidden">
              <div className="max-h-full overflow-y-auto">
                {commandSuggestions.map((suggestion, index) => (
                  <div
                    key={suggestion.name}
                    className={`suggestion-item px-2 py-1 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors duration-100 ${
                      index === selectedIndex ? 'bg-blue-50 border-blue-200' : 'hover:bg-gray-50'
                    }`}
                    title={suggestion.description}
                    onMouseDown={(e) => {
                      e.preventDefault()
                      handleSuggestionClick(suggestion)
                    }}
                    onMouseEnter={() => handleSuggestionMouseEnter(index)}
                  >
                    <div className={`font-medium text-xs leading-tight ${
                      index === selectedIndex ? 'text-blue-600' : 'text-gray-800'
                    }`}>
                      {suggestion.name}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div
            ref={editableRef}
            contentEditable
            className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 h-[120px] overflow-y-auto whitespace-pre-wrap text-sm"
            style={{
              fontFamily: 'inherit',
              lineHeight: '1.4'
            }}
            data-placeholder="Enter your message or use /sel-figma, /sel-twhtml, /help... (Shift+Enter for new line)"
            suppressContentEditableWarning={true}
            onInput={(e) => {
              const now = performance.now()

              // Throttle input processing to prevent excessive updates
              if (now - lastInputTime < INPUT_THROTTLE_MS) {
                return
              }
              setLastInputTime(now)

              const newText = e.currentTarget.textContent || ''

              // Quick optimization: skip expensive cursor calculation if text is just appended
              let cursorPos = newText.length
              if (newText.length > messageContentValue.text.length &&
                  newText.startsWith(messageContentValue.text)) {
                // Text was appended, cursor is at end
                cursorPos = newText.length
              } else {
                // Need to calculate actual cursor position
                const selection = window.getSelection()
                if (selection && selection.rangeCount > 0) {
                  const range = selection.getRangeAt(0)
                  const preCaretRange = range.cloneRange()
                  preCaretRange.selectNodeContents(e.currentTarget)
                  preCaretRange.setEnd(range.endContainer, range.endOffset)
                  cursorPos = preCaretRange.toString().length
                }
              }

              // Use the optimized message content update function
              const updatedContent = updateMessageContentText(messageContentValue, newText, cursorPos)
              handleMessageContentChange(updatedContent)
            }}
            onKeyDown={(e) => {
              if (showSuggestions && commandSuggestions.length > 0) {
                if (e.key === 'ArrowDown') {
                  e.preventDefault()
                  const newIndex = selectedIndex < commandSuggestions.length - 1 ? selectedIndex + 1 : 0
                  handleSuggestionMouseEnter(newIndex)
                } else if (e.key === 'ArrowUp') {
                  e.preventDefault()
                  const newIndex = selectedIndex > 0 ? selectedIndex - 1 : commandSuggestions.length - 1
                  handleSuggestionMouseEnter(newIndex)
                } else if (e.key === 'Enter') {
                  e.preventDefault()
                  handleSuggestionClick(commandSuggestions[selectedIndex])
                } else if (e.key === 'Escape') {
                  e.preventDefault()
                  hideSuggestions()
                }
              } else if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault()
                if (!unifiedAiApi.streaming) {
                  sendMessage()
                }
              }
            }}
            onBlur={() => {
              setTimeout(() => {
                hideSuggestions()
              }, 150)
            }}
          />
        </div>

        {/* Controls Row - Vendor, Model Input and Send Button */}
        <div className="flex items-center justify-between gap-2">
          {/* Vendor Selection */}
          <VendorSelector
            vendor={vendorStorage.vendor}
            onVendorChange={vendorStorage.saveVendor}
            isLoading={vendorStorage.loadingVendor}
          />

          {/* Model Selection Input - Always show since AI is always enabled */}
          <ModelAutocomplete
            value={unifiedAiApi.vendor === 'deepseek' ? deepSeekStorage.config.model : openRouterStorage.config.model}
            onChange={handleModelInputChange}
            vendor={unifiedAiApi.vendor}
            placeholder={unifiedAiApi.vendor === 'deepseek' ? "Model: deepseek-chat, deepseek-coder..." : "Model: gpt-4o-mini, claude-3.5-sonnet..."}
            isUpdating={isModelUpdating}
          />

          {/* Send/Stop Button */}
          {unifiedAiApi.streaming ? (
            // Stop Button - shown during AI streaming
            <button
              onClick={unifiedAiApi.abortStreaming}
              className="w-8 h-8 bg-red-600 text-white rounded-lg flex items-center justify-center hover:bg-red-700 transition-colors"
              title="Stop AI Response"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="6" y="6" width="12" height="12" fill="currentColor"/>
              </svg>
            </button>
          ) : (
            // Send Button - normal state
            <button
              onClick={sendMessage}
              disabled={!getPlainText(messageContentValue).trim()}
              className="w-8 h-8 bg-blue-600 text-white rounded-lg flex items-center justify-center hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
              title="Send (Enter)"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M2 21L23 12L2 3V10L17 12L2 14V21Z" fill="currentColor"/>
              </svg>
            </button>
          )}
        </div>
      </div>

      {/* Conversation Manager Modal */}
      <ConversationManager
        isOpen={conversationManager.showManager}
        onClose={() => conversationManager.setShowManager(false)}
      />
    </div>
  )
}
