{"compilerOptions": {"target": "ESNext", "lib": ["ESNext", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "strict": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "typeRoots": ["./node_modules/@types", "./node_modules/@figma"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "scripts/*.ts", "vite.config.plugin.ts", "vite.config.ui.ts"]}