// Performance utilities for optimizing ChatInterface with large content

import { Message } from '../types/ui_types'

// Debounce function for performance optimization
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

// Throttle function for scroll events
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean = false
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// Analyze message content for performance optimization
export interface MessageAnalysis {
  hasLargeJson: boolean
  hasLargeCodeBlocks: boolean
  estimatedSize: number
  contentType: 'text' | 'json' | 'code' | 'mixed'
  optimizationRecommendation: 'none' | 'collapse' | 'virtualize' | 'paginate'
}

export function analyzeMessage(message: Message): MessageAnalysis {
  const text = message.text
  const textLength = text.length
  
  // Check for JSON content
  const jsonMatches = text.match(/```json[\s\S]*?```/g)
  const hasLargeJson = jsonMatches ? jsonMatches.some(match => 
    match.length > 10000 || match.split('\n').length > 50
  ) : false
  
  // Check for other code blocks
  const codeMatches = text.match(/```[\s\S]*?```/g)
  const hasLargeCodeBlocks = codeMatches ? codeMatches.some(match => 
    !match.includes('```json') && (match.length > 5000 || match.split('\n').length > 30)
  ) : false
  
  // Determine content type
  let contentType: MessageAnalysis['contentType'] = 'text'
  if (jsonMatches && jsonMatches.length > 0) {
    contentType = codeMatches && codeMatches.length > jsonMatches.length ? 'mixed' : 'json'
  } else if (codeMatches && codeMatches.length > 0) {
    contentType = 'code'
  }
  
  // Determine optimization recommendation
  let optimizationRecommendation: MessageAnalysis['optimizationRecommendation'] = 'none'
  if (hasLargeJson) {
    optimizationRecommendation = 'collapse'
  } else if (hasLargeCodeBlocks) {
    optimizationRecommendation = 'virtualize'
  } else if (textLength > 50000) {
    optimizationRecommendation = 'paginate'
  }
  
  return {
    hasLargeJson,
    hasLargeCodeBlocks,
    estimatedSize: textLength,
    contentType,
    optimizationRecommendation
  }
}

// Memory usage monitoring
export interface MemoryStats {
  usedJSHeapSize: number
  totalJSHeapSize: number
  jsHeapSizeLimit: number
  isMemoryPressure: boolean
}

export function getMemoryStats(): MemoryStats | null {
  if ('memory' in performance) {
    const memory = (performance as any).memory
    const usedMB = memory.usedJSHeapSize / 1024 / 1024
    const totalMB = memory.totalJSHeapSize / 1024 / 1024
    const limitMB = memory.jsHeapSizeLimit / 1024 / 1024
    
    return {
      usedJSHeapSize: usedMB,
      totalJSHeapSize: totalMB,
      jsHeapSizeLimit: limitMB,
      isMemoryPressure: usedMB > limitMB * 0.8 // 80% threshold
    }
  }
  return null
}

// Optimize message list for performance
export function optimizeMessageList(messages: Message[]): {
  optimizedMessages: Message[]
  totalSavings: number
  optimizationApplied: boolean
} {
  let totalSavings = 0
  let optimizationApplied = false
  
  const optimizedMessages = messages.map(message => {
    const analysis = analyzeMessage(message)
    
    if (analysis.optimizationRecommendation === 'collapse' && analysis.hasLargeJson) {
      // For large JSON, we'll mark it for collapsed rendering
      // The actual optimization happens in the JsonRenderer component
      optimizationApplied = true
      totalSavings += analysis.estimatedSize * 0.8 // Estimate 80% savings from collapsing
      
      return {
        ...message,
        metadata: {
          ...message.metadata,
          hasLargeContent: true,
          contentType: 'json',
          originalSize: analysis.estimatedSize
        }
      }
    }
    
    return message
  })
  
  return {
    optimizedMessages,
    totalSavings,
    optimizationApplied
  }
}

// Performance monitoring hook data
export interface PerformanceMetrics {
  renderTime: number
  domNodes: number
  memoryUsage: number
  messageCount: number
  largeMessageCount: number
  inputLatency: number
  scrollPerformance: number
}

// Calculate performance score (0-100, higher is better)
export function calculatePerformanceScore(metrics: PerformanceMetrics): number {
  let score = 100
  
  // Render time penalty (target: <16ms for 60fps)
  if (metrics.renderTime > 16) {
    score -= Math.min(30, (metrics.renderTime - 16) * 2)
  }
  
  // DOM nodes penalty (target: <1000 nodes)
  if (metrics.domNodes > 1000) {
    score -= Math.min(20, (metrics.domNodes - 1000) / 100)
  }
  
  // Memory usage penalty (target: <50MB)
  if (metrics.memoryUsage > 50) {
    score -= Math.min(25, (metrics.memoryUsage - 50) / 2)
  }
  
  // Input latency penalty (target: <50ms)
  if (metrics.inputLatency > 50) {
    score -= Math.min(15, (metrics.inputLatency - 50) / 5)
  }
  
  // Large message penalty
  if (metrics.largeMessageCount > 5) {
    score -= Math.min(10, (metrics.largeMessageCount - 5) * 2)
  }
  
  return Math.max(0, Math.round(score))
}

// Get performance recommendations
export function getPerformanceRecommendations(metrics: PerformanceMetrics): string[] {
  const recommendations: string[] = []
  
  if (metrics.renderTime > 33) {
    recommendations.push('Consider reducing message complexity or enabling virtualization')
  }
  
  if (metrics.domNodes > 2000) {
    recommendations.push('Too many DOM nodes - virtualization may help')
  }
  
  if (metrics.memoryUsage > 100) {
    recommendations.push('High memory usage - consider message pagination')
  }
  
  if (metrics.inputLatency > 100) {
    recommendations.push('Input lag detected - optimize message rendering')
  }
  
  if (metrics.largeMessageCount > 10) {
    recommendations.push('Many large messages - enable content collapsing')
  }
  
  if (recommendations.length === 0) {
    recommendations.push('Performance looks good!')
  }
  
  return recommendations
}

// Cleanup utility for removing old messages to free memory
export function cleanupOldMessages(
  messages: Message[], 
  maxMessages: number = 1000,
  preserveRecent: number = 100
): Message[] {
  if (messages.length <= maxMessages) {
    return messages
  }
  
  // Keep the most recent messages and some older ones for context
  const recentMessages = messages.slice(-preserveRecent)
  const olderMessages = messages.slice(0, messages.length - preserveRecent)
  
  // Keep every nth older message to maintain some history
  const keepEveryN = Math.ceil(olderMessages.length / (maxMessages - preserveRecent))
  const filteredOlderMessages = olderMessages.filter((_, index) => index % keepEveryN === 0)
  
  return [...filteredOlderMessages, ...recentMessages]
}
