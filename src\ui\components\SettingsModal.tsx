import { useState, useCallback, useRef, useEffect } from 'react'
import { useMount, useDebounce, useKey } from 'react-use'
import { DEFAULT_WINDOW_SETTINGS, WindowSettings, getFigmaClient } from '../../lib/figma'
import { useOpenRouterStorage, useDeepSeekStorage, useVendorStorage, useTerminologyStorage, useCodeBlockSettingsStorage } from '../store'
import { ModelAutocomplete } from './ModelAutocomplete'
import { AIVendor, DEEPSEEK_MODELS, TerminologySettings, OpenRouterConfig, DeepSeekConfig, CodeBlockSettings } from '../types/ui_types'

interface SettingsModalProps {
  isOpen: boolean
  onClose: () => void
}

// Utility functions for API key masking
const maskApiKey = (apiKey: string): string => {
  if (!apiKey || apiKey.length === 0) return ''

  // For keys starting with 'sk-', show the prefix and mask the rest
  if (apiKey.startsWith('sk-')) {
    const prefix = apiKey.substring(0, 3) // 'sk-'
    const masked = '•'.repeat(Math.max(16, apiKey.length - 3))
    return prefix + masked
  }

  // For other keys, mask most of it but show first few characters
  if (apiKey.length <= 8) {
    return '•'.repeat(apiKey.length)
  }

  const visibleChars = Math.min(4, Math.floor(apiKey.length * 0.2))
  const prefix = apiKey.substring(0, visibleChars)
  const masked = '•'.repeat(apiKey.length - visibleChars)
  return prefix + masked
}

const isApiKeyConfigured = (apiKey: string): boolean => {
  return apiKey && apiKey.trim().length > 0
}



// Interface for tracking pending changes
interface PendingChanges {
  windowSettings?: WindowSettings
  vendor?: AIVendor
  terminology?: Partial<TerminologySettings>
  openRouter?: Partial<OpenRouterConfig>
  deepSeek?: Partial<DeepSeekConfig>
  codeBlockSettings?: Partial<CodeBlockSettings>
}

export function SettingsModal({ isOpen, onClose }: SettingsModalProps) {
  const [settings, setSettings] = useState<WindowSettings>(DEFAULT_WINDOW_SETTINGS)
  const [customWidth, setCustomWidth] = useState('')
  const [customHeight, setCustomHeight] = useState('')

  // Change tracking
  const [pendingChanges, setPendingChanges] = useState<PendingChanges>({})
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [saveSuccess, setSaveSuccess] = useState(false)
  const originalValues = useRef<any>({})

  // Storage hooks (but we won't use their auto-save functions)
  const vendorStorage = useVendorStorage()
  const terminologyStorage = useTerminologyStorage()
  const openRouterStorage = useOpenRouterStorage()
  const deepSeekStorage = useDeepSeekStorage()
  const codeBlockSettingsStorage = useCodeBlockSettingsStorage()

  // Current values for display - use global state for vendor to ensure synchronization
  const currentVendor = vendorStorage.vendor // Use global state directly for real-time sync
  const [currentTerminology, setCurrentTerminology] = useState(terminologyStorage.terminology)
  const [currentCodeBlockSettings, setCurrentCodeBlockSettings] = useState(codeBlockSettingsStorage.codeBlockSettings)

  // OpenRouter configuration
  const { config: openRouterConfig, updateConfig: updateOpenRouterConfig } = useOpenRouterStorage()
  const [openRouterApiKey, setOpenRouterApiKey] = useState('')
  const [openRouterApiKeyMasked, setOpenRouterApiKeyMasked] = useState(false)
  const [openRouterTemperature, setOpenRouterTemperature] = useState('')
  const [openRouterMaxTokens, setOpenRouterMaxTokens] = useState('')
  const [openRouterSystemPrompt, setOpenRouterSystemPrompt] = useState('')

  // DeepSeek configuration
  const { config: deepSeekConfig, updateConfig: updateDeepSeekConfig } = useDeepSeekStorage()
  const [deepSeekApiKey, setDeepSeekApiKey] = useState('')
  const [deepSeekApiKeyMasked, setDeepSeekApiKeyMasked] = useState(false)
  const [deepSeekTemperature, setDeepSeekTemperature] = useState('')
  const [deepSeekMaxTokens, setDeepSeekMaxTokens] = useState('')
  const [deepSeekSystemPrompt, setDeepSeekSystemPrompt] = useState('')

  const [isModelUpdating, setIsModelUpdating] = useState(false)

  // Track if we're currently saving to prevent form reinitialization during save
  const isSavingRef = useRef(false)

  // Helper function to track changes
  const trackChange = useCallback((category: keyof PendingChanges, changes: any) => {
    setPendingChanges(prev => ({
      ...prev,
      [category]: { ...prev[category], ...changes }
    }))
    setHasUnsavedChanges(true)
    setSaveSuccess(false) // Clear any previous save success message
  }, [])

  // Helper function to check if there are any pending changes
  const hasPendingChanges = useCallback(() => {
    return Object.keys(pendingChanges).length > 0 && hasUnsavedChanges
  }, [pendingChanges, hasUnsavedChanges])

  // Save all pending changes
  const saveAllChanges = useCallback(async () => {
    if (!hasPendingChanges()) return

    setIsSaving(true)
    isSavingRef.current = true
    try {
      // Save window settings
      if (pendingChanges.windowSettings) {
        await getFigmaClient().setWindowSettings(pendingChanges.windowSettings)
        setSettings(pendingChanges.windowSettings)
      }

      // Save vendor selection
      if (pendingChanges.vendor) {
        await vendorStorage.saveVendor(pendingChanges.vendor)
      }

      // Save terminology
      if (pendingChanges.terminology) {
        const newTerminology = { ...terminologyStorage.terminology, ...pendingChanges.terminology }
        await terminologyStorage.saveTerminology(newTerminology)
      }

      // Save OpenRouter config
      if (pendingChanges.openRouter) {
        const newConfig = { ...openRouterStorage.config, ...pendingChanges.openRouter }
        await openRouterStorage.updateConfig(newConfig)
      }

      // Save DeepSeek config
      if (pendingChanges.deepSeek) {
        const newConfig = { ...deepSeekStorage.config, ...pendingChanges.deepSeek }
        await deepSeekStorage.updateConfig(newConfig)
      }

      // Save code block settings
      if (pendingChanges.codeBlockSettings) {
        const newSettings = { ...codeBlockSettingsStorage.codeBlockSettings, ...pendingChanges.codeBlockSettings }
        await codeBlockSettingsStorage.updateCodeBlockSettings(newSettings)
      }

      // Clear pending changes
      setPendingChanges({})
      setHasUnsavedChanges(false)
      setSaveSuccess(true)

      // Clear success message after 2 seconds
      setTimeout(() => setSaveSuccess(false), 2000)

      console.log('✅ [Settings] All changes saved successfully')
    } catch (error) {
      console.error('❌ [Settings] Failed to save changes:', error)
    } finally {
      setIsSaving(false)
      isSavingRef.current = false
    }
  }, [pendingChanges, hasPendingChanges, vendorStorage, terminologyStorage, openRouterStorage, deepSeekStorage, codeBlockSettingsStorage])

  // Discard all pending changes
  const discardChanges = useCallback(() => {
    // Reset to original values (vendor is now global state, no need to reset)
    setCurrentTerminology(terminologyStorage.terminology)
    setCurrentCodeBlockSettings(codeBlockSettingsStorage.codeBlockSettings)
    setCustomWidth(originalValues.current.width || settings.width.toString())
    setCustomHeight(originalValues.current.height || settings.height.toString())

    // Reset API key fields to their original masked state
    const openRouterHasKey = isApiKeyConfigured(openRouterStorage.config.apiKey)
    if (openRouterHasKey) {
      setOpenRouterApiKey(maskApiKey(openRouterStorage.config.apiKey))
      setOpenRouterApiKeyMasked(true)
    } else {
      setOpenRouterApiKey('')
      setOpenRouterApiKeyMasked(false)
    }

    const deepSeekHasKey = isApiKeyConfigured(deepSeekStorage.config.apiKey)
    if (deepSeekHasKey) {
      setDeepSeekApiKey(maskApiKey(deepSeekStorage.config.apiKey))
      setDeepSeekApiKeyMasked(true)
    } else {
      setDeepSeekApiKey('')
      setDeepSeekApiKeyMasked(false)
    }

    // Reset other fields
    setOpenRouterTemperature(openRouterStorage.config.temperature.toString())
    setOpenRouterMaxTokens(openRouterStorage.config.maxTokens.toString())
    setOpenRouterSystemPrompt(openRouterStorage.config.systemPrompt)
    setDeepSeekTemperature(deepSeekStorage.config.temperature.toString())
    setDeepSeekMaxTokens(deepSeekStorage.config.maxTokens.toString())
    setDeepSeekSystemPrompt(deepSeekStorage.config.systemPrompt)

    // Clear pending changes
    setPendingChanges({})
    setHasUnsavedChanges(false)
    setSaveSuccess(false)
  }, [vendorStorage, terminologyStorage, openRouterStorage, deepSeekStorage, codeBlockSettingsStorage, settings])

  // Use react-use debounce for auto-apply
  const [debouncedApply] = useDebounce(
    () => {
      const widthNum = parseInt(customWidth)
      const heightNum = parseInt(customHeight)
      if (!isNaN(widthNum) && !isNaN(heightNum) &&
          widthNum >= 200 && widthNum <= 1200 &&
          heightNum >= 300 && heightNum <= 1000) {
        saveSettings({ width: widthNum, height: heightNum })
      }
    },
    500
  )

  // Load settings from Figma storage on mount
  useMount(async () => {
    try {
      const stored = await getFigmaClient().getWindowSettings(DEFAULT_WINDOW_SETTINGS)
      setSettings(stored)
      setCustomWidth(stored.width.toString())
      setCustomHeight(stored.height.toString())

      // Store original values for reset functionality
      originalValues.current = {
        width: stored.width.toString(),
        height: stored.height.toString(),
        vendor: vendorStorage.vendor,
        terminology: { ...terminologyStorage.terminology },
        openRouter: { ...openRouterStorage.config },
        deepSeek: { ...deepSeekStorage.config },
        codeBlockSettings: { ...codeBlockSettingsStorage.codeBlockSettings }
      }

      // Initialize current values (vendor is now global state, no need to set)
      setCurrentTerminology(terminologyStorage.terminology)
      setCurrentCodeBlockSettings(codeBlockSettingsStorage.codeBlockSettings)

      // Initialize OpenRouter settings with masking
      const openRouterHasKey = isApiKeyConfigured(openRouterStorage.config.apiKey)
      if (openRouterHasKey) {
        setOpenRouterApiKey(maskApiKey(openRouterStorage.config.apiKey))
        setOpenRouterApiKeyMasked(true)
      } else {
        setOpenRouterApiKey('')
        setOpenRouterApiKeyMasked(false)
      }
      setOpenRouterTemperature(openRouterStorage.config.temperature.toString())
      setOpenRouterMaxTokens(openRouterStorage.config.maxTokens.toString())
      setOpenRouterSystemPrompt(openRouterStorage.config.systemPrompt)

      // Initialize DeepSeek settings with masking
      const deepSeekHasKey = isApiKeyConfigured(deepSeekStorage.config.apiKey)
      if (deepSeekHasKey) {
        setDeepSeekApiKey(maskApiKey(deepSeekStorage.config.apiKey))
        setDeepSeekApiKeyMasked(true)
      } else {
        setDeepSeekApiKey('')
        setDeepSeekApiKeyMasked(false)
      }

      setDeepSeekTemperature(deepSeekStorage.config.temperature.toString())
      setDeepSeekMaxTokens(deepSeekStorage.config.maxTokens.toString())
      setDeepSeekSystemPrompt(deepSeekStorage.config.systemPrompt)

      // Clear any pending changes when modal opens
      setPendingChanges({})
      setHasUnsavedChanges(false)
      setSaveSuccess(false)
    } catch (error) {
      console.error('Failed to load settings:', error)
      setCustomWidth(DEFAULT_WINDOW_SETTINGS.width.toString())
      setCustomHeight(DEFAULT_WINDOW_SETTINGS.height.toString())
    }
  })



  // Reinitialize form when storage hooks finish loading (only when modal first opens)
  useEffect(() => {
    if (isOpen && !vendorStorage.loadingVendor && !deepSeekStorage.loadingConfig && !openRouterStorage.loadingConfig && !codeBlockSettingsStorage.isLoading && !hasUnsavedChanges && !isSavingRef.current) {
      // Only reinitialize if there are no unsaved changes and we're not currently saving
      // Vendor is now global state, no need to reset it
      setCurrentTerminology(terminologyStorage.terminology)
      setCurrentCodeBlockSettings(codeBlockSettingsStorage.codeBlockSettings)

      // Initialize OpenRouter settings
      const openRouterHasKey = isApiKeyConfigured(openRouterStorage.config.apiKey)
      if (openRouterHasKey) {
        setOpenRouterApiKey(maskApiKey(openRouterStorage.config.apiKey))
        setOpenRouterApiKeyMasked(true)
      } else {
        setOpenRouterApiKey('')
        setOpenRouterApiKeyMasked(false)
      }
      setOpenRouterTemperature(openRouterStorage.config.temperature.toString())
      setOpenRouterMaxTokens(openRouterStorage.config.maxTokens.toString())
      setOpenRouterSystemPrompt(openRouterStorage.config.systemPrompt)

      // Initialize DeepSeek settings with masking
      const deepSeekHasKey = isApiKeyConfigured(deepSeekStorage.config.apiKey)
      if (deepSeekHasKey) {
        setDeepSeekApiKey(maskApiKey(deepSeekStorage.config.apiKey))
        setDeepSeekApiKeyMasked(true)
      } else {
        setDeepSeekApiKey('')
        setDeepSeekApiKeyMasked(false)
      }

      setDeepSeekTemperature(deepSeekStorage.config.temperature.toString())
      setDeepSeekMaxTokens(deepSeekStorage.config.maxTokens.toString())
      setDeepSeekSystemPrompt(deepSeekStorage.config.systemPrompt)
    }
  }, [isOpen, vendorStorage.loadingVendor, deepSeekStorage.loadingConfig, openRouterStorage.loadingConfig, codeBlockSettingsStorage.isLoading, hasUnsavedChanges])

  // Handle ESC key to close settings modal with unsaved changes warning
  useKey('Escape', () => {
    if (isOpen) {
      handleClose()
    }
  }, {}, [isOpen])

  // Handle keyboard shortcuts with useEffect for better reliability
  useEffect(() => {
    if (!isOpen) return

    const handleKeyDown = (e: KeyboardEvent) => {
      // Handle Ctrl+S (Windows/Linux) or Cmd+S (Mac)
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault()
        e.stopPropagation()
        console.log('🔧 [Settings] Ctrl+S pressed, saving changes...')
        saveAllChanges()
        return
      }
    }

    // Add event listener to document to capture all keyboard events
    document.addEventListener('keydown', handleKeyDown, { capture: true })

    return () => {
      document.removeEventListener('keydown', handleKeyDown, { capture: true })
    }
  }, [isOpen, saveAllChanges])

  // Handle close with unsaved changes warning
  const handleClose = useCallback(() => {
    if (hasPendingChanges()) {
      discardChanges()
    }
    onClose()
  }, [hasPendingChanges, discardChanges, onClose])

  // No cleanup needed for react-use debounce

  // Save settings to Figma storage and apply to Figma
  const saveSettings = async (newSettings: WindowSettings) => {
    try {
      await getFigmaClient().setWindowSettings(newSettings)
      setSettings(newSettings)
    } catch (error) {
      console.error('❌ [Settings] Failed to save window settings:', error)
    }
  }



  // Handle keyboard shortcuts for width input
  const handleWidthKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      // Trigger immediate auto apply
      const widthNum = parseInt(customWidth)
      const heightNum = parseInt(customHeight)
      if (!isNaN(widthNum) && !isNaN(heightNum) &&
          widthNum >= 200 && widthNum <= 1200 &&
          heightNum >= 300 && heightNum <= 1000) {
        autoApplySettings(customWidth, customHeight)
      }
    }
  }

  // Handle keyboard shortcuts for height input
  const handleHeightKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      // Trigger immediate auto apply
      const widthNum = parseInt(customWidth)
      const heightNum = parseInt(customHeight)
      if (!isNaN(widthNum) && !isNaN(heightNum) &&
          widthNum >= 200 && widthNum <= 1200 &&
          heightNum >= 300 && heightNum <= 1000) {
        autoApplySettings(customWidth, customHeight)
      }
    }
  }

  // Auto apply settings when values change
  const autoApplySettings = async (width: string, height: string) => {
    const widthNum = parseInt(width)
    const heightNum = parseInt(height)

    // Only apply if both values are valid
    if (!isNaN(widthNum) && !isNaN(heightNum) &&
        widthNum >= 200 && widthNum <= 1200 &&
        heightNum >= 300 && heightNum <= 1000) {
      await saveSettings({ width: widthNum, height: heightNum })
    }
  }

  // Handle width input change with change tracking
  const handleWidthChange = (value: string) => {
    setCustomWidth(value)
    const widthNum = parseInt(value)
    const heightNum = parseInt(customHeight)
    if (!isNaN(widthNum) && !isNaN(heightNum) &&
        widthNum >= 200 && widthNum <= 1200 &&
        heightNum >= 300 && heightNum <= 1000) {
      trackChange('windowSettings', { width: widthNum, height: heightNum })
    }
  }

  // Handle height input change with change tracking
  const handleHeightChange = (value: string) => {
    setCustomHeight(value)
    const widthNum = parseInt(customWidth)
    const heightNum = parseInt(value)
    if (!isNaN(widthNum) && !isNaN(heightNum) &&
        widthNum >= 200 && widthNum <= 1200 &&
        heightNum >= 300 && heightNum <= 1000) {
      trackChange('windowSettings', { width: widthNum, height: heightNum })
    }
  }



  // OpenRouter API Key handlers
  const handleOpenRouterApiKeyFocus = () => {
    if (openRouterApiKeyMasked) {
      // Clear the masked value and allow editing
      setOpenRouterApiKey('')
      setOpenRouterApiKeyMasked(false)
    }
  }

  const handleOpenRouterApiKeyChange = (value: string) => {
    setOpenRouterApiKey(value)
    setOpenRouterApiKeyMasked(false)
    trackChange('openRouter', { apiKey: value })
  }

  const handleOpenRouterApiKeyBlur = () => {
    // If the field is empty and we have a stored key, show masked version
    if (!openRouterApiKey.trim() && isApiKeyConfigured(openRouterStorage.config.apiKey)) {
      setOpenRouterApiKey(maskApiKey(openRouterStorage.config.apiKey))
      setOpenRouterApiKeyMasked(true)
    } else if (openRouterApiKey.trim()) {
      // If user entered a new key, keep it visible for a moment then mask it
      setTimeout(() => {
        if (isApiKeyConfigured(openRouterApiKey)) {
          setOpenRouterApiKey(maskApiKey(openRouterApiKey))
          setOpenRouterApiKeyMasked(true)
        }
      }, 1000)
    }
  }

  // DeepSeek API Key handlers
  const handleDeepSeekApiKeyFocus = () => {
    if (deepSeekApiKeyMasked) {
      // Clear the masked value and allow editing
      setDeepSeekApiKey('')
      setDeepSeekApiKeyMasked(false)
    }
  }

  const handleDeepSeekApiKeyChange = (value: string) => {
    setDeepSeekApiKey(value)
    setDeepSeekApiKeyMasked(false)
    trackChange('deepSeek', { apiKey: value })
  }

  const handleDeepSeekApiKeyBlur = () => {
    // If the field is empty and we have a stored key, show masked version
    if (!deepSeekApiKey.trim() && isApiKeyConfigured(deepSeekStorage.config.apiKey)) {
      setDeepSeekApiKey(maskApiKey(deepSeekStorage.config.apiKey))
      setDeepSeekApiKeyMasked(true)
    } else if (deepSeekApiKey.trim()) {
      // If user entered a new key, keep it visible for a moment then mask it
      setTimeout(() => {
        if (isApiKeyConfigured(deepSeekApiKey)) {
          setDeepSeekApiKey(maskApiKey(deepSeekApiKey))
          setDeepSeekApiKeyMasked(true)
        }
      }, 1000)
    }
  }

  // Reset to default
  const resetToDefault = async () => {
    setCustomWidth(DEFAULT_WINDOW_SETTINGS.width.toString())
    setCustomHeight(DEFAULT_WINDOW_SETTINGS.height.toString())
    await saveSettings(DEFAULT_WINDOW_SETTINGS)
  }

  // Don't render modal if not open
  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl p-4 w-full max-w-sm sm:max-w-md md:max-w-lg lg:max-w-xl xl:max-w-2xl h-auto max-h-[90vh] overflow-y-auto">
        {/* Header with Save/Cancel buttons */}
        <div className="flex items-center justify-between mb-4 sm:mb-6">
          <h2 className="text-base sm:text-lg font-semibold text-gray-900">⚙️ Settings</h2>

          <div className="flex items-center gap-3">
            {/* Unsaved changes indicator */}
            <div className="flex items-center gap-2">
              {hasUnsavedChanges && (
                <span className="text-xs text-orange-600 flex items-center gap-1">
                  <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
                  Unsaved
                </span>
              )}
              {saveSuccess && (
                <span className="text-xs text-green-600 flex items-center gap-1">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  Saved
                </span>
              )}
            </div>

            {/* Action buttons */}
            <div className="flex items-center gap-2">
              <button
                onClick={discardChanges}
                disabled={!hasUnsavedChanges}
                className="px-2 py-1 text-xs text-gray-600 border border-gray-300 rounded hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Cancel
              </button>
              <button
                onClick={saveAllChanges}
                disabled={!hasUnsavedChanges || isSaving}
                className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1"
              >
                {isSaving ? (
                  <>
                    <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin"></div>
                    Saving...
                  </>
                ) : (
                  'Save'
                )}
              </button>
            </div>

            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 transition-colors p-1"
            >
              <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Keyboard shortcut hint */}
        {!hasUnsavedChanges && !saveSuccess && (
          <div className="mb-4 text-xs text-gray-500 text-center">
            Press Ctrl+S to save changes
          </div>
        )}

        {/* Settings Sections */}
        <div className="space-y-4 sm:space-y-6">
          {/* Window Size Section */}
          <div className="border-b border-gray-200 pb-4 sm:pb-6">
            <h3 className="text-sm font-medium text-gray-700 mb-3">Window Size</h3>

            {/* Current Size Display with Reset Button */}
            <div className="bg-gray-50 rounded p-2 mb-3 flex items-center justify-between">
              <div className="text-xs font-mono text-gray-800">{settings.width} × {settings.height}px</div>
              <button
                onClick={resetToDefault}
                className="px-2 py-1 border border-gray-300 text-gray-700 rounded text-xs font-medium hover:bg-gray-50 transition-colors"
                title="Reset to default size (400×600px)"
              >
                Reset
              </button>
            </div>

            {/* Width, Height - responsive layout */}
            <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
              <div className="flex-1">
                <label className="block text-xs text-gray-600 mb-1 sm:hidden">Width</label>
                <input
                  type="number"
                  placeholder="Width"
                  value={customWidth}
                  onChange={(e) => handleWidthChange(e.target.value)}
                  onKeyDown={handleWidthKeyDown}
                  className="w-full px-2 py-1 border border-gray-300 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  min="200"
                  max="1200"
                  title="Press +/- to adjust by 10px, Enter to apply"
                />
              </div>
              <div className="flex-1">
                <label className="block text-xs text-gray-600 mb-1 sm:hidden">Height</label>
                <input
                  type="number"
                  placeholder="Height"
                  value={customHeight}
                  onChange={(e) => handleHeightChange(e.target.value)}
                  onKeyDown={handleHeightKeyDown}
                  className="w-full px-2 py-1 border border-gray-300 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  min="300"
                  max="1000"
                  title="Press +/- to adjust by 10px, Enter to apply"
                />
              </div>
            </div>
          </div>

          {/* Terminology Settings */}
          <div className="border-b border-gray-200 pb-4 sm:pb-6">
            <h3 className="text-sm font-medium text-gray-700 mb-3">🏷️ Interface Labels</h3>

            <div className="space-y-3">
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Agent Name</label>
                  <input
                    type="text"
                    value={currentTerminology.agentName}
                    onChange={(e) => {
                      const newTerminology = { ...currentTerminology, agentName: e.target.value }
                      setCurrentTerminology(newTerminology)
                      trackChange('terminology', { agentName: e.target.value })
                    }}
                    placeholder="Agent"
                    className="w-full px-2 py-1 border border-gray-300 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">Name for AI responses (formerly "AI Assistant")</p>
                </div>
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Executor Name</label>
                  <input
                    type="text"
                    value={currentTerminology.executorName}
                    onChange={(e) => {
                      const newTerminology = { ...currentTerminology, executorName: e.target.value }
                      setCurrentTerminology(newTerminology)
                      trackChange('terminology', { executorName: e.target.value })
                    }}
                    placeholder="Executor"
                    className="w-full px-2 py-1 border border-gray-300 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">Name for system commands (formerly "Bot")</p>
                </div>
              </div>
            </div>
          </div>

          {/* Code Block Settings */}
          <div className="border-b border-gray-200 pb-4 sm:pb-6">
            <h3 className="text-sm font-medium text-gray-700 mb-3">📝 Code Block Display</h3>

            <div className="space-y-3">
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Max Height (px)</label>
                  <input
                    type="number"
                    min="100"
                    max="800"
                    step="50"
                    value={currentCodeBlockSettings.maxHeight}
                    onChange={(e) => {
                      const maxHeight = parseInt(e.target.value) || 300
                      const newSettings = { ...currentCodeBlockSettings, maxHeight }
                      setCurrentCodeBlockSettings(newSettings)
                      trackChange('codeBlockSettings', { maxHeight })
                    }}
                    className="w-full px-2 py-1 border border-gray-300 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">Maximum height for code blocks</p>
                </div>
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Virtualization Threshold</label>
                  <input
                    type="number"
                    min="10"
                    max="200"
                    step="10"
                    value={currentCodeBlockSettings.virtualizationThreshold}
                    onChange={(e) => {
                      const virtualizationThreshold = parseInt(e.target.value) || 50
                      const newSettings = { ...currentCodeBlockSettings, virtualizationThreshold }
                      setCurrentCodeBlockSettings(newSettings)
                      trackChange('codeBlockSettings', { virtualizationThreshold })
                    }}
                    className="w-full px-2 py-1 border border-gray-300 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">Lines needed to trigger virtual scrolling</p>
                </div>
              </div>
              <div>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={currentCodeBlockSettings.showLineNumbers}
                    onChange={(e) => {
                      const showLineNumbers = e.target.checked
                      const newSettings = { ...currentCodeBlockSettings, showLineNumbers }
                      setCurrentCodeBlockSettings(newSettings)
                      trackChange('codeBlockSettings', { showLineNumbers })
                    }}
                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                  />
                  <span className="text-xs text-gray-600">Show line numbers by default</span>
                </label>
              </div>
            </div>
          </div>

          {/* AI Vendor Selection */}
          <div className="border-b border-gray-200 pb-4 sm:pb-6">
            <h3 className="text-sm sm:text-base font-medium text-gray-700 mb-3 sm:mb-4">🤖 Agent Provider</h3>

            <div className="space-y-3">
              <div>
                <label className="block text-xs text-gray-600 mb-1">Select Agent Provider</label>
                <select
                  value={currentVendor}
                  onChange={async (e) => {
                    const newVendor = e.target.value as AIVendor
                    // Save vendor immediately for real-time synchronization
                    try {
                      await vendorStorage.saveVendor(newVendor)
                      console.log('✅ [Settings] Vendor changed to:', newVendor)
                    } catch (error) {
                      console.error('❌ [Settings] Failed to save vendor:', error)
                      // Still track the change for the save button
                      trackChange('vendor', newVendor)
                    }
                  }}
                  disabled={vendorStorage.loadingVendor}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed"
                >
                  <option value="openrouter">OpenRouter</option>
                  <option value="deepseek">DeepSeek</option>
                </select>
              </div>
            </div>
          </div>

          {/* OpenRouter AI Settings */}
          {currentVendor === 'openrouter' && (
            <div className="border-b border-gray-200 pb-4 sm:pb-6">
              <h3 className="text-sm sm:text-base font-medium text-gray-700 mb-3 sm:mb-4">🔗 OpenRouter Configuration</h3>

            <div className="space-y-3">
              {/* API Key */}
              <div>
                <label className="block text-xs text-gray-600 mb-1">API Key</label>
                <input
                  type={openRouterApiKeyMasked ? "text" : "password"}
                  placeholder="sk-or-..."
                  value={openRouterApiKey}
                  onFocus={handleOpenRouterApiKeyFocus}
                  onChange={(e) => handleOpenRouterApiKeyChange(e.target.value)}
                  onBlur={handleOpenRouterApiKeyBlur}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Model Selection */}
              <div>
                <label className="block text-xs text-gray-600 mb-1">Model ID</label>
                <ModelAutocomplete
                  value={openRouterStorage.config.model}
                  onChange={async (value) => {
                    const trimmedValue = value.trim()
                    // Save model immediately for real-time synchronization
                    try {
                      await openRouterStorage.updateConfig({ model: trimmedValue })
                      console.log('✅ [Settings] OpenRouter model changed to:', trimmedValue)
                    } catch (error) {
                      console.error('❌ [Settings] Failed to save OpenRouter model:', error)
                      // Still track the change for the save button
                      trackChange('openRouter', { model: trimmedValue })
                    }
                  }}
                  vendor="openrouter"
                  placeholder="e.g., openai/gpt-4o-mini, anthropic/claude-3.5-sonnet"
                  isUpdating={isModelUpdating}
                />
              </div>

              {/* Temperature */}
              <div className="flex space-x-3">
                <div className="flex-1">
                  <label className="block text-xs text-gray-600 mb-1">Temperature</label>
                  <input
                    type="number"
                    min="0"
                    max="2"
                    step="0.1"
                    value={openRouterTemperature}
                    onChange={(e) => {
                      setOpenRouterTemperature(e.target.value)
                      trackChange('openRouter', { temperature: parseFloat(e.target.value) || 0.7 })
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div className="flex-1">
                  <label className="block text-xs text-gray-600 mb-1">Max Tokens</label>
                  <input
                    type="number"
                    min="1"
                    max="4000"
                    value={openRouterMaxTokens}
                    onChange={(e) => {
                      setOpenRouterMaxTokens(e.target.value)
                      trackChange('openRouter', { maxTokens: parseInt(e.target.value) || 1000 })
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              {/* System Prompt */}
              <div>
                <label className="block text-xs text-gray-600 mb-1">System Prompt</label>
                <textarea
                  value={openRouterSystemPrompt}
                  onChange={(e) => {
                    setOpenRouterSystemPrompt(e.target.value)
                    trackChange('openRouter', { systemPrompt: e.target.value })
                  }}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="You are a helpful AI assistant..."
                />
              </div>
            </div>
          </div>
          )}

          {/* DeepSeek AI Settings */}
          {currentVendor === 'deepseek' && (
            <div className="border-b border-gray-200 pb-4 sm:pb-6">
              <h3 className="text-sm sm:text-base font-medium text-gray-700 mb-3 sm:mb-4">🧠 DeepSeek Configuration</h3>

            <div className="space-y-3">
              {/* API Key */}
              <div>
                <label className="block text-xs text-gray-600 mb-1">API Key</label>
                <input
                  type={deepSeekApiKeyMasked ? "text" : "password"}
                  placeholder="sk-..."
                  value={deepSeekApiKey}
                  onFocus={handleDeepSeekApiKeyFocus}
                  onChange={(e) => handleDeepSeekApiKeyChange(e.target.value)}
                  onBlur={handleDeepSeekApiKeyBlur}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Model Selection */}
              <div>
                <label className="block text-xs text-gray-600 mb-1">Model</label>
                <ModelAutocomplete
                  value={deepSeekStorage.config.model}
                  onChange={async (value) => {
                    const trimmedValue = value.trim()
                    // Save model immediately for real-time synchronization
                    try {
                      await deepSeekStorage.updateConfig({ model: trimmedValue })
                      console.log('✅ [Settings] DeepSeek model changed to:', trimmedValue)
                    } catch (error) {
                      console.error('❌ [Settings] Failed to save DeepSeek model:', error)
                      // Still track the change for the save button
                      trackChange('deepSeek', { model: trimmedValue })
                    }
                  }}
                  vendor="deepseek"
                  placeholder="e.g., deepseek-chat, deepseek-coder, deepseek-reasoner"
                  isUpdating={isModelUpdating}
                />
              </div>

              {/* Temperature */}
              <div className="flex space-x-3">
                <div className="flex-1">
                  <label className="block text-xs text-gray-600 mb-1">Temperature</label>
                  <input
                    type="number"
                    min="0"
                    max="2"
                    step="0.1"
                    value={deepSeekTemperature}
                    onChange={(e) => {
                      setDeepSeekTemperature(e.target.value)
                      trackChange('deepSeek', { temperature: parseFloat(e.target.value) || 0.7 })
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div className="flex-1">
                  <label className="block text-xs text-gray-600 mb-1">Max Tokens</label>
                  <input
                    type="number"
                    min="1"
                    max="4000"
                    value={deepSeekMaxTokens}
                    onChange={(e) => {
                      setDeepSeekMaxTokens(e.target.value)
                      trackChange('deepSeek', { maxTokens: parseInt(e.target.value) || 1000 })
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              {/* System Prompt */}
              <div>
                <label className="block text-xs text-gray-600 mb-1">System Prompt</label>
                <textarea
                  value={deepSeekSystemPrompt}
                  onChange={(e) => {
                    setDeepSeekSystemPrompt(e.target.value)
                    trackChange('deepSeek', { systemPrompt: e.target.value })
                  }}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="You are a helpful AI assistant..."
                />
              </div>
            </div>
          </div>
          )}

          {/* Future Settings Placeholder */}
          <div className="border-b border-gray-200 pb-4 sm:pb-6">
            <h3 className="text-sm sm:text-base font-medium text-gray-700 mb-2 sm:mb-3">Connection</h3>
            <div className="text-xs sm:text-sm text-gray-500 italic">Zenoh settings coming soon...</div>
          </div>

          <div>
            <h3 className="text-sm sm:text-base font-medium text-gray-700 mb-2 sm:mb-3">About</h3>
            <div className="text-xs sm:text-sm text-gray-500 leading-relaxed">
              FigmaAgent v1.0.0<br/>
              Built with React & Zenoh
            </div>
          </div>
        </div>


      </div>
    </div>
  )
}
