// Model Autocomplete Component
// Provides autocomplete functionality for AI model selection (OpenRouter and DeepSeek)

import React, { useState, useEffect, useRef, useCallback } from 'react'
import { loadModels, searchModels, formatModelForDisplay, highlightMatch } from '../../lib/openrouter/models'
import { OpenRouterModelData, ModelSuggestion } from '../../lib/openrouter/types'
import { AIVendor, DEEPSEEK_MODELS } from '../types/ui_types'

interface ModelAutocompleteProps {
  value: string
  onChange: (value: string) => void
  vendor: AIVendor
  placeholder?: string
  className?: string
  disabled?: boolean
  isUpdating?: boolean
}

// Helper function to create DeepSeek model suggestions
const createDeepSeekSuggestions = (searchValue: string): ModelSuggestion[] => {
  return DEEPSEEK_MODELS
    .filter(model =>
      model.toLowerCase().includes(searchValue.toLowerCase()) ||
      searchValue.toLowerCase().includes(model.toLowerCase())
    )
    .map(model => ({
      id: model,
      name: model.charAt(0).toUpperCase() + model.slice(1).replace('-', ' '),
      description: `DeepSeek ${model.split('-').pop()} model`,
      pricing: { prompt: 0, completion: 0 }, // DeepSeek models are typically free or very low cost
      contextLength: 128000,
      matchScore: searchValue.toLowerCase().includes(model.toLowerCase()) ? 1 : 0.5
    }))
    .sort((a, b) => b.matchScore - a.matchScore)
}

export const ModelAutocomplete: React.FC<ModelAutocompleteProps> = ({
  value,
  onChange,
  vendor,
  placeholder = "Model: gpt-4o-mini, claude-3.5-sonnet...",
  className = "",
  disabled = false,
  isUpdating = false
}) => {
  const [models, setModels] = useState<OpenRouterModelData[]>([])
  const [suggestions, setSuggestions] = useState<ModelSuggestion[]>([])
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [isLoading, setIsLoading] = useState(false)

  const inputRef = useRef<HTMLInputElement>(null)
  const suggestionsRef = useRef<HTMLDivElement>(null)

  // Load models when vendor changes
  useEffect(() => {
    const loadModelData = async () => {
      if (vendor === 'deepseek') {
        // For DeepSeek, we use the static model list
        setModels([])
        setIsLoading(false)
        return
      }

      // For OpenRouter, load from the models file
      setIsLoading(true)
      try {
        const modelData = await loadModels()
        setModels(modelData)
      } catch (error) {
        console.error('Failed to load OpenRouter models:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadModelData()
  }, [vendor])

  // Update suggestions when value or vendor changes
  useEffect(() => {
    if (vendor === 'deepseek') {
      const deepSeekSuggestions = createDeepSeekSuggestions(value)
      setSuggestions(deepSeekSuggestions)
      setSelectedIndex(0)
      return
    }

    if (models.length === 0) return

    const newSuggestions = searchModels(models, value)
    setSuggestions(newSuggestions)
    setSelectedIndex(0)
  }, [value, models, vendor])

  // Handle input change
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    onChange(newValue)
    setShowSuggestions(true)
  }, [onChange])

  // Handle input focus
  const handleInputFocus = useCallback(() => {
    if (suggestions.length > 0) {
      setShowSuggestions(true)
    }
  }, [suggestions.length])

  // Handle input blur
  const handleInputBlur = useCallback(() => {
    // Delay hiding suggestions to allow for clicks
    setTimeout(() => {
      setShowSuggestions(false)
    }, 150)
  }, [])

  // Handle suggestion selection
  const handleSuggestionSelect = useCallback((suggestion: ModelSuggestion) => {
    onChange(suggestion.id)
    setShowSuggestions(false)
    inputRef.current?.focus()
  }, [onChange])

  // Handle keyboard navigation
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    // Only handle navigation keys when suggestions are showing
    // Allow all other keys (including Backspace, Delete, etc.) to pass through normally
    if (!showSuggestions || suggestions.length === 0) return

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setSelectedIndex(prev => (prev + 1) % suggestions.length)
        break
      case 'ArrowUp':
        e.preventDefault()
        setSelectedIndex(prev => (prev - 1 + suggestions.length) % suggestions.length)
        break
      case 'Enter':
        e.preventDefault()
        if (suggestions[selectedIndex]) {
          handleSuggestionSelect(suggestions[selectedIndex])
        }
        break
      case 'Escape':
        e.preventDefault()
        setShowSuggestions(false)
        break
      // Allow all other keys (Backspace, Delete, typing, etc.) to pass through
      default:
        // Don't prevent default for other keys - let them work normally
        break
    }
  }, [showSuggestions, suggestions, selectedIndex, handleSuggestionSelect])

  // Scroll selected suggestion into view
  useEffect(() => {
    if (showSuggestions && suggestionsRef.current) {
      const selectedElement = suggestionsRef.current.children[selectedIndex] as HTMLElement
      if (selectedElement) {
        selectedElement.scrollIntoView({
          block: 'nearest',
          behavior: 'smooth'
        })
      }
    }
  }, [selectedIndex, showSuggestions])

  return (
    <div className="relative flex-1">
      <input
        ref={inputRef}
        type="text"
        value={value}
        onChange={handleInputChange}
        onFocus={handleInputFocus}
        onBlur={handleInputBlur}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        disabled={disabled}
        className={`w-full px-2 py-1 text-xs border rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white ${
          isUpdating ? 'border-blue-300' : 'border-gray-300'
        } ${disabled ? 'opacity-50 cursor-not-allowed' : ''} ${className}`}
      />

      {/* Loading/Updating indicator */}
      {(isLoading || isUpdating) && (
        <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
          <div className="w-3 h-3 border border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}

      {/* Suggestions dropdown - positioned above input */}
      {showSuggestions && suggestions.length > 0 && !disabled && (
        <div
          ref={suggestionsRef}
          className="absolute bottom-full left-0 right-0 mb-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-64 overflow-y-auto"
        >
          {suggestions.map((suggestion, index) => (
            <div
              key={suggestion.id}
              className={`px-2 py-1.5 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors ${
                index === selectedIndex ? 'bg-blue-50 border-blue-200' : 'hover:bg-gray-50'
              }`}
              onMouseDown={(e) => {
                e.preventDefault()
                handleSuggestionSelect(suggestion)
              }}
              onMouseEnter={() => setSelectedIndex(index)}
            >
              <div className={`font-medium text-xs ${
                index === selectedIndex ? 'text-blue-600' : 'text-gray-800'
              }`}>
                <span
                  dangerouslySetInnerHTML={{
                    __html: highlightMatch(suggestion.id, value)
                  }}
                />
              </div>
              <div className="text-xs text-gray-500 mt-1">
                <span
                  dangerouslySetInnerHTML={{
                    __html: highlightMatch(suggestion.name, value)
                  }}
                />
                {suggestion.pricing.prompt > 0 && (
                  <span className="ml-2">
                    ${suggestion.pricing.prompt.toFixed(6)}/${suggestion.pricing.completion.toFixed(6)}
                  </span>
                )}
                {suggestion.pricing.prompt === 0 && (
                  <span className="ml-2 text-green-600 font-medium">Free</span>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
