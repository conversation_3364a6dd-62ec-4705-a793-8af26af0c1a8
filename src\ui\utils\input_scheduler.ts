// Input-specific scheduler for prioritizing user interactions

import { unstable_scheduleCallback, unstable_cancelCallback, unstable_ImmediatePriority, unstable_UserBlockingPriority, unstable_NormalPriority, unstable_LowPriority } from 'scheduler'

// Priority levels for different types of operations
export enum SchedulerPriority {
  IMMEDIATE = 'immediate',
  USER_BLOCKING = 'user-blocking', 
  NORMAL = 'normal',
  LOW = 'low'
}

// Map our priority enum to React's scheduler priorities
const priorityMap = {
  [SchedulerPriority.IMMEDIATE]: unstable_ImmediatePriority,
  [SchedulerPriority.USER_BLOCKING]: unstable_UserBlockingPriority,
  [SchedulerPriority.NORMAL]: unstable_NormalPriority,
  [SchedulerPriority.LOW]: unstable_LowPriority,
}

// Task interface for scheduled operations
interface ScheduledTask {
  id: string
  callback: () => void
  priority: SchedulerPriority
  schedulerTaskId?: any
}

// Input scheduler class for managing task priorities
class InputScheduler {
  private tasks = new Map<string, ScheduledTask>()
  private taskIdCounter = 0

  // Schedule a task with specific priority
  scheduleTask(
    callback: () => void,
    priority: SchedulerPriority = SchedulerPriority.NORMAL,
    taskId?: string
  ): string {
    const id = taskId || `task-${++this.taskIdCounter}`
    
    // Cancel existing task with same ID if it exists
    this.cancelTask(id)

    const task: ScheduledTask = {
      id,
      callback,
      priority
    }

    // Schedule with React's scheduler
    const schedulerTaskId = unstable_scheduleCallback(
      priorityMap[priority],
      () => {
        // Remove task from our map when it executes
        this.tasks.delete(id)
        callback()
      }
    )

    task.schedulerTaskId = schedulerTaskId
    this.tasks.set(id, task)

    return id
  }

  // Cancel a scheduled task
  cancelTask(taskId: string): boolean {
    const task = this.tasks.get(taskId)
    if (task && task.schedulerTaskId) {
      unstable_cancelCallback(task.schedulerTaskId)
      this.tasks.delete(taskId)
      return true
    }
    return false
  }

  // Schedule input-related tasks with highest priority
  scheduleInputTask(callback: () => void, taskId?: string): string {
    return this.scheduleTask(callback, SchedulerPriority.IMMEDIATE, taskId)
  }

  // Schedule user interaction tasks (clicks, selections, etc.)
  scheduleUserTask(callback: () => void, taskId?: string): string {
    return this.scheduleTask(callback, SchedulerPriority.USER_BLOCKING, taskId)
  }

  // Schedule rendering updates with normal priority
  scheduleRenderTask(callback: () => void, taskId?: string): string {
    return this.scheduleTask(callback, SchedulerPriority.NORMAL, taskId)
  }

  // Schedule background tasks with low priority
  scheduleBackgroundTask(callback: () => void, taskId?: string): string {
    return this.scheduleTask(callback, SchedulerPriority.LOW, taskId)
  }

  // Cancel all tasks
  cancelAllTasks(): void {
    for (const task of this.tasks.values()) {
      if (task.schedulerTaskId) {
        unstable_cancelCallback(task.schedulerTaskId)
      }
    }
    this.tasks.clear()
  }

  // Get number of pending tasks
  getPendingTaskCount(): number {
    return this.tasks.size
  }

  // Get pending tasks by priority
  getTasksByPriority(priority: SchedulerPriority): ScheduledTask[] {
    return Array.from(this.tasks.values()).filter(task => task.priority === priority)
  }
}

// Global scheduler instance
const globalInputScheduler = new InputScheduler()

// Convenience functions for common scheduling patterns
export const scheduleInputTask = (callback: () => void, taskId?: string) => 
  globalInputScheduler.scheduleInputTask(callback, taskId)

export const scheduleUserTask = (callback: () => void, taskId?: string) => 
  globalInputScheduler.scheduleUserTask(callback, taskId)

export const scheduleRenderTask = (callback: () => void, taskId?: string) => 
  globalInputScheduler.scheduleRenderTask(callback, taskId)

export const scheduleBackgroundTask = (callback: () => void, taskId?: string) => 
  globalInputScheduler.scheduleBackgroundTask(callback, taskId)

export const cancelScheduledTask = (taskId: string) => 
  globalInputScheduler.cancelTask(taskId)

export const cancelAllScheduledTasks = () => 
  globalInputScheduler.cancelAllTasks()

// Hook for using the scheduler in React components
export const useInputScheduler = () => {
  return {
    scheduleInputTask: globalInputScheduler.scheduleInputTask.bind(globalInputScheduler),
    scheduleUserTask: globalInputScheduler.scheduleUserTask.bind(globalInputScheduler),
    scheduleRenderTask: globalInputScheduler.scheduleRenderTask.bind(globalInputScheduler),
    scheduleBackgroundTask: globalInputScheduler.scheduleBackgroundTask.bind(globalInputScheduler),
    cancelTask: globalInputScheduler.cancelTask.bind(globalInputScheduler),
    cancelAllTasks: globalInputScheduler.cancelAllTasks.bind(globalInputScheduler),
    getPendingTaskCount: globalInputScheduler.getPendingTaskCount.bind(globalInputScheduler),
    getTasksByPriority: globalInputScheduler.getTasksByPriority.bind(globalInputScheduler),
  }
}

// Utility for deferring non-critical operations
export const deferNonCritical = (callback: () => void, delay = 0) => {
  if (delay > 0) {
    setTimeout(() => scheduleBackgroundTask(callback), delay)
  } else {
    scheduleBackgroundTask(callback)
  }
}

// Utility for batching multiple operations
export const batchOperations = (operations: Array<() => void>, priority: SchedulerPriority = SchedulerPriority.NORMAL) => {
  const batchedCallback = () => {
    operations.forEach(op => op())
  }
  
  globalInputScheduler.scheduleTask(batchedCallback, priority)
}

export { InputScheduler, globalInputScheduler }
export default globalInputScheduler
